﻿using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Service.Services
{
    public class FcmPushNotificationService
    {
        private ILogger<FcmPushNotificationService> logger;
        public FcmPushNotificationService(ILogger<FcmPushNotificationService> logger)
        {
            this.logger = logger;
        }

        private async Task<string> GetAccessTokenAsync()
        {
            try
            {
                // Use a path relative to the application or from configuration
                string credentialPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "firebase-admin-key.json");

                // Check if the file exists
                if (!File.Exists(credentialPath))
                {
                    logger.LogError($"File 'firebase-admin-key.json' not found at: {credentialPath}");

                    throw new FileNotFoundException($"Firebase credentials file not found at: {credentialPath}/firebase-admin-key.json");
                }

                try
                {
                    // Read the file content to log in case of issues
                    string fileContent = await File.ReadAllTextAsync(credentialPath);

                    // Check if the file contains valid JSON with required fields
                    try
                    {
                        Dictionary<string, object> jsonObj = JsonConvert.DeserializeObject<Dictionary<string, object>>(fileContent);

                        // Check if the required fields exist
                        if (!jsonObj!.ContainsKey("client_email") || !jsonObj.ContainsKey("private_key"))
                        {
                            logger.LogError("Firebase credentials file is missing required fields (client_email or private_key)");
                            return string.Empty;
                        }

                        // Validate the project ID matches what you're expecting
                        if (jsonObj.ContainsKey("project_id") && !jsonObj["project_id"].ToString().Contains("fluentblue"))
                        {
                            logger.LogWarning($"Firebase project ID '{jsonObj["project_id"]}' may not match your expected project");
                        }
                    }
                    catch (JsonException)
                    {
                        logger.LogError("Firebase credentials file contains invalid JSON");
                        return string.Empty;
                    }

                    // Load the credentials and create a scoped credential
                    GoogleCredential credential = GoogleCredential.FromFile(credentialPath).CreateScoped(new[] { "https://www.googleapis.com/auth/firebase.messaging" });

                    // Get access token for Firebase Cloud Messaging
                    string token = await credential.UnderlyingCredential.GetAccessTokenForRequestAsync();
                    return token;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error loading Google credentials from file or getting access token");

                    // Log specific error information to help diagnose the issue
                    if (ex.Message.Contains("invalid_grant"))
                    {
                        logger.LogError("The service account credentials are invalid or the account doesn't exist. This means:" +
                                       "\n1. The service account may have been deleted in Firebase console" +
                                       "\n2. The private key might be malformed or corrupted" +
                                       "\n3. The project ID may not match an actual Firebase project");

                        logger.LogError("To fix this issue, create a new service account key in the Firebase Console:");
                        logger.LogError("1. Go to Firebase Console > Project settings > Service accounts");
                        logger.LogError("2. Click 'Generate new private key'");
                        logger.LogError("3. Save the JSON file as 'firebase-credentials.json' in the application directory");
                    }

                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in GetAccessToken method");
                return string.Empty;
            }
        }

        private async Task<string> GetFirebaseProjectIdAsync()
        {
            try
            {
                // Use the same path used for getting the access token
                string credentialPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "firebase-admin-key.json");

                // Check if the file exists
                if (!File.Exists(credentialPath))
                {
                    logger.LogError($"File 'firebase-admin-key.json' not found at: {credentialPath}");
                    return string.Empty;
                }

                // Read the file content
                string fileContent = await File.ReadAllTextAsync(credentialPath);

                try
                {
                    var jsonObj = JsonConvert.DeserializeObject<Dictionary<string, object>>(fileContent);

                    // Get the project_id from the JSON file
                    if (jsonObj.TryGetValue("project_id", out object projectIdObj) && projectIdObj != null)
                    {
                        string projectId = projectIdObj.ToString();
                        logger.LogInformation($"Using Firebase project ID: {projectId}");
                        return projectId;
                    }
                    else
                    {
                        logger.LogError("Firebase credentials file is missing the project_id field");
                        return string.Empty;
                    }
                }
                catch (JsonException ex)
                {
                    logger.LogError(ex, "Firebase credentials file contains invalid JSON");
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error reading Firebase project ID from credentials file");
                return string.Empty;
            }
        }

        public async Task SendPushNotificationAsync(string deviceToken, string title, string body, Dictionary<string, string> data)
        {
            try
            {
                // Get project ID dynamically from the credentials file
                string projectId = await GetFirebaseProjectIdAsync();

                // If we couldn't get a project ID, don't proceed
                if (string.IsNullOrEmpty(projectId))
                {
                    logger.LogWarning("Skipping push notification send due to missing project ID");
                    return;
                }

                // FCM HTTP v1 API endpoint with dynamically retrieved project ID
                string fcmUrl = $"https://fcm.googleapis.com/v1/projects/{projectId}/messages:send";
                
                // Validate device token - at least check if it's not empty
                if (string.IsNullOrEmpty(deviceToken))
                {
                    logger.LogWarning("Device token is empty. Cannot send push notification.");
                    return;
                }

                // Create the FCM HTTP v1 message payload - proper structure for the v1 API
                var messagePayload = new
                {
                    message = new
                    {
                        token = deviceToken,
                        notification = new
                        {
                            title = title,
                            body = body
                        },
                        data = data ?? new Dictionary<string, string>(),
                        android = new
                        {
                            priority = "high",
                            notification = new
                            {
                                click_action = "FLUTTER_NOTIFICATION_CLICK",
                                channel_id = "TestChannel"
                            }
                        },
                        apns = new
                        {
                            headers = new Dictionary<string, string>
                            {
                                { "apns-priority", "10" }
                            },
                            payload = new
                            {
                                aps = new
                                {
                                    alert = new
                                    {
                                        title = title,
                                        body = body
                                    },
                                    badge = 1,
                                    sound = "default"
                                }
                            }
                        }
                    }
                };

                // Get OAuth 2.0 token for authorization
                string accessToken = await GetAccessTokenAsync();

                // If we couldn't get an access token, don't proceed
                if (string.IsNullOrEmpty(accessToken))
                {
                    logger.LogWarning("Skipping push notification send due to missing access token");
                    return;
                }

                using (var httpClient = new HttpClient())
                {
                    // Set authorization header with OAuth 2.0 access token
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    string payload = JsonConvert.SerializeObject(messagePayload);
                    logger.LogInformation($"FCM v1 API Request: {payload}");

                    var response = await httpClient.PostAsync(fcmUrl, new StringContent(payload, Encoding.UTF8, "application/json"));

                    string responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        logger.LogInformation($"FCM v1 API Success: {responseContent}");
                    }
                    else
                    {
                        logger.LogError($"FCM v1 API Error: {response.StatusCode}, Details: {responseContent}");

                        // If the error is authentication related, provide guidance
                        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                        {
                            logger.LogError("Authentication failed. Make sure to provide a valid OAuth 2.0 access token. " +
                                "For FCM v1 API, you need a service account and proper OAuth 2.0 authentication.");
                        }

                        // If the error is about an invalid token
                        if (responseContent.Contains("INVALID_ARGUMENT") && responseContent.Contains("token"))
                        {
                            logger.LogError("The FCM token is invalid or has expired. " +
                                           "This could be because the device has uninstalled the app or " +
                                           "the token was generated for a different Firebase project.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error sending push notification: {ex.Message}");
            }
        }

        //// Method to send Web Push notifications using VAPID
        //public async Task SendWebPushNotificationAsync(string endpoint, string p256dh, string auth, string message)
        //{
        //    try
        //    {
        //        var audience = new Uri(endpoint).Scheme + "://" + new Uri(endpoint).Host;

        //        using (var client = new HttpClient())
        //        {
        //            // Add VAPID authentication headers
        //            client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization",
        //                GenerateVapidAuthorizationHeader(audience, "mailto:<EMAIL>"));

        //            // Encrypt the message using the user's keys (p256dh and auth)
        //            // Note: In a real implementation, you'd use a library like WebPush to handle encryption
        //            // This is a placeholder for the actual encryption logic

        //            // Send the push message
        //            var response = await client.PostAsync(endpoint, new StringContent(message));

        //            if (!response.IsSuccessStatusCode)
        //            {
        //                string responseContent = await response.Content.ReadAsStringAsync();
        //                logger.LogError($"Web Push Error: {response.StatusCode}, Details: {responseContent}");
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        logger.LogError(ex, $"Error sending web push notification: {ex.Message}");
        //    }
        //}


    }
}
