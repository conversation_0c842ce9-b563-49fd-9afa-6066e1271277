﻿using Microsoft.AspNetCore.SignalR;

namespace FluentBlue.WebApi.Service.Hubs
{
    public class NotificationHub : Hub
    {
        public async Task JoinGroup(string groupId)
        {
            if (string.IsNullOrWhiteSpace(groupId))
                throw new HubException("TenantId is required");

            await Groups.AddToGroupAsync(Context.ConnectionId, groupId);
            Console.WriteLine($"Client {Context.ConnectionId} joined group {groupId}");
        }

        public async Task LeaveGroup(string groupId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupId);
            Console.WriteLine($"Client {Context.ConnectionId} left group {groupId}");
        }


        public override async Task OnConnectedAsync()
        {
            await base.OnConnectedAsync();
            Console.WriteLine($"Client connected: {Context.ConnectionId}");
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            Console.WriteLine($"Client disconnected: {Context.ConnectionId}");
            await base.OnDisconnectedAsync(exception);
        }
    }
}
