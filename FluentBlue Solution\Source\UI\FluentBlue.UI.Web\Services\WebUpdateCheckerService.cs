﻿using FluentBlue.UI.Main;
using FluentBlue.UI.Main.Services;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Configuration;
using Microsoft.JSInterop;
using System;
using System.Net.Http;
using System.Text.Json;
using System.Timers;
using Timer = System.Timers.Timer;

namespace FluentBlue.UI.Web.Services
{
    /// <summary>
    /// The purpose of this service is to automatically check for updates in web part of application, and refresh the webpage, also notify the user during the update.
    /// </summary>
    public class WebUpdateCheckerService : IWebUpdateCheckerService
    {
        private readonly HttpClient httpClient = default!;
        private readonly IJSRuntime jsRuntime = default!;
        private readonly Timer timer = default!;
        private readonly NavigationManager navigationManager = default!;
        private readonly ILogger<WebUpdateCheckerService> logger = default!;
        private readonly HybridCache cache = default!;

        private readonly string webAppUrl = default!;
        private string currentGuid = string.Empty;

        public event Func<string, Task> OnUpdateDetected = _ => Task.CompletedTask;

        public WebUpdateCheckerService(IJSRuntime jsRuntime, IConfiguration configuration, ILogger<WebUpdateCheckerService> logger, HybridCache cache, NavigationManager navigationManager)
        {
            try
            {
                this.httpClient = new HttpClient();
                this.webAppUrl = configuration.GetValue<string>("WebApp:BaseUrl") ?? throw new ArgumentNullException(nameof(webAppUrl), "WebApp:BaseUrl configuration is missing.");
                this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
                this.cache = cache ?? throw new ArgumentNullException(nameof(cache));
                this.httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
                this.httpClient.BaseAddress = new Uri(webAppUrl);
                this.jsRuntime = jsRuntime ?? throw new ArgumentNullException(nameof(jsRuntime));
                this.navigationManager = navigationManager ?? throw new ArgumentNullException(nameof(navigationManager));

                // Initialize non-nullable fields
                //OnUpdateDetected = (version) => Task.CompletedTask;

                // Check for updates every 5 minutes
                timer = new Timer(1 * 60 * 1000) { AutoReset = true };
                timer.Elapsed += async (sender, args) => await CheckForUpdatesAsync();
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, ex.Message);
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                // Get current Guid on startup
                await CheckForUpdatesAsync();

                // Start periodic checking
                timer.Start();

                // Set up visibility change listener
                await jsRuntime.InvokeVoidAsync("webUpdateChecker.initialize", DotNetObjectReference.Create(this));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
            }
        }

        public async Task CheckForUpdatesAsync()
        {
            try
            {
                // Reads the deployment-guid using a cache-busting query parameter
                var requestUri = $"{webAppUrl}deployment-guid.json?t={DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
                var response = await httpClient.GetAsync(requestUri);
                response.EnsureSuccessStatusCode();
                string newGuid = await response.Content.ReadAsStringAsync();

                if (!string.IsNullOrWhiteSpace(currentGuid) && currentGuid != newGuid)
                {
                    if (OnUpdateDetected != null)
                    {
                        await OnUpdateDetected.Invoke(newGuid);
                    }
                }

                if (string.IsNullOrEmpty(newGuid) == false)
                {
                    this.currentGuid = newGuid;  // Stores the new deploymentGuid to hybridCache.
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                Console.Error.WriteLine($"Failed to check for updates in web app: {ex.Message}");
            }
        }

        [JSInvokable]
        public async Task NotifyVisibilityChange(bool isVisible)
        {
            try
            {
                if (isVisible)
                {
                    await CheckForUpdatesAsync();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                Console.Error.WriteLine($"Failed to check for updates in web app: {ex.Message}");
            }
        }

        public Task ForceReloadAsync()
        {
            try
            {
                // Prefer navigating to the current URI to force a full reload.
                var target = string.IsNullOrWhiteSpace(webAppUrl) ? navigationManager.Uri : webAppUrl;
                navigationManager.NavigateTo(target, forceLoad: true);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                Console.Error.WriteLine($"Failed to force reload of web app: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        public void Dispose()
        {
            try
            {
                timer?.Stop();
                timer?.Dispose();
            }
            catch
            {
                // no-op
            }
        }
    }
}
