﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Shared.Request
{
    public class RegisterUserDeviceRequest
    {
        public string? DeviceToken { get; set; } = string.Empty;
        public string? Platform { get; set; } = string.Empty; // e.g., "Android", "iOS"

        public RegisterUserDeviceRequest()
        {
        }

        public RegisterUserDeviceRequest(string deviceToken, string platform)
        {
            DeviceToken = deviceToken;
            Platform = platform;
        }
    }
}
