using FluentBlue.Application.Business.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Service.Middleware
{
    public class ApplicationKeyMiddleware
    {
        private readonly RequestDelegate next;
        private readonly ILogger<ApplicationKeyMiddleware> logger;

        public ApplicationKeyMiddleware(
            RequestDelegate next, 
            ILogger<ApplicationKeyMiddleware> logger)
        {
            this.next = next;
            this.logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // Resolve the IApplicationKeyService from the request's service provider
                var applicationKeyService = context.RequestServices.GetRequiredService<IApplicationKeyService>();

                // Get the application key and set it for this request
                var applicationKey = await applicationKeyService.GetApplicationKeyAsync();
                applicationKeyService.SetCurrentApplicationKey(applicationKey);
                
                logger.LogDebug("Set application encryption key for request {RequestPath}", context.Request.Path);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error setting application key for request {RequestPath}", context.Request.Path);
                // Continue with the request even if key setting fails
            }

            await next(context);
        }
    }
} 