﻿CREATE TABLE [Contacts].[Contact] (
    [ContactId]       UNIQUEIDENTIFIER CONSTRAINT [DF_Contact_ContactId1] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]        UNIQUEIDENTIFIER NOT NULL,
    [Type]            TINYINT          NULL,
    [FirstName]       NVARCHAR (500)   CONSTRAINT [DF_Contact_FirstName] DEFAULT (N'Ν('')') NOT NULL,
    [LastName]        NVARCHAR (500)   CONSTRAINT [DF_Contact_LastName] DEFAULT (N'') NOT NULL,
    [MiddleName]      NVARCHAR (500)   CONSTRAINT [DF_Contact_MiddleName] DEFAULT (N'') NOT NULL,
    [Occupation]      NVARCHAR (200)   CONSTRAINT [DF_Contact_Occupation] DEFAULT (N'') NOT NULL,
    [TIN]             NVARCHAR (500)   CONSTRAINT [DF_Contact_TIN] DEFAULT (N'') NOT NULL,
    [SSN]             NVARCHAR (500)   CONSTRAINT [DF_Contact_SSN] DEFAULT (N'') NOT NULL,
    [Vat]             DECIMAL (6, 2)   NOT NULL,
    [Notes]           NVARCHAR (MAX)   CONSTRAINT [DF_Contact_Notes] DEFAULT (N'') NOT NULL,
    [Image]           NVARCHAR (MAX)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Contact_Image] DEFAULT (N'') NOT NULL,
    [DateCreatedUtc]  DATETIME         CONSTRAINT [DF_Contact_DateModified1] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc] DATETIME         CONSTRAINT [DF_Contact_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_Contact] PRIMARY KEY CLUSTERED ([ContactId] ASC)
);












GO
CREATE NONCLUSTERED INDEX [TinIndex]
    ON [Contacts].[Contact]([TIN] ASC);


GO
CREATE NONCLUSTERED INDEX [TenantIdIndex]
    ON [Contacts].[Contact]([TenantId] ASC);


GO
CREATE NONCLUSTERED INDEX [SsnIndex]
    ON [Contacts].[Contact]([SSN] ASC);


GO
CREATE NONCLUSTERED INDEX [MiddleNameIndex]
    ON [Contacts].[Contact]([MiddleName] ASC);


GO
CREATE NONCLUSTERED INDEX [LastNameIndex]
    ON [Contacts].[Contact]([LastName] ASC);


GO
CREATE NONCLUSTERED INDEX [FirstNameIndex]
    ON [Contacts].[Contact]([FirstName] ASC);

