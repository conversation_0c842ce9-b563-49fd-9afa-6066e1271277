CREATE TABLE [Tenants].[PasswordResetToken] (
    [TokenId]        UNIQUEIDENTIFIER CONSTRAINT [DF_PasswordResetToken_TokenId] DEFAULT (newsequentialid()) NOT NULL,
    [UserId]         UNIQUEIDENTIFIER NOT NULL,
    [Token]          NVARCHAR (255)   CONSTRAINT [DF_PasswordResetToken_Token] DEFAULT (N'') NOT NULL,
    [DateCreatedUtc] DATETIME         CONSTRAINT [DF_PasswordResetToken_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [ExpiresUtc]     DATETIME         CONSTRAINT [DF_PasswordResetToken_ExpiresUtc] DEFAULT (dateadd(hour, 24, getutcdate())) NOT NULL,
    [IsUsed]         BIT              CONSTRAINT [DF_PasswordResetToken_IsUsed] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_PasswordResetToken] PRIMARY KEY CLUSTERED ([TokenId] ASC),
    CONSTRAINT [FK_PasswordResetToken_User] FOREIGN KEY ([UserId]) REFERENCES [Tenants].[User] ([UserId]) ON DELETE CASCADE ON UPDATE CASCADE
);

GO
CREATE NONCLUSTERED INDEX [TokenIndex]
    ON [Tenants].[PasswordResetToken]([Token] ASC);

GO
CREATE NONCLUSTERED INDEX [UserIdIndex]
    ON [Tenants].[PasswordResetToken]([UserId] ASC);

GO
CREATE NONCLUSTERED INDEX [ExpiresUtcIndex]
    ON [Tenants].[PasswordResetToken]([ExpiresUtc] ASC);
