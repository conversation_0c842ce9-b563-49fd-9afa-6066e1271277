﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
	<application android:allowBackup="true" android:icon="@mipmap/appicon" android:supportsRtl="true" android:usesCleartextTraffic="true">
    
    <!-- Register Firebase Messaging Service -->
	<service android:name="fluentblue.ui.devices.platforms.android.services.FirebaseService"
			android:exported="true">
		<intent-filter>
			<action android:name="com.google.firebase.MESSAGING_EVENT" />
		</intent-filter>
	</service>

    <!-- Firebase Initialization Provider (required for google-services.json) -->
    <provider android:name="com.google.firebase.provider.FirebaseInitProvider"
              android:authorities="${applicationId}.firebaseinitprovider"
              android:exported="false"
              android:initOrder="100" />

    <!-- (Optional) Metadata for Firebase config -->
    <meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@mipmap/appicon" />
    <meta-data android:name="com.google.firebase.messaging.default_notification_channel_id" android:value="TestChannel" />

	</application>
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.CALL_PHONE" />
	<uses-permission android:name="android.permission.READ_PHONE_STATE" />
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
</manifest>