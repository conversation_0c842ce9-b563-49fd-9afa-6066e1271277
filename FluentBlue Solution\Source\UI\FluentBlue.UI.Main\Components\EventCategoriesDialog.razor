﻿@implements IDialogContentComponent<List<Data.Model.DBOs.Calendar.EventCategory>>

@using Blazored.FluentValidation
@using FluentBlue.Data.Model.DBOs.Calendar
@using FluentBlue.Shared.Utilities
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Caching.Hybrid
@using Microsoft.JSInterop

@inject HttpClient httpClient
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject HybridCache cache
@inject ILogger<FluentBlue.UI.Main.Components.EventCategoriesDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.EventCategoriesWebApiClient> eventCategoriesWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject IFormFactor formFactor
@inject IJSRuntime JS

<FluentDialogHeader Class="hidden"></FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto">
    <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4">@Resources.EventCategoriesDialogResource.Name</FluentLabel>
    <EditForm id="editForm" Model="@Content">
        <ChildContent Context="context2">
            <FluentValidationValidator @ref="fluentValidationValidator" />

            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                <FluentButton IconStart="@(new Icons.Regular.Size16.Add())" Appearance="Appearance.Accent" OnClick="AddBtnOnClick"><span>@Resources.EventCategoriesDialogResource.AddBtn_Text</span></FluentButton>
                @*  <FluentButton IconStart="@(new Icons.Regular.Size16.Save())" OnClick="SaveBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.EventCategoriesDialogResource.SaveBtn_Text</span></FluentButton>
                <FluentButton IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.EventCategoriesDialogResource.CancelBtn_Text</span></FluentButton>*@ 
            </FluentToolbar>

            @if (Content != null && Content.Count > 0)
            {
                <FluentSortableList TItem="EventCategory" Items="@Content" Fallback="true" Handle="true" OnUpdate="HandleReorder" Context="eventCategoryContext">
                    <ItemTemplate>
                        <div class="sortable-grab cursor-move">
                            <FluentIcon Value="@(new Icons.Regular.Size20.ArrowSort())" />
                        </div>
                        <div class="sortable-item-content flex flex-1 gap-2 items-center p-1 overflow-hidden" style="height:31px">
                            <div class="flex-1">
                                <FluentTextField @bind-Value="@eventCategoryContext.Name" Appearance="FluentInputAppearance.Filled" Placeholder="@Resources.EventCategoriesDialogResource.NamePlaceholder" Maxlength="50" AutoComplete="off" Class="w-full" />
                            </div>
                            <FluentIcon Id="@eventCategoryContext.EventCategoryId.ToString()" Value="@(new Icons.Filled.Size28.Square())" Color="Color.Custom" CustomColor="@eventCategoryContext.BackColor" />
                            <FluentMenu Anchor="@eventCategoryContext.EventCategoryId.ToString()" Trigger="MouseButton.Left" VerticalPosition="VerticalPosition.Center" Style="height: 150px !important; overflow-y: scroll !important; scrollbar-width: thin !important;">
                                @foreach (EventCategoryColor eventCategoryColor in this.predefinedColors)
                                {
                                    <FluentMenuItem OnClick="@((e) => {eventCategoryContext.BackColor = eventCategoryColor.Color;})">
                                        <div slot="start" class="flex items-center"><FluentIcon Value="@(new Icons.Filled.Size16.Square())" Color="Color.Custom" CustomColor="@eventCategoryColor.Color" Slot="start" Class="mr-2" />@eventCategoryColor.Name</div>
                                    </FluentMenuItem>
                                }
                            </FluentMenu>

                            <FluentButton IconStart="@(new Icons.Regular.Size16.Delete())" OnClick="() => DeleteBtnOnClick(eventCategoryContext)" BackgroundColor="transparent" Class="flex-none w-10" />
                        </div>
                    </ItemTemplate>
                </FluentSortableList>
            }
        </ChildContent>
    </EditForm>
</FluentDialogBody>

<FluentDialogFooter>
    <FluentStack Orientation="Orientation.Vertical" VerticalAlignment="VerticalAlignment.Bottom">
        <FluentDivider Class="w-full" Role="DividerRole.Presentation"></FluentDivider>
        <FluentStack Orientation="Orientation.Horizontal" HorizontalAlignment="HorizontalAlignment.Right" VerticalAlignment="VerticalAlignment.Bottom" Class="self-end">
            <FluentButton Loading="@this.isSaving" IconStart="@(new Icons.Regular.Size16.Save())" Appearance="Appearance.Accent" OnClick="SaveBtnOnClick"><span>@GlobalResource.Save</span></FluentButton>
            <FluentButton Disabled="@this.isSaving" IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtnOnClick"><span>@GlobalResource.Cancel</span></FluentButton>
        </FluentStack>
    </FluentStack>
</FluentDialogFooter>