﻿using Android.App;
using Android.Runtime;
using System;

namespace FluentBlue.UI.Devices
{
    [Application(UsesCleartextTraffic = true)]
    public class MainApplication : MauiApplication
    {
        public static IServiceProvider Services { get; private set; }

        public MainApplication(IntPtr handle, JniHandleOwnership ownership)
            : base(handle, ownership)
        {
        }

        protected override MauiApp CreateMauiApp()
        {
            var mauiApp = MauiProgram.CreateMauiApp();
            Services = mauiApp.Services;
            return mauiApp;
        }
    }
}
