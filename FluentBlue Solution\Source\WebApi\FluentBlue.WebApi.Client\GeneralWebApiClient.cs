﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace FluentBlue.WebApi.Client
{
    public class GeneralWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<GeneralWebApiClient> logger;

        public GeneralWebApiClient(HttpClient httpClient, ILogger<GeneralWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<SummaryDataDto> GetSummaryData(DateTime todayLocal, string timeZoneId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/General/GetSummaryData?todayLocal=" + DateTime.Today.ToString("yyyy/MM/dd") + "&timeZoneId=" + timeZoneId;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<SummaryDataDto> response = JsonConvert.DeserializeObject<ApiResponse<SummaryDataDto>>(responseString)!;
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return Task.FromResult(new SummaryDataDto()).Result;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetSummaryData({@todayLocal})", todayLocal.ToString("yyyy/MM/dd")+", timeZoneId:"+timeZoneId);
                throw;
            }
        }



      
    }
}
