﻿namespace FluentBlue.UI.Main.Shared
{
    public class EventDialogPreloadedData
    {
        public List<Data.Model.DTOs.UserLI> Users { get; set; } = new();
        public List<Data.Model.DTOs.ContactLI> Contacts { get; set; } = new();
        public List<Data.Model.DBOs.Calendar.EventCategory> EventCategories { get; set; } = new();
        public List<Data.Model.DBOs.Calendar.EventState> EventStates { get; set; } = new();
    }
}