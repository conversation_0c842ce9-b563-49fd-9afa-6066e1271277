﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources.Calendar {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class EventResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EventResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.Calendar.EventResource", typeof(EventResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Day.
        /// </summary>
        public static string AllDay {
            get {
                return ResourceManager.GetString("AllDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Block.
        /// </summary>
        public static string Block {
            get {
                return ResourceManager.GetString("Block", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        public static string ContactId {
            get {
                return ResourceManager.GetString("ContactId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created.
        /// </summary>
        public static string DateCreated {
            get {
                return ResourceManager.GetString("DateCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modified.
        /// </summary>
        public static string DateModified {
            get {
                return ResourceManager.GetString("DateModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Too many characters (2000 characters limit).
        /// </summary>
        public static string DescriptionMaxLengthError {
            get {
                return ResourceManager.GetString("DescriptionMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        public static string EndTime {
            get {
                return ResourceManager.GetString("EndTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End must be after Start..
        /// </summary>
        public static string EndTimeMustBeAfterStartTime {
            get {
                return ResourceManager.GetString("EndTimeMustBeAfterStartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Time Zone.
        /// </summary>
        public static string EndTimeZone {
            get {
                return ResourceManager.GetString("EndTimeZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category.
        /// </summary>
        public static string EventCategoryId {
            get {
                return ResourceManager.GetString("EventCategoryId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Contact name) (Subject).
        /// </summary>
        public static string EventDisplayContactNameSubject {
            get {
                return ResourceManager.GetString("EventDisplayContactNameSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Subject).
        /// </summary>
        public static string EventDisplaySubject {
            get {
                return ResourceManager.GetString("EventDisplaySubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Subject) (Contact name).
        /// </summary>
        public static string EventDisplaySubjectContactName {
            get {
                return ResourceManager.GetString("EventDisplaySubjectContactName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        public static string EventId {
            get {
                return ResourceManager.GetString("EventId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        public static string EventStateId {
            get {
                return ResourceManager.GetString("EventStateId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comma (,).
        /// </summary>
        public static string EventTextSeparatorComma {
            get {
                return ResourceManager.GetString("EventTextSeparatorComma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dash (-).
        /// </summary>
        public static string EventTextSeparatorDash {
            get {
                return ResourceManager.GetString("EventTextSeparatorDash", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pipe (|).
        /// </summary>
        public static string EventTextSeparatorPipe {
            get {
                return ResourceManager.GetString("EventTextSeparatorPipe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Space.
        /// </summary>
        public static string EventTextSeparatorSpace {
            get {
                return ResourceManager.GetString("EventTextSeparatorSpace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        public static string EventUsers {
            get {
                return ResourceManager.GetString("EventUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 15 minutes before.
        /// </summary>
        public static string FifteenMinReminder {
            get {
                return ResourceManager.GetString("FifteenMinReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5 minutes before.
        /// </summary>
        public static string FiveMinReminder {
            get {
                return ResourceManager.GetString("FiveMinReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No reminder.
        /// </summary>
        public static string NoReminder {
            get {
                return ResourceManager.GetString("NoReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 hour before.
        /// </summary>
        public static string OneHourReminder {
            get {
                return ResourceManager.GetString("OneHourReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string PlainDescription {
            get {
                return ResourceManager.GetString("PlainDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read Only.
        /// </summary>
        public static string ReadOnly {
            get {
                return ResourceManager.GetString("ReadOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Time Zone.
        /// </summary>
        public static string StartTimeZone {
            get {
                return ResourceManager.GetString("StartTimeZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subject.
        /// </summary>
        public static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Too many characters (300 characters limit).
        /// </summary>
        public static string SubjectMaxLengthError {
            get {
                return ResourceManager.GetString("SubjectMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        public static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10 minutes before.
        /// </summary>
        public static string TenMinReminder {
            get {
                return ResourceManager.GetString("TenMinReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 30 minutes before.
        /// </summary>
        public static string ThirtyMinReminder {
            get {
                return ResourceManager.GetString("ThirtyMinReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At event time.
        /// </summary>
        public static string ZeroMinReminder {
            get {
                return ResourceManager.GetString("ZeroMinReminder", resourceCulture);
            }
        }
    }
}
