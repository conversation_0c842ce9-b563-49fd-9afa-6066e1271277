﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;

namespace FluentBlue.Data.Model.DBOs.Validators
{
    public class UserValidator : AbstractValidator<User>
    {
        public UserValidator()
        {
            RuleFor(x => x.UserId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.UserId));
            RuleFor(x => x.TenantId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.TenantId));
            RuleFor(x => x.RoleId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.Role));
            RuleFor(x => x.FirstName).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.FirstName));
            RuleFor(x => x.FirstName).MaximumLength(100).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength100);
            RuleFor(x => x.LastName).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.LastName));
            RuleFor(x => x.LastName).MaximumLength(100).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength100);
            RuleFor(x => x.Email).MaximumLength(50).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength50);
            RuleFor(x => x.Email).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.Email));
            RuleFor(x => x.Email).EmailAddress().WithMessage(Data.Model.Resources.GeneralValidationResource.InvalidEmailAddress);
            //RuleFor(x => x.Username).MaximumLength(30).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength30);
            //RuleFor(x => x.Username).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.Username));
            RuleFor(x => x.Password).MaximumLength(30).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength30);
            RuleFor(x => x.Password).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.Password));
            RuleFor(x => x.Phone).MaximumLength(30).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength30);
            RuleFor(x => x.Mobile).MaximumLength(30).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength30);

            RuleFor(x => x.DateCreatedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.DateModifiedUtc));
            RuleFor(x => x.DateModifiedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(User.DateModifiedUtc));

        }

        protected override bool PreValidate(ValidationContext<User> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }

    }
}
