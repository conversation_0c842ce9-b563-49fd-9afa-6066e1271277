@page "/register"
@layout EmptyLayout

@using FluentBlue.Shared
@using FluentBlue.Data.Model.DBOs.Tenants
@using FluentBlue.WebApi.Client
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<FluentBlue.UI.Main.Pages.Resources.RegisterResource> registerResource
@inject AuthenticationWebApiClient authenticationWebApiClient
@inject UsersWebApiClient usersWebApiClient
@inject NavigationManager navigationManager
@inject ILogger<Register> logger
@inject IDialogService dialogService

<div style="background-size: cover; background-position: center; height: 100vh; width: 100%; background-image: url('_content/FluentBlue.UI.Main/images/login-background.png')">
    <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center" VerticalAlignment="VerticalAlignment.Center" Class="min-h-screen" Style="padding: 20px;">
        <FluentCard Class="w-full max-w-lg" Height="auto">
            @if (!isRegistered)
            {
                <EditForm Model="@registrationData" OnValidSubmit="@OnSubmit">
                    <DataAnnotationsValidator />

                    <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                        <FluentLabel Typo="Typography.H1">@registerResource["Title"]</FluentLabel>
                        <FluentLabel Style="text-align: center; margin-bottom: 20px;">@registerResource["Subtitle"]</FluentLabel>
                        
                        <FluentGrid Spacing="2" Style="width: 90%;">
                            <FluentGridItem xs="12" md="6">
                                <FluentTextField @bind-Value="@registrationData.FirstName" 
                                               Label="@registerResource["FirstNameLabel"]"
                                               Maxlength="100" 
                                               Class="w-full" 
                                               Required="true"
                                               AutoComplete="off" />
                                <FluentValidationMessage For="@(() => registrationData.FirstName)" />
                            </FluentGridItem>

                            <FluentGridItem xs="12" md="6">
                                <FluentTextField @bind-Value="@registrationData.LastName" 
                                               Label="@registerResource["LastNameLabel"]"
                                               Maxlength="100" 
                                               Class="w-full" 
                                               Required="true"
                                               AutoComplete="off" />
                                <FluentValidationMessage For="@(() => registrationData.LastName)" />
                            </FluentGridItem>

                            <FluentGridItem xs="12">
                                <FluentTextField @bind-Value="@registrationData.Email" 
                                               Label="@registerResource["EmailLabel"]"
                                               Maxlength="50" 
                                               Class="w-full" 
                                               Required="true"
                                               AutoComplete="off"
                                               TextFieldType="TextFieldType.Email">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Mail())" Color="@Color.Neutral" Slot="end" />
                                </FluentTextField>
                                <FluentValidationMessage For="@(() => registrationData.Email)" />
                            </FluentGridItem>

                            <FluentGridItem xs="12">
                                <FluentTextField @bind-Value="@registrationData.Username" 
                                               Label="@registerResource["UsernameLabel"]"
                                               Maxlength="30" 
                                               Class="w-full" 
                                               Required="true"
                                               AutoComplete="off">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Person())" Color="@Color.Neutral" Slot="end" />
                                </FluentTextField>
                                <FluentValidationMessage For="@(() => registrationData.Username)" />
                            </FluentGridItem>

                            <FluentGridItem xs="12" md="6">
                                <FluentTextField @bind-Value="@registrationData.Password" 
                                               Label="@registerResource["PasswordLabel"]"
                                               TextFieldType="TextFieldType.Password" 
                                               Maxlength="30" 
                                               Class="w-full" 
                                               Required="true"
                                               AutoComplete="off">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.LockClosed())" Color="@Color.Neutral" Slot="end" />
                                </FluentTextField>
                                <FluentValidationMessage For="@(() => registrationData.Password)" />
                            </FluentGridItem>

                            <FluentGridItem xs="12" md="6">
                                <FluentTextField @bind-Value="@registrationData.ConfirmPassword" 
                                               Label="@registerResource["ConfirmPasswordLabel"]"
                                               TextFieldType="TextFieldType.Password" 
                                               Maxlength="30" 
                                               Class="w-full" 
                                               Required="true"
                                               AutoComplete="off">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.LockClosed())" Color="@Color.Neutral" Slot="end" />
                                </FluentTextField>
                                <FluentValidationMessage For="@(() => registrationData.ConfirmPassword)" />
                            </FluentGridItem>

                            <FluentGridItem xs="12">
                                <FluentTextField @bind-Value="@registrationData.Mobile" 
                                               Label="@registerResource["MobileLabel"]"
                                               Maxlength="30" 
                                               Class="w-full"
                                               AutoComplete="off"
                                               TextFieldType="TextFieldType.Tel">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Phone())" Color="@Color.Neutral" Slot="end" />
                                </FluentTextField>
                                <FluentValidationMessage For="@(() => registrationData.Mobile)" />
                            </FluentGridItem>
                        </FluentGrid>

                        <FluentButton Type="@ButtonType.Submit" 
                                    Loading="@isLoading" 
                                    Appearance="@Appearance.Accent"
                                    Style="margin-top: 20px;">
                            @registerResource["CreateAccountButton"]
                        </FluentButton>
                        
                        <FluentLabel Color="Color.Warning">@resultMessage</FluentLabel>
                    </FluentStack>
                </EditForm>
            }
            else
            {
                <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                    <FluentLabel Typo="Typography.H1">@registerResource["Title"]</FluentLabel>
                    <FluentIcon Value="@(new Icons.Regular.Size48.CheckmarkCircle())" Color="@Color.Success" Style="margin: 20px 0;" />
                    <FluentLabel Style="text-align: center; margin-bottom: 20px;" Color="@Color.Success">
                        @registerResource["SuccessMessage"]
                    </FluentLabel>
                </FluentStack>
            }
            
            <br />
            <p style="text-align: center;">
                <FluentAnchor Href="/login" Appearance="Appearance.Hypertext">
                    @registerResource["BackToLoginButton"]
                </FluentAnchor>
            </p>
        </FluentCard>
    </FluentStack>
</div>

<FluentDialogProvider />
<FluentTooltipProvider />
<FluentToastProvider MaxToastCount="10" />
