﻿@implements IDialogContentComponent

@inject IDialogService dialogService
@inject ILogger<FluentBlue.UI.Main.Components.RecurrentEventHandlingDialog> logger

<FluentDialogHeader @ref="@dialogHeader">
    <FluentStack Orientation="Orientation.Horizontal" VerticalAlignment="VerticalAlignment.Top">
        <div style="width: 100%;">
            <FluentLabel Typo="Typography.PaneHeader">@this.title</FluentLabel>
        </div>
        @* <FluentButton Appearance="Appearance.Stealth">
            <FluentIcon Icon="Icons.Filled.Size20.Checkmark"  />
        </FluentButton> *@
    </FluentStack>
</FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto">
    <FluentLabel Class="mb-4">@this.message</FluentLabel>
    <FluentStack Orientation="Orientation.Vertical" VerticalGap="6">
        <FluentButton Appearance="Appearance.Accent" OnClick="CurrentEventBtnOnClick"><span>@Resources.RecurrentEventHandlingDialogResource.CurrentEvent</span></FluentButton>
        <FluentButton Appearance="Appearance.Neutral" OnClick="CurrentAndFollowingEventsBtnOnClick"><span>@Resources.RecurrentEventHandlingDialogResource.CurrentAndFollowingEvents</span></FluentButton>
        <FluentButton Appearance="Appearance.Neutral" OnClick="AllEventsBtnOnClick"><span>@Resources.RecurrentEventHandlingDialogResource.AllEvents</span></FluentButton>
    </FluentStack>
</FluentDialogBody>

<FluentDialogFooter>
    <FluentStack Orientation="Orientation.Vertical" VerticalAlignment="VerticalAlignment.Bottom">
        <FluentDivider Class="w-full" Role="DividerRole.Presentation"></FluentDivider>
        <FluentStack Orientation="Orientation.Horizontal" HorizontalAlignment="HorizontalAlignment.Right" VerticalAlignment="VerticalAlignment.Bottom" Class="self-end">
            @* <FluentButton Appearance="Appearance.Accent" OnClick="SaveBtnOnClick"><span class="xs:hidden">@GlobalResource.Save</span></FluentButton> *@
            <FluentButton IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtnOnClick"><span>@GlobalResource.Cancel</span></FluentButton>
        </FluentStack>
    </FluentStack>
</FluentDialogFooter>