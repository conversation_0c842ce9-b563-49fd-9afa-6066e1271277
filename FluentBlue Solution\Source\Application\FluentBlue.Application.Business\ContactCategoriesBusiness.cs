﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.Application.Business
{
    public class ContactCategoriesBusiness : IContactCategoriesBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public ContactCategoriesBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<ContactCategoriesBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Contacts.ContactCategory>> GetContactCategories(Guid tenantId)
        {
            try
            {
                //Validation

                //Query

                IQueryable<FluentBlue.Data.Model.DBOs.Contacts.ContactCategory> query = this.dbContext.ContactCategories.AsQueryable();

                query = query.AsNoTracking().Where(x => x.TenantId == tenantId).OrderBy(x => x.SortIndex);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Contacts.ContactCategory> contactCategories = await query.AsNoTracking().ToListAsync();

                //Response
                return contactCategories;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Contacts.ContactCategory?> GetContactCategory(Guid eventCategoryId)
        {
            try
            {
                //Query
                IQueryable<ContactCategory> query = dbContext.ContactCategories.AsQueryable();
                query = query.Where(c => c.ContactCategoryId.ToString() == eventCategoryId.ToString());

                return await query.AsNoTracking().FirstOrDefaultAsync<ContactCategory>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "ContactCategoryId=" + eventCategoryId.ToString() });
                throw;
            }
        }

        public async Task CreateOrUpdateContactCategory(ContactCategory contactCategory)
        {
            try
            {
                #region Validation
                if (contactCategory == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                List<ValidationResult> validationResults = new List<ValidationResult>();
                ValidationContext validationContext = new ValidationContext(contactCategory);
                if (Validator.TryValidateObject(contactCategory, validationContext, validationResults, true) == false)
                {
                    string validationErrors = string.Empty;
                    foreach (CompositeValidationResult compValidationResult in validationResults)
                    {
                        foreach (ValidationResult validationResult in compValidationResult.Results)
                        {
                            validationErrors += validationResult.ErrorMessage + ". ";
                        }
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Checks in database if Name alread exists
                FluentBlue.Data.Model.DBOs.Contacts.ContactCategory? eventCategory = await this.dbContext.ContactCategories.Where(x => x.Name == contactCategory.Name && x.ContactCategoryId != contactCategory.ContactCategoryId).FirstOrDefaultAsync();
                if (eventCategory != null)
                {
                    throw new ApplicationException(Resources.ContactCategoriesResource.NameAlreadyExists);
                }

                // In case we are deleting the ContactCategory
                if (contactCategory.ObjectState == ObjectState.Deleted)
                {
                    // Checks in database if this ContactCategory is used in any Event
                    FluentBlue.Data.Model.DBOs.Contacts.ContactCategory? existingContactCategory = await this.dbContext.ContactCategories.Where(x => x.ContactCategoryId == contactCategory.ContactCategoryId).FirstOrDefaultAsync();
                    if (existingContactCategory != null)
                    {
                        throw new ApplicationException(Resources.ContactCategoriesResource.CannotDeleteAlreadyUsedCategory);
                    }
                }
                #endregion

                //Query
                this.dbContext.Attach(contactCategory);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { contactCategory.ContactCategoryId });
                throw;
            }
        }

        public async Task DeleteContactCategory(Guid contactCategoryId)
        {
            try
            {
                ContactCategory? eventCagegoryObj = await this.dbContext.ContactCategories.Where(x => x.ContactCategoryId == contactCategoryId).FirstAsync();
                if (eventCagegoryObj != null)
                {
                    // Checks in database if there is any Contact.ContactCategoryMappings connected with this ContactCategory
                    bool usedInContacts = await this.dbContext.Set<ContactCategoryMapping>().AnyAsync(x => x.ContactCategoryId == contactCategoryId);
                    if (usedInContacts)
                    {
                        throw new ApplicationException(Resources.ContactCategoriesResource.CannotDeleteAlreadyUsedCategory);
                    }

                    eventCagegoryObj.ObjectState = ObjectState.Deleted;
                    this.dbContext.Attach(eventCagegoryObj);
                    this.dbContext.SaveChanges();
                }
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { "ContactCategoryId=" + contactCategoryId.ToString() });
                throw;
            }
        }
    }
}
