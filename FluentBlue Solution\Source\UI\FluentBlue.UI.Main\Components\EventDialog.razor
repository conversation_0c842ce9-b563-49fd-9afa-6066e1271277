﻿@inject HttpClient httpClient
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject HybridCache cache
@inject ILogger<FluentBlue.UI.Main.Components.EventDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.EventsWebApiClient> eventsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.EventCategoriesWebApiClient> eventCategoriesWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.EventStatesWebApiClient> eventStatesWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.ContactsWebApiClient> contactsWeb<PERSON>piClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject IFormFactor formFactor
@inject IJSRuntime JS
@inject IMessageService messageService

@using FluentBlue.Data.Model
@using FluentBlue.Data.Model.DBOs.Calendar
@using FluentBlue.Data.Model.DBOs.Contacts
@using FluentBlue.Data.Model.DBOs.Settings
@using FluentBlue.Data.Model.DBOs.Validators
@using FluentBlue.Data.Model.DTOs
@using FluentBlue.Shared.Utilities
@using FluentBlue.UI.Main.Auth
@using FluentBlue.UI.Main.Shared
@using FluentBlue.WebApi.Client
@using FluentBlue.WebApi.Shared.Request
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Caching.Hybrid
@using Microsoft.JSInterop
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Schedule
@using Blazored.FluentValidation


@implements IDialogContentComponent<EventDialogInput>

<FluentDialogHeader class="hidden"></FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto" Style="padding: -20px;">
    <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4">@Resources.EventDialogResource.Title</FluentLabel>
    <EditForm id="editForm" Model="@this.Content.Event">
        <ChildContent Context="context2">
            <FluentValidationValidator @ref="fluentValidationValidator" Validator="validator" />
            @* <FluentValidationSummary></FluentValidationSummary> *@

            <FluentToolbar Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                <FluentButton id="saveBtn" Loading="@this.isSaving" Disabled="@this.isDeleting" IconStart="@(new Icons.Regular.Size16.Save())" OnClick="SaveBtnOnClick" Appearance="Appearance.Accent" FormId="editForm"><span class="xs:hidden">@Resources.EventDialogResource.SaveBtn_Text</span></FluentButton>
                <FluentButton Loading="@this.isDeleting" Disabled="@this.isSaving" OnClick="DeleteBtnOnClick"><FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Color="Color.Error" Slot="start" /><span class="xs:hidden sm:hidden">@Resources.EventDialogResource.DeleteBtn_Text</span></FluentButton>
                <FluentButton Disabled="@(this.isSaving || this.isDeleting)" IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CloseBtnOnClick"><span class="xs:hidden sm:hidden">@GlobalResource.Close</span></FluentButton>
            </FluentToolbar>

            <FluentGrid Spacing="1" Class="md:m">
                <FluentGridItem xs="12">
                    <FluentMessageBarProvider Section="MESSAGES_TOP" />
                </FluentGridItem>

                <FluentGridItem xs="12">
                    <FluentTextField @bind-Value="@Content!.Event!.Subject" Label="@Resources.EventDialogResource.Subject" Maxlength="100" Class="w-full" AutoComplete="off" />
                    <FluentValidationMessage For="@(() => Content.Event!.Subject)" />
                </FluentGridItem>

                <FluentGridItem xs="12">
                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" HorizontalGap="4" HorizontalAlignment="HorizontalAlignment.Left" Wrap="true">
                        <div style="width: 320px;">
                            <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical">

                                <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical" VerticalGap="0" HorizontalGap="0">
                                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" HorizontalGap="5" VerticalGap="4">
                                        <div>
                                            <FluentDatePicker Value="@Content!.Event!.StartTimeLocal.Value" ValueChanged="StartDate_ValueChanged" Label="@Resources.EventDialogResource.Start" Class="w-40" />
                                        </div>
                                        <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical" VerticalGap="0" HorizontalGap="0" Style="padding-top:24px;">
                                            <Syncfusion.Blazor.Calendars.SfTimePicker TValue="DateTime?" Format="@(this.userSetting?.TimeFormat ?? "")" Value="@Content!.Event!.StartTimeLocal" ValueChanged="StartDate_ValueChanged" CssClass="@("w-32 " + (Content!.Event!.AllDay ? "hidden" : string.Empty))"></Syncfusion.Blazor.Calendars.SfTimePicker>
                                        </FluentStack>
                                    </FluentStack>
                                    <FluentValidationMessage For="@(() => Content.Event!.StartTimeLocal)" />
                                </FluentStack>

                                <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical" VerticalGap="0" HorizontalGap="0">
                                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" HorizontalGap="5" VerticalGap="4">
                                        <div>
                                            <FluentDatePicker Value="@Content!.Event!.EndTimeLocal.Value" ValueChanged="EndDate_ValueChanged" Label="@Resources.EventDialogResource.End" Class="w-40" />
                                        </div>
                                        <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical" VerticalGap="0" HorizontalGap="0" Style="padding-top:24px;">
                                            @* <FluentTimePicker Value="@Content!.Event!.EndTimeLocal" ValueChanged="EndDate_ValueChanged" Label="&nbsp;" Class="@(Content!.Event!.AllDay ? "hidden" :string.Empty)" /> *@
                                            <Syncfusion.Blazor.Calendars.SfTimePicker TValue="DateTime?" Format="@(this.userSetting?.TimeFormat ?? "")" Value="@Content!.Event!.EndTimeLocal" ValueChanged="EndDate_ValueChanged" CssClass="@("w-32 " + (Content!.Event!.AllDay ? "hidden" : string.Empty))"></Syncfusion.Blazor.Calendars.SfTimePicker>
                                        </FluentStack>
                                    </FluentStack>
                                    <FluentValidationMessage For="@(() => Content.Event!.EndTimeLocal)" />
                                </FluentStack>
                            </FluentStack>
                        </div>
                        <div style="width: 230px;">
                            <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical" VerticalGap="3">
                                <div class="block h-px sm:h-7 md:h-7 lg:h-7 xl:h-7"></div>
                                <FluentSwitch @bind-Value="@Content!.Event!.AllDay" Label="@Resources.EventDialogResource.AllDay" />
                                <div class="block h-px sm:h-9 md:h-9 lg:h-9 xl:h-9"></div>
                                <div>
                                    @if (Content!.Event!.CustomRecurrenceRule == "")  //if it is not a recurrent event
                                    {
                                        <FluentButton Appearance="Appearance.Outline" IconStart="@(new Icons.Regular.Size20.ArrowRepeatAll())" OnClick="ShowRecurrenceDialog">@Resources.EventDialogResource.ConvertToRecurrent</FluentButton>
                                    }
                                    else  //If it is a recurrent Event
                                    {
                                        @if (Content!.RecurrentEventHandlingType != WebApi.Shared.Request.RecurrentEventHandlingType.Current)  //If it is a recurrent Event but we are not editing only the current Event.
                                        {
                                            <FluentButton Appearance="Appearance.Lightweight" IconStart="@(new Icons.Regular.Size20.ArrowRepeatAll())" OnClick="ShowRecurrenceDialog">@Resources.EventDialogResource.EditRecurrence</FluentButton>
                                        }
                                    }
                                </div>
                            </FluentStack>
                        </div>
                        <div>
                            <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical" VerticalGap="3">
                                <div class="block h-px sm:h-[62px] md:h-[62px] lg:h-[62px] xl:h-[62px]"></div>
                                <FluentSelect Label="@Resources.EventDialogResource.Reminder" Items="@reminderTimes" ValueChanged="OnReminderTimeValueChanged" SelectedOption="@selectedReminder" TOption="ReminderTime?" Width="200px">
                                    <OptionTemplate>
                                        <FluentStack>
                                            <FluentLabel>@(context?.GetDisplayText() ?? @Resources.EventDialogResource.NoReminder)</FluentLabel>
                                        </FluentStack>
                                    </OptionTemplate>
                                </FluentSelect>
                            </FluentStack>
                        </div>
                    </FluentStack>
                </FluentGridItem>

                <FluentGridItem xs="12">
                    <FluentStack VerticalAlignment="VerticalAlignment.Bottom">
                        <div class="w-full">
                            <FluentAutocomplete Id="contactCmbBox" @ref="contactCmbBox" Items="this.contacts" Multiple="false" Virtualize="true" Label="@Resources.EventDialogResource.Contact" OnOptionsSearch="@OnContactCmbBoxSearch" Autocomplete="off" Width="100%" Class="w-full" Height="230px" @bind-SelectedOption="@selectedContact" TOption="Data.Model.DTOs.ContactLI" OptionText="@(x => x.FirstLastName)" OptionValue="@(x => x.ContactId.ToString())">
                                <OptionTemplate>
                                    <table style="width: 900px; margin:auto; border-collapse: separate; border-spacing: 15px; max-width:100%; table-layout: fixed">
                                        <tr>
                                            <td style="overflow: hidden;vertical-align: middle; text-overflow: ellipsis; white-space: nowrap;">
                                                @if (this.userSetting!.FullNameDisplay == Data.Model.FullNameDisplay.FirstLast)
                                                {
                                                    @context.FirstLastName
                                                }
                                                else if (this.userSetting.FullNameDisplay == Data.Model.FullNameDisplay.LastFirst)
                                                {
                                                    @context.LastFirstName
                                                }
                                            </td>
                                            @if (this.showTinInContactLists)
                                            {
                                                <td style="overflow: hidden;vertical-align: middle; text-overflow: ellipsis; white-space: nowrap;">
                                                    @context.TIN
                                                </td>
                                            }
                                            @if (this.showSsnInContactLists)
                                            {
                                                <td style="overflow: hidden;vertical-align: middle; text-overflow: ellipsis; white-space: nowrap;">
                                                    @context.SSN
                                                </td>
                                            }
                                        </tr>
                                    </table>
                                </OptionTemplate>
                                <FooterContent>
                                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal"
                                                 HorizontalAlignment="HorizontalAlignment.Right"
                                                 Style="padding: 3px;">
                                        <FluentButton id="newContactBtn" IconStart="@(new Icons.Regular.Size16.PeopleAdd())" OnClick="NewContactBtnOnClick"><span class="xs:hidden sm:hidden">@Resources.EventDialogResource.NewContactBtn_Text</span></FluentButton>

                                    </FluentStack>
                                </FooterContent>
                            </FluentAutocomplete>
                        </div>
                        @if (this.Content.Restricted == false)
                        {
                            <div>
                                <FluentButton id="editContactBtn" IconStart="@(new Icons.Regular.Size16.PeopleEdit())" OnClick="EditContactBtnOnClick"><span class="xs:hidden sm:hidden">@Resources.EventDialogResource.EditContactBtn_Text</span></FluentButton>
                            </div>
                        }
                    </FluentStack>
                </FluentGridItem>
                <FluentGridItem xs="12">
                    <FluentAutocomplete @ref="@userSelect" Label="@Resources.EventDialogResource.User" Items="@this.users" SelectedOptions="@selectedEventUsers" MaximumOptionsSearch="1000" TOption="UserLI" IconSearch="null" Class="w-full" KeepOpen="false" Appearance="FluentInputAppearance.Outline" MaximumSelectedOptions="5" OptionText="@(x => x.FullName)" IconDismiss="null" AutoComplete="off" SelectedOptionsChanged="UserSelectOnSelectedOptionsChanged" OnOptionsSearch="@UserSelectOnOptionsSearch">
                        <SelectedOptionTemplate>
                            <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal">
                                <FluentPersona ImageSize="24px" Initials="@context.Initials" Style=@("background-color: " + FluentBlue.Shared.Utilities.Colors.GetColorFromInitials(context.Initials)) DismissTitle="@Resources.EventDialogResource.Dismiss" OnDismissClick="@(async () => await this.userSelect.RemoveSelectedItemAsync(context))" />
                                <FluentLabel Class="whitespace-nowrap" Style="white-space:nowrap;">@context.FullName</FluentLabel>
                            </FluentStack>
                        </SelectedOptionTemplate>

                        @* Template used with each Option items *@
                        <OptionTemplate>
                            <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal">
                                <FluentPersona ImageSize="26px" Initials="@context.Initials" Style=@("background-color: " + FluentBlue.Shared.Utilities.Colors.GetColorFromInitials(context.Initials)) DismissTitle="" />
                                <FluentLabel Class="whitespace-nowrap">@context.FullName</FluentLabel>
                            </FluentStack>
                        </OptionTemplate>

                        @* Template used when the maximum number of selected items (MaximumSelectedOptions) has been reached *@
                        <MaximumSelectedOptionsMessage>
                            @Resources.EventDialogResource.MaximumUsersSelected
                        </MaximumSelectedOptionsMessage>

                        @* Content display at the top of the Popup area *@
                        @*  <HeaderContent>
                            <FluentLabel Color="Color.Accent"
                                         Style="padding: 8px; font-size: 11px; border-bottom: 1px solid var(--neutral-fill-stealth-hover);">
                                Suggested contacts
                            </FluentLabel>
                        </HeaderContent> *@

                        @* Content display at the bottom of the Popup area *@
                        <FooterContent>
                            @if (!context.Any())
                            {
                                <FluentLabel Style="font-size: 11px; text-align: center; width: 200px;">
                                    @Resources.EventDialogResource.NoUsersFound
                                </FluentLabel>
                            }
                        </FooterContent>
                    </FluentAutocomplete>
                    <FluentValidationMessage For="@(() => Content.Event!.EventUsers)" />
                </FluentGridItem>

                <FluentGridItem xs="12" md="6">
                    <FluentSelect Items="@this.eventCategories" Label="@Resources.EventDialogResource.Category" Class="w-full" Height="230px" @bind-SelectedOption="@selectedCategory" TOption="Data.Model.DBOs.Calendar.EventCategory" OptionText="@(x => x.Name)" OptionValue="@(x => x.EventCategoryId.ToString())">
                        <OptionTemplate>
                            <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Class="w-full">
                                <FluentIcon Icon="Icons.Filled.Size20.Square" hidden="@(context.EventCategoryId == Guid.Empty)" Slot="start" Color=@Color.Custom CustomColor="@context.BackColor" />
                                <FluentLabel Class="w-1/2">@context.Name</FluentLabel>
                            </FluentStack>
                        </OptionTemplate>
                    </FluentSelect>
                </FluentGridItem>
                <FluentGridItem xs="12" md="6">
                    <FluentSelect Items="@this.eventStates" Label="@Resources.EventDialogResource.State" Class="w-full" Height="230px" @bind-SelectedOption="@selectedState" TOption="Data.Model.DBOs.Calendar.EventState" OptionText="@(x => x.Name)" OptionValue="@(x => x.EventStateId.ToString())">
                        <OptionTemplate>
                            <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Class="w-full">
                                @* <FluentIcon Icon="Icons.Filled.Size20.Square" hidden="@(context.EventStateId == Guid.Empty)" Slot="start" Color=@Color.Custom CustomColor="@context.BackColor" /> *@
                                <FluentLabel Class="w-1/2">@context.Name</FluentLabel>
                            </FluentStack>
                        </OptionTemplate>
                    </FluentSelect>
                </FluentGridItem>

                <FluentGridItem xs="12" md="6">
                    <FluentTextField @bind-Value="@Content!.Event!.Location" Maxlength="100" Label="@Resources.EventDialogResource.Location" Class="w-full" AutoComplete="off" />
                    <FluentValidationMessage For="@(() => Content.Event!.Location)" />
                </FluentGridItem>

                @* <FluentGridItem xs="12" md="6">
                     <FluentCombobox Items="@reminderOptions" Label="@Resources.EventDialogResource.User" Class="w-full" Height="230px" @bind-SelectedOption="@selectedUserLI" TOption="Data.Model.DTOs.UserLI" OptionText="@(x=>x.FullName)" OptionValue="@(x=>x.UserId.ToString())">
                    <OptionTemplate>
                        <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Class="w-full">
                            <FluentLabel Class="w-1/2">@context.FullName</FluentLabel>
                            <FluentLabel Class="w-1/2">@context.Email</FluentLabel>
                        </FluentStack>
                    </OptionTemplate>
                </FluentCombobox>
                </FluentGridItem> *@

                <FluentGridItem xs="12">
                    <SfRichTextEditor @bind-Value="@Content.Event!.Description" EnableResize="false" CssClass=" min-h-64 h-full">
                        <RichTextEditorToolbarSettings Items="@toolbarItems" />
                    </SfRichTextEditor>
                </FluentGridItem>

            </FluentGrid>
        </ChildContent>
    </EditForm>
</FluentDialogBody>

