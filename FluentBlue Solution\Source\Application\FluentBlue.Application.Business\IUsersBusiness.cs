﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;

namespace FluentBlue.Application.Business
{
    public interface IUsersBusiness
    {
        Task<bool> CheckUsernamePasswordExists(string username, string password);
        Task<User?> GetUser(string username, string password);
        Task<User?> GetUser(Guid userId);
        Task<PagedData<List<UserView>>> GetUsers(ReadPagedDataParameters parameters);
        Task<List<UserLI>> GetUsersLI(Guid tenantId);
        Task CreateOrUpdateUser(User user);
        Task DeleteUser(Guid userId);
        Task<User?> CheckUserExists(Guid tenantId, Guid userId, string email);
        Task<User?> CheckUserExistsByEmail(string email);
        Task<User?> CheckUserExistsByUsername(string username);
        Task<int> GetUsersCount(Guid tenantId);

        // Password Reset Methods
        Task<PasswordResetToken> CreatePasswordResetTokenAsync(string email);
        Task<bool> ValidatePasswordResetTokenAsync(string token);
        Task<bool> ResetPasswordAsync(string token, string newPassword);

        // Registration Methods
        Task<User> RegisterUserAsync(RegisterUserRequest request);
    }
}