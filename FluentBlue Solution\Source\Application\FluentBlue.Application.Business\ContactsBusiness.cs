﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.Application.Business
{
    public class ContactsBusiness : IContactsBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public ContactsBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<ContactsBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>> GetContacts(ReadPagedContactsParameters parameters)
        {
            try
            {
                //Validation
                int resultsCount;
                parameters.Filter = (parameters.Filter ?? "").Trim();
                if (parameters.PageIndex <= 0)
                {
                    parameters.PageIndex = 1;
                }
                if (parameters.PageSize <= 0)
                {
                    parameters.PageSize = 10;
                }

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Contacts.Contact> query = this.dbContext.Contacts.Include(x => x.ContactCategoryMappings).ThenInclude(x => x.ContactCategory).AsQueryable();
                query = query.Where(c => c.TenantId == parameters.TenantId);
                if (parameters.Filter != "")
                {
                    query = query.Where(c => c.FirstName.Contains(parameters.Filter) || c.LastName.Contains(parameters.Filter));
                }
                //If parameters.ContactCategoryIds is null or empty then it means that we should return Contacts from all ContactCategories.
                if (parameters.ContactCategoryIds != null && parameters.ContactCategoryIds.Count > 0)
                {
                    //If the list contains the Guid.Empty (it means events that has none category)
                    if (parameters.ContactCategoryIds.Contains(Guid.Empty))
                    {
                        query = query.Where(c => c.ContactCategoryMappings.Count == 0 || c.ContactCategoryMappings.Any(x => parameters.ContactCategoryIds.Contains(x.ContactCategoryId)));
                    }
                    else  //Else, it means events that at least one selected "real" ContactCategories.
                    {
                        query = query.Where(c => c.ContactCategoryMappings.Any(x => parameters.ContactCategoryIds.Contains(x.ContactCategoryId)));
                    }
                }
                if (parameters.SortColumns != null && parameters.SortColumns.Count > 0)
                {
                    foreach (var sortColumn in parameters.SortColumns)
                    {
                        query = sortColumn.Value == SortOrder.Ascending
                            ? query.OrderBy(e => EF.Property<object>(e, sortColumn.Key))
                            : query.OrderByDescending(e => EF.Property<object>(e, sortColumn.Key));
                    }
                }

                //Διαβάζει το σύνολο των records.
                resultsCount = query.Count();

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Contacts.Contact> contacts = await query.AsNoTracking().Skip((parameters.PageIndex - 1) * parameters.PageSize).Take(parameters.PageSize).ToListAsync();
                List<FluentBlue.Data.Model.DTOs.ContactView> contactsDTOs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Contacts.Contact>, List<FluentBlue.Data.Model.DTOs.ContactView>>(contacts);

                //Response
                PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>> pagedData = new PagedData<List<Data.Model.DTOs.ContactView>>();
                pagedData.Data = contactsDTOs;
                pagedData.DataTotalCount = resultsCount;
                return pagedData;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Parameters=" + parameters.ToString() });
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DTOs.ContactLI>> GetContactsLI(Guid tenantId)
        {
            try
            {
                //Validation

                //Query - Using direct projection to ContactLI for better performance
                IQueryable<FluentBlue.Data.Model.DTOs.ContactLI> query = this.dbContext.Contacts
                    .Where(x => x.TenantId == tenantId)
                    .OrderBy(x => x.LastName)
                    .ThenBy(x => x.FirstName)
                    .Take(5000) // TODO: να φύγει το Take(5000) αλλά κολλάει με πολλά contacts.
                    .Select(x => new FluentBlue.Data.Model.DTOs.ContactLI
                    {
                        ContactId = x.ContactId,
                        FirstName = x.FirstName,
                        LastName = x.LastName,
                        FullName = x.FirstName + " " + x.LastName,
                        Occupation = x.Occupation,
                        TIN = x.TIN,
                        SSN = x.SSN,
                        Image = x.Image,
                        // PhoneNumbers concatenation can be expensive - only include if absolutely necessary
                        // If phone numbers are critical for the UI, consider a separate endpoint for detailed contact info
                        PhoneNumbers = x.Phones.Any() ? 
                            string.Join(", ", x.Phones.OrderBy(p => p.Type).Select(p => p.PhoneNumber)) : 
                            string.Empty
                    });

                //Read data with no tracking for better performance
                List<FluentBlue.Data.Model.DTOs.ContactLI> contactLIs = await query.AsNoTracking().ToListAsync();
                return contactLIs;
            }
            catch (Exception ex)
            {
                logger.Log(Microsoft.Extensions.Logging.LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }
        public async Task<Data.Model.DBOs.Contacts.Contact?> GetContact(Guid contactId)
        {
            try
            {
                //Query
                IQueryable<Contact> query = dbContext.Contacts.Include(x => x.Emails).Include(x => x.Phones).Include(x => x.Addresses).Include(x => x.ContactCategoryMappings).AsQueryable();
                query = query.Where(c => c.ContactId.ToString() == contactId.ToString());

                return await query.AsNoTracking().FirstOrDefaultAsync<Contact>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "ContactId=" + contactId.ToString() });
                throw;
            }
        }

        public async Task CreateOrUpdateContact(Contact contact)
        {
            try
            {
                //Validation
                if (contact == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                ContactValidator validator = new ContactValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(contact);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage;  // + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Query
                this.dbContext.Attach(contact);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { contact.ContactId });
                throw;
            }
        }

        public async Task DeleteContact(Guid contactId)
        {
            Contact? contact = await this.dbContext.Contacts.Where(x => x.ContactId == contactId).FirstAsync();
            if (contact != null)
            {
                contact.ObjectState = ObjectState.Deleted;
                this.dbContext.Attach(contact);
                this.dbContext.SaveChanges();
            }
        }

        public async Task<int> GetContactsCount(Guid tenantId)
        {
            try
            {
                //Query
                IQueryable<Contact> query = this.dbContext.Contacts.AsNoTracking().AsQueryable();
                query = query.Where(c => c.TenantId == tenantId);

                //Return total count
                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }
    }
}
