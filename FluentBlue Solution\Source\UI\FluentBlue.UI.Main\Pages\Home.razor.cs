﻿using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Pages
{
    public partial class Home
    {
        [Inject]
        private HybridCache cache { get; set; } = default!;
        private UserSetting? userSetting;
        private SummaryDataDto? summaryData = new();

        protected override void OnInitialized()
        {
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    #region Διαβάζει τα UserSettings από το cache
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);
                    #endregion

                    #region Διαβάζει τα SummaryData από το cache
                    var generalDataCacheOptions = new HybridCacheEntryOptions
                    {
                        LocalCacheExpiration = TimeSpan.FromMinutes(20)
                    };

                    this.summaryData = await cache.GetOrCreateAsync(
                        Keywords.SummaryData,
                        async cancel =>
                        {
                            GeneralWebApiClient generalDataWebApiClient = new GeneralWebApiClient(httpClient, generalWebApiClientLogger);
                            return await generalDataWebApiClient.GetSummaryData(DateTime.Now, userSetting!.TimeZone);
                        },
                        generalDataCacheOptions,
                        new List<string> { "Deletable" }
                    );
                    #endregion

                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
            }
        }

        public void buttonClicked()
        {
            try
            {
                throw new ApplicationException("apo to Home Device");
            }
            //catch (Exception ex)
            //{
            //    // Log the exception using the logger
            //    logger.LogError(ex, "An error occurred in Home buttonClicked");
            //    // Optionally, you can also log to Sentry
            //    //_exceptionLogger.LogException(ex);
            //}
            finally { }
        }
    }
}
