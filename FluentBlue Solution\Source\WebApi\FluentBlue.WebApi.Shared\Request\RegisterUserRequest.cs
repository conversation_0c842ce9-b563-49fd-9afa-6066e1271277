using System.ComponentModel.DataAnnotations;

namespace FluentBlue.WebApi.Shared.Request
{
    public class RegisterUserRequest
    {
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [MaxLength(50)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MaxLength(30)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [MaxLength(30)]
        public string Password { get; set; } = string.Empty;

        [MaxLength(30)]
        public string Mobile { get; set; } = string.Empty;
    }
}
