﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Components.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class EventDialogResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EventDialogResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Components.Resources.EventDialogResource", typeof(EventDialogResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Day.
        /// </summary>
        internal static string AllDay {
            get {
                return ResourceManager.GetString("AllDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        internal static string CancelBtn_Text {
            get {
                return ResourceManager.GetString("CancelBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category.
        /// </summary>
        internal static string Category {
            get {
                return ResourceManager.GetString("Category", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert to recurrent.
        /// </summary>
        internal static string ConvertToRecurrent {
            get {
                return ResourceManager.GetString("ConvertToRecurrent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string DeleteBtn_Text {
            get {
                return ResourceManager.GetString("DeleteBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dismiss.
        /// </summary>
        internal static string Dismiss {
            get {
                return ResourceManager.GetString("Dismiss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show.
        /// </summary>
        internal static string EditContactBtn_Text {
            get {
                return ResourceManager.GetString("EditContactBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are editing all occurrences of the recurring event..
        /// </summary>
        internal static string EditingAllRecurrentEventsMessage {
            get {
                return ResourceManager.GetString("EditingAllRecurrentEventsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are editing this and the following events of the recurring event..
        /// </summary>
        internal static string EditingCurrentAndFollowingRecurrentEventsMessage {
            get {
                return ResourceManager.GetString("EditingCurrentAndFollowingRecurrentEventsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are editing a single occurrence of the recurring event..
        /// </summary>
        internal static string EditingSingleRecurrentEventMessage {
            get {
                return ResourceManager.GetString("EditingSingleRecurrentEventMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit recurrence.
        /// </summary>
        internal static string EditRecurrence {
            get {
                return ResourceManager.GetString("EditRecurrence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        internal static string End {
            get {
                return ResourceManager.GetString("End", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 15 minutes before.
        /// </summary>
        internal static string FifteenMinReminder {
            get {
                return ResourceManager.GetString("FifteenMinReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5 minutes before.
        /// </summary>
        internal static string FiveMinReminder {
            get {
                return ResourceManager.GetString("FiveMinReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid recurrence rule. Please try again..
        /// </summary>
        internal static string InvalidRecurrenceRule {
            get {
                return ResourceManager.GetString("InvalidRecurrenceRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum number of users selected..
        /// </summary>
        internal static string MaximumUsersSelected {
            get {
                return ResourceManager.GetString("MaximumUsersSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Contact.
        /// </summary>
        internal static string NewContactBtn_Text {
            get {
                return ResourceManager.GetString("NewContactBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No reminder.
        /// </summary>
        internal static string NoReminder {
            get {
                return ResourceManager.GetString("NoReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No users found.
        /// </summary>
        internal static string NoUsersFound {
            get {
                return ResourceManager.GetString("NoUsersFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 hour before.
        /// </summary>
        internal static string OneHourReminder {
            get {
                return ResourceManager.GetString("OneHourReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recurrence.
        /// </summary>
        internal static string Recurrence {
            get {
                return ResourceManager.GetString("Recurrence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recurrence count cannot be more than 20..
        /// </summary>
        internal static string RecurrenceCountExceededRule {
            get {
                return ResourceManager.GetString("RecurrenceCountExceededRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current recurrence causes overlapping events. Select another recurrence..
        /// </summary>
        internal static string RecurrencesOverlapping {
            get {
                return ResourceManager.GetString("RecurrencesOverlapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reminder.
        /// </summary>
        internal static string Reminder {
            get {
                return ResourceManager.GetString("Reminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string SaveBtn_Text {
            get {
                return ResourceManager.GetString("SaveBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        internal static string Start {
            get {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        internal static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subject.
        /// </summary>
        internal static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 30 minutes before.
        /// </summary>
        internal static string ThirtyMinReminder {
            get {
                return ResourceManager.GetString("ThirtyMinReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event.
        /// </summary>
        internal static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2 hours before.
        /// </summary>
        internal static string TwoHoursReminder {
            get {
                return ResourceManager.GetString("TwoHoursReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At event time.
        /// </summary>
        internal static string ZeroMinReminder {
            get {
                return ResourceManager.GetString("ZeroMinReminder", resourceCulture);
            }
        }
    }
}
