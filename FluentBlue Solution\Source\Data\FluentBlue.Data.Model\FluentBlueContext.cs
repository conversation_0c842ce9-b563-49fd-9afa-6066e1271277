using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.Extensions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public partial class FluentBlueDbContext : DbContext
    {
        private string connectionString = string.Empty;

        public FluentBlueDbContext(DbContextOptions<FluentBlueDbContext> options) : base(options)
        {
            ChangeTracker.LazyLoadingEnabled = false;
        }

        public FluentBlueDbContext(string connectionString) : base()
        {
            //base.OnConfiguring(new DbContextOptionsBuilder().UseSqlServer(connectionString));
            this.connectionString = connectionString;
            ChangeTracker.LazyLoadingEnabled = false;
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                if (string.IsNullOrEmpty(connectionString))
                {
                    //optionsBuilder.UseSqlServer("Server=MAIN\\SQLEXPRESS;Database=FluentBlue;Trusted_Connection=True;TrustServerCertificate=True");
                    optionsBuilder.UseSqlServer("Name=FluentBlueConnectionString");
                }
                else
                {
                    optionsBuilder.UseSqlServer(connectionString);
                }
            }
        }

        public virtual DbSet<Tenant> Tenants { get; set; }
        public virtual DbSet<Role> Roles { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<PasswordResetToken> PasswordResetTokens { get; set; }
        public virtual DbSet<Contact> Contacts { get; set; }
        public virtual DbSet<ContactCategory> ContactCategories { get; set; }
        public virtual DbSet<Event> Events { get; set; }
        public virtual DbSet<EventCategory> EventCategories { get; set; }
        public virtual DbSet<EventState> EventStates { get; set; }
        public virtual DbSet<EventReminder> EventReminders { get; set; }
        public virtual DbSet<EventUser> EventUsers { get; set; }
        public virtual DbSet<UserSetting> UserSettings { get; set; }
        public virtual DbSet<UserDevice> UserDevices { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.UseCollation("Greek_CI_AI");
                        
            OnModelCreatingPartial(modelBuilder);
           
            modelBuilder.Entity<Role>().Navigation(r => r.Users).AutoInclude(false); // Prevent automatic eager loading

            ConfigureDataEncryption(modelBuilder);  //Confiigure the fields in database that will be encrypted.
        }

        private void ConfigureDataEncryption(ModelBuilder modelBuilder)
        {
            //ATTENTION: This method configures the encryption for sensitive data fields in the database.

            var encryptionConverter = new EncryptionValueConverter();

            // Configure User entity encryption
            modelBuilder.Entity<User>(entity =>
            {
                entity.Property(e => e.Password)
                    .HasConversion(encryptionConverter);
            });

            // ATTENTION: DON'T UNCOMMENT THIS, OTHERWISE IT WILL ENCRYPT DATA

            ////// Configure Contact entity encryption
            ////modelBuilder.Entity<Contact>(entity =>
            ////{
            ////    // Encrypt sensitive personal information
            ////    entity.Property(e => e.FirstName)
            ////        .HasConversion(encryptionConverter)
            ////        .HasMaxLength(500); // Increased for encrypted data

            ////    entity.Property(e => e.LastName)
            ////        .HasConversion(encryptionConverter)
            ////        .HasMaxLength(500);

            ////    entity.Property(e => e.MiddleName)
            ////        .HasConversion(encryptionConverter)
            ////        .HasMaxLength(500);

            ////    entity.Property(e => e.SSN)
            ////        .HasConversion(encryptionConverter)
            ////        .HasMaxLength(500);

            ////    entity.Property(e => e.TIN)
            ////        .HasConversion(encryptionConverter)
            ////        .HasMaxLength(500);
            ////});

            ////// Configure ContactEmail entity encryption
            ////modelBuilder.Entity<ContactEmail>(entity =>
            ////{
            ////    entity.Property(e => e.EmailAddress)
            ////        .HasConversion(encryptionConverter)
            ////        .HasMaxLength(500);
            ////});

            ////// Configure ContactPhone entity encryption
            ////modelBuilder.Entity<ContactPhone>(entity =>
            ////{
            ////    entity.Property(e => e.PhoneNumber)
            ////        .HasConversion(encryptionConverter)
            ////        .HasMaxLength(500);
            ////});
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
