﻿using Blazored.FluentValidation;
using FluentBlue.Data.Model.DBOs.Settings;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Data.Model;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Microsoft.Extensions.Caching.Hybrid;

namespace FluentBlue.UI.Main.Components
{
    public partial class RoleDialog
    {
        //General
        [Parameter] public Data.Model.DBOs.Tenants.Role? Content { get; set; } = new Data.Model.DBOs.Tenants.Role();
        private EditContext roleContext = new EditContext(typeof(Data.Model.DBOs.Tenants.Role));
        [CascadingParameter]
        public FluentDialog CurrentDialog { get; set; } = default!;
        private FluentValidationValidator? fluentValidationValidator = new FluentValidationValidator();
        private FluentValidation.IValidator? validator = new RoleValidator();
        UserSetting? userSetting;


        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    // Reads the UserSettings from cache or database
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);

                    this.SetDataToUI();
                    StateHasChanged();
                }
                else
                {
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {

            }
        }

        private void GetDataFromUI()
        {
            //TODO: Αυτή η function υπάρχει για ορισμένα controls όπως τα FluentCombobox όπου το @bind-Value δεν δουλεύει σωστά και αντί για το Value επιστρέφει το Text του control.
            //Αυτό θα πρέπει να αλλάξει και να φύγει η function αυτή.
            //this.Content!.RoleId = this.selectedRole?.RoleId ?? Guid.Empty;
            //this.Content!.RoleId = this.roleIdSelect!.SelectedOption?.RoleId;
            //this.Content!.RoleId = this.selectedRoleId != null ? Guid.Parse(this.selectedRoleId) : Guid.Empty;
            //this.Content!.Role = null;  //Το κάνουμε null για να μην διαφέρει από το RoleId.
        }

        private void SetDataToUI()
        {
            //this.selectedRole = this.roles!.Where(x => x.RoleId == this.Content!.RoleId).FirstOrDefault()!;
            //if (this.Content!.RoleId.HasValue)
            //{
            //    //this.selectedRole = this.roles!.Where(x => x.RoleId == this.Content!.RoleId).FirstOrDefault()!;
            //    this.selectedRoleId = this.roles!.Where(x => x.RoleId == this.Content!.RoleId).FirstOrDefault()?.RoleId.ToString();
            //}
            //else
            //{
            //    //this.selectedRole = RoleLI.Empty;
            //    this.selectedRoleId = Guid.Empty.ToString();
            //}
        }

        private async Task CancelBtn_OnClick()
        {
            try
            {
                await this.CurrentDialog.CancelAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task DeleteBtn_OnClick()
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();

                if (result.Cancelled == false)
                {
                    RolesWebApiClient rolesWebApiClient = new RolesWebApiClient(httpClient, rolesWebApiClientLogger);
                    await rolesWebApiClient.DeleteRole(this.Content!.RoleId);
                    await this.CurrentDialog.CloseAsync();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SaveBtn_OnClick()
        {
            await this.Save(true);
        }

        private async Task Save(bool close)
        {
            try
            {
                this.GetDataFromUI();

                if (this.Content!.ObjectState != ObjectState.Added)
                {
                    this.Content.ObjectState = ObjectState.Modified;
                }
                else
                {
                    this.Content.DateCreatedUtc = DateTime.UtcNow;
                }
                this.Content.DateModifiedUtc = DateTime.UtcNow;

                if (await this.ValidateData())
                {
                    RolesWebApiClient rolesWebApiClient = new RolesWebApiClient(httpClient, rolesWebApiClientLogger);

                    this.Content = await rolesWebApiClient.CreateOrUpdateRole(this.Content);
                    this.Content!.UserTimeZoneId = this.userSetting!.TimeZone;

                    if (close)
                    {
                        await this.CurrentDialog.CloseAsync(this.Content);
                    }
                    else
                    {
                        this.Content!.ObjectState = ObjectState.Unchanged;

                        this.StateHasChanged();
                    }
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                logger.LogError(ex, ex.Message);
            }
        }

        private async Task<bool> ValidateData()
        {
            try
            {
                //RoleValidator validator = new RoleValidator();
                //this.fluentValidationValidator.Validator = validator;
                bool valid = this.fluentValidationValidator!.Validate();

                if (valid == false)
                {
                    // Convert error messages to HTML bullet list
                    string errorMessage = GlobalResource.CorrectInvalidFields;
                    RenderFragment errorRF = FluentBlue.Shared.Utilities.ValidationErrorsToBulletsConverter.ConvertValidationErrorsToBullets(errorMessage, this.fluentValidationValidator.GetFailuresFromLastValidation().Select(e => e.ErrorMessage).ToArray(), "");

                    // Show errors in dialog
                    await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                    {
                        ShowTitle = false,
                        ShowDismiss = false,
                        DialogType = Microsoft.FluentUI.AspNetCore.Components.DialogType.MessageBox,
                        PrimaryAction = UI.Main.GlobalResource.Close,
                        SecondaryAction = "",
                        Modal = true,
                        PreventDismissOnOverlayClick = true
                    });
                }

                return valid;
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
