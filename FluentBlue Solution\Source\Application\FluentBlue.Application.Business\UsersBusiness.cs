﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using static System.Runtime.InteropServices.JavaScript.JSType;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.WebApi.Shared.Request;
using System.Security.Cryptography;

namespace FluentBlue.Application.Business
{
    public class UsersBusiness : IUsersBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        private IMapper mapper;
        private ILogger logger;
        private IEmailService emailService;

        public UsersBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, <PERSON><PERSON>apper mapper, ILogger logger, IEmailService emailService)
        {
            this.dbContext = dbContext;
            this.mapper = mapper;
            this.logger = logger;
            this.emailService = emailService;
        }

        public async Task<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>> GetUsers(ReadPagedDataParameters parameters)
        {
            try
            {
                //Validation
                int resultsCount;
                parameters.Filter = parameters.Filter?.Trim() ?? "";
                if (parameters.PageIndex <= 0)
                {
                    parameters.PageIndex = 1;
                }
                if (parameters.PageSize <= 0)
                {
                    parameters.PageSize = 10;
                }

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.Include(x => x.Role).AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == parameters.TenantId);
                if (parameters.Filter != null && parameters.Filter != "")
                {
                    query = query.Where(x => x.Username.Contains(parameters.Filter) || x.FirstName.Contains(parameters.Filter) || x.LastName.Contains(parameters.Filter) || x.Email.Contains(parameters.Filter) || x.Role!.Name.Contains(parameters.Filter));
                }
                if (parameters.SortColumns != null && parameters.SortColumns.Count > 0)
                {
                    foreach (var sortColumn in parameters.SortColumns)
                    {
                        query = sortColumn.Value == SortOrder.Ascending
                            ? query.OrderBy(e => EF.Property<object>(e, sortColumn.Key))
                            : query.OrderByDescending(e => EF.Property<object>(e, sortColumn.Key));
                    }
                }

                //Διαβάζει το σύνολο των records.
                resultsCount = query.Count();

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Tenants.User> users = await query.Skip((parameters.PageIndex - 1) * parameters.PageSize).Take(parameters.PageSize).ToListAsync();
                List<FluentBlue.Data.Model.DTOs.UserView> usersDTOs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Tenants.User>, List<FluentBlue.Data.Model.DTOs.UserView>>(users);

                PagedData<List<FluentBlue.Data.Model.DTOs.UserView>> pagedData = new PagedData<List<Data.Model.DTOs.UserView>>();
                pagedData.Data = usersDTOs;
                pagedData.DataTotalCount = resultsCount;
                return pagedData;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DTOs.UserLI>> GetUsersLI(Guid tenantId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTrackingWithIdentityResolution().AsQueryable();
                query = query.Where(x => x.TenantId == tenantId).OrderBy(x => x.LastName);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Tenants.User> users = await query.ToListAsync();
                List<FluentBlue.Data.Model.DTOs.UserLI> usersLIs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Tenants.User>, List<FluentBlue.Data.Model.DTOs.UserLI>>(users);

                return usersLIs;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Tenants.User?> GetUser(string username, string password)
        {
            try
            {
                //if (username == "333" && password == "333")
                //{
                //    User fakeUser = new Data.Model.DBOs.Tenants.User();
                //    fakeUser.Username = "33";
                //    fakeUser.LastName = "Manp";
                //    fakeUser.FirstName = "Peter";
                //    fakeUser.Email = "<EMAIL>";
                //    fakeUser.UserId = Guid.CreateVersion7();
                //    fakeUser.TenantId = Guid.CreateVersion7();
                //    fakeUser.RoleId = Guid.CreateVersion7();
                //    Role role = new Role();
                //    role.TenantId = fakeUser.TenantId;
                //    role.Name = "Admin";
                //    fakeUser.Role = role;
                //    return fakeUser;
                //}

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTrackingWithIdentityResolution().AsQueryable();
                query = query.Include(x => x.Role).Where(x => x.Username == username);
                Data.Model.DBOs.Tenants.User? user = await query.FirstOrDefaultAsync();

                // If user not found, return null
                if (user == null)
                {
                    return null;
                }

                // Verify password - the stored password is automatically decrypted by the EncryptionValueConverter
                // So we can directly compare the provided password with the decrypted stored password
                if (user.Password == password)
                {
                return user;
            }

                // Password doesn't match
                return null;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Tenants.User?> GetUser(Guid userId)
        {
            try
            {
                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTrackingWithIdentityResolution().Include(x => x.Role).AsNoTrackingWithIdentityResolution().Where(x => x.UserId == userId);
                Data.Model.DBOs.Tenants.User? user = await query.FirstOrDefaultAsync();

                if (user?.Role != null)
                {
                    user.Role.Users = new List<User>();  //Καθαρίζουμε το property Users του Role γιατί φέρνει δεδομένα χωρίς να τα έχουμε ζητήσει και δημιουργεί πρόβλημα.
                }

                //////////////////////
                //FluentBlue.Data.Model.DBOs.Tenants.User? user2 = await this.dbContext.Users.AsNoTrackingWithIdentityResolution().Where(x => x.UserId == userId).FirstOrDefaultAsync();
                //if (user2 != null)
                //{
                //    this.dbContext.Entry(user2).Reference(x => x.Role).Load();
                //}

                //Result
                return user;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task CreateOrUpdateUser(User user)
        {
            try
            {
                //Validation
                if (user == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                UserValidator validator = new UserValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(user);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Query
                this.dbContext.Attach(user);

                //Creates UserSetting for new User
                if (user.ObjectState == ObjectState.Added)
                {
                    UserSetting userSetting = new UserSetting();
                    userSetting.UserId = user.UserId;
                    userSetting.ObjectState = ObjectState.Added;
                    this.dbContext.Attach(userSetting);
                }
                
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { user.UserId });
                throw;
            }
        }

        public async Task DeleteUser(Guid userId)
        {
            User? user = await this.dbContext.Users.Where(x => x.UserId == userId).AsNoTracking().FirstAsync();
            if (user != null)
            {
                user.ObjectState = ObjectState.Deleted;
                this.dbContext.Attach(user);
                this.dbContext.SaveChanges();
            }
        }

        public async Task<bool> CheckUsernamePasswordExists(string username, string password)
        {
            try
            {
                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTracking().AsQueryable();
                query = query.Where(x => x.Username == username);
                var user = await query.FirstOrDefaultAsync();

                // If user not found, return false
                if (user == null)
                {
                    return false;
                }

                // Verify password - the stored password is automatically decrypted by the EncryptionValueConverter
                // So we can directly compare the provided password with the decrypted stored password
                return user.Password == password;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<User?> CheckUserExists(Guid tenantId, Guid userId, string email)
        {
            User? user = await this.dbContext.Users.Where(x => x.UserId != userId && x.Email == email && x.TenantId == tenantId).FirstOrDefaultAsync();
            return user;
        }

        public async Task<int> GetUsersCount(Guid tenantId)
        {
            try
            {
                //Query
                IQueryable<User> query = this.dbContext.Users.AsNoTracking().AsQueryable();
                query = query.Where(c => c.TenantId == tenantId);

                //Return total count
                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

        public async Task<User?> CheckUserExistsByEmail(string email)
        {
            try
            {
                User? user = await this.dbContext.Users.AsNoTracking()
                    .Where(x => x.Email.ToLower() == email.ToLower())
                    .FirstOrDefaultAsync();
                return user;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Email=" + email });
                throw;
            }
        }

        public async Task<User?> CheckUserExistsByUsername(string username)
        {
            try
            {
                User? user = await this.dbContext.Users.AsNoTracking()
                    .Where(x => x.Username.ToLower() == username.ToLower())
                    .FirstOrDefaultAsync();
                return user;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Username=" + username });
                throw;
            }
        }

        public async Task<PasswordResetToken> CreatePasswordResetTokenAsync(string email)
        {
            try
            {
                // Find user by email
                var user = await CheckUserExistsByEmail(email);
                if (user == null)
                {
                    throw new ApplicationException("User not found with the provided email address.");
                }

                // Invalidate any existing tokens for this user
                var existingTokens = await this.dbContext.PasswordResetTokens
                    .Where(x => x.UserId == user.UserId && !x.IsUsed)
                    .ToListAsync();

                foreach (var token in existingTokens)
                {
                    token.IsUsed = true;
                    token.ObjectState = ObjectState.Modified;
                    this.dbContext.Attach(token);
                }

                // Create new token
                var resetToken = PasswordResetToken.CreateToken(user.UserId);
                this.dbContext.Attach(resetToken);

                await this.dbContext.SaveChangesAsync();

                // Send email
                await emailService.SendPasswordResetEmailAsync(user.Email, resetToken.Token, user.FullName);

                return resetToken;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Email=" + email });
                throw;
            }
        }

        public async Task<bool> ValidatePasswordResetTokenAsync(string token)
        {
            try
            {
                var resetToken = await this.dbContext.PasswordResetTokens
                    .AsNoTracking()
                    .Where(x => x.Token == token)
                    .FirstOrDefaultAsync();

                return resetToken?.IsValid ?? false;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Token=" + token });
                throw;
            }
        }

        public async Task<bool> ResetPasswordAsync(string token, string newPassword)
        {
            try
            {
                var resetToken = await this.dbContext.PasswordResetTokens
                    .Include(x => x.User)
                    .Where(x => x.Token == token)
                    .FirstOrDefaultAsync();

                if (resetToken == null || !resetToken.IsValid)
                {
                    return false;
                }

                // Update user password (Note: In production, you should hash the password)
                resetToken.User!.Password = newPassword; // TODO: Hash password
                resetToken.User.ObjectState = ObjectState.Modified;
                this.dbContext.Attach(resetToken.User);

                // Mark token as used
                resetToken.IsUsed = true;
                resetToken.ObjectState = ObjectState.Modified;
                this.dbContext.Attach(resetToken);

                await this.dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Token=" + token });
                throw;
            }
        }

        public async Task<User> RegisterUserAsync(RegisterUserRequest request)
        {
            try
            {
                // Check if user already exists by email
                var existingUserByEmail = await CheckUserExistsByEmail(request.Email);
                if (existingUserByEmail != null)
                {
                    throw new ApplicationException("A user with this email address already exists.");
                }

                // Check if user already exists by username
                var existingUserByUsername = await CheckUserExistsByUsername(request.Username);
                if (existingUserByUsername != null)
                {
                    throw new ApplicationException("A user with this username already exists.");
                }

                // Get default tenant and role (you may need to adjust this logic)
                var defaultTenantId = await GetDefaultTenantId();
                var defaultRoleId = await GetDefaultRoleId();

                // Create new user
                var newUser = new User
                {
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    Username = request.Username,
                    Password = request.Password, // TODO: Hash password
                    Mobile = request.Mobile,
                    TenantId = defaultTenantId,
                    RoleId = defaultRoleId,
                    ObjectState = ObjectState.Added
                };

                // Validate user
                UserValidator validator = new UserValidator();
                var validationResult = validator.Validate(newUser);
                if (!validationResult.IsValid)
                {
                    var errors = string.Join(". ", validationResult.Errors.Select(x => x.ErrorMessage));
                    throw new ApplicationException(errors);
                }

                this.dbContext.Attach(newUser);

                // Create UserSetting for new User
                UserSetting userSetting = new UserSetting();
                userSetting.UserId = newUser.UserId;
                userSetting.ObjectState = ObjectState.Added;
                this.dbContext.Attach(userSetting);

                await this.dbContext.SaveChangesAsync();

                // Send welcome email
                await emailService.SendWelcomeEmailAsync(newUser.Email, newUser.FullName);

                return newUser;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { request });
                throw;
            }
        }

        private async Task<Guid> GetDefaultTenantId()
        {
            // TODO: Implement logic to get default tenant ID
            // For now, return the first tenant or create a default one
            var tenant = await this.dbContext.Tenants.FirstOrDefaultAsync();
            if (tenant != null)
            {
                return tenant.TenantId;
            }

            // If no tenant exists, you might want to create a default one
            // or throw an exception
            throw new ApplicationException("No default tenant found. Please contact administrator.");
        }

        private async Task<Guid> GetDefaultRoleId()
        {
            // TODO: Implement logic to get default role ID (e.g., "User" role)
            // For now, return the first non-admin role
            var role = await this.dbContext.Roles
                .Where(x => x.Name.ToLower() != "admin" && x.Name.ToLower() != "superadmin")
                .FirstOrDefaultAsync();

            if (role != null)
            {
                return role.RoleId;
            }

            // If no suitable role exists, get any role
            var anyRole = await this.dbContext.Roles.FirstOrDefaultAsync();
            if (anyRole != null)
            {
                return anyRole.RoleId;
            }

            throw new ApplicationException("No default role found. Please contact administrator.");
        }
    }
}
