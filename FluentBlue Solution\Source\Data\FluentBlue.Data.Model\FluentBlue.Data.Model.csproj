﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Tenants\**" />
    <EmbeddedResource Remove="Tenants\**" />
    <None Remove="Tenants\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.FluentValidation" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.DataAnnotations.Validation" Version="3.2.0-rc1.20223.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NodaTime" Version="3.2.2" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\FluentBlue.Shared\FluentBlue.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Calendar\EventCategoryResource.el.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventCategoryResource.el.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Calendar\EventReminderResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventReminderResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Calendar\EventResource.el.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventResource.el.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactAddressResource.el.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactAddressResource.el.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactCategoryResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactCategoryResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Calendar\EventCategoryResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventCategoryResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Calendar\EventResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Calendar\EventStateResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventStateResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactAddressResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactAddressResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactCategoryResource.el.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactCategoryResource.el.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactEmailResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactEmailResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactEmailResource.el.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactEmailResource.el.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactPhoneResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactPhoneResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactPhoneResource.el.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactPhoneResource.el.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\ContactResource.el.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactResource.el.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Contacts\CountryResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>CountryResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\LanguageResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>LanguageResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Settings\GeneralSettingResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GeneralSettingResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Tenants\BranchDisplayResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>BranchDisplayResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Tenants\CompanyDisplayResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>CompanyDisplayResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Tenants\RoleDisplayResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>RoleDisplayResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Tenants\TenantDisplayResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TenantDisplayResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Tenants\TenantValidationResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TenantValidationResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\Tenants\UserDisplayResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>UserDisplayResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\EmailDisplayResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EmailDisplayResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\EmailTypeResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EmailTypeResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\GeneralDisplayResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GeneralDisplayResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\GeneralValidationResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GeneralValidationResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\PhoneTypeResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>PhoneTypeResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\Calendar\EventCategoryResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EventCategoryResource.el.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Calendar\EventReminderResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Calendar\EventReminderResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EventReminderResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Calendar\EventResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EventResource.el.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactAddressResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactAddressResource.el.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactCategoryResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactCategoryResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Calendar\EventCategoryResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EventCategoryResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Calendar\EventResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EventResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Calendar\EventStateResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Calendar\EventStateResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EventStateResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactAddressResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactAddressResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactCategoryResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactCategoryResource.el.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactEmailResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactEmailResource.el.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactEmailResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactEmailResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactPhoneResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactPhoneResource.el.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactPhoneResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactPhoneResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactResource.el.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\ContactResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\CountryResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Contacts\CountryResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CountryResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EmailDisplayResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EmailTypeResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\GeneralDisplayResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\GeneralValidationResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\LanguageResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\LanguageResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>LanguageResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\PhoneTypeResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Settings\GeneralSettingResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Settings\GeneralSettingResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>GeneralSettingResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\BranchDisplayResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\BranchDisplayResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>BranchDisplayResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\CompanyDisplayResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\CompanyDisplayResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CompanyDisplayResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\RoleDisplayResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\RoleDisplayResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RoleDisplayResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\TenantDisplayResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\TenantDisplayResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TenantDisplayResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\TenantValidationResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TenantValidationResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\UserDisplayResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Tenants\UserDisplayResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>UserDisplayResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EmailDisplayResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EmailDisplayResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EmailTypeResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EmailTypeResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\GeneralDisplayResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>GeneralDisplayResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\GeneralValidationResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>GeneralValidationResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\PhoneTypeResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PhoneTypeResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
