@page "/forgot-password"
@layout EmptyLayout

@using FluentBlue.Shared
@using FluentBlue.WebApi.Client
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<FluentBlue.UI.Main.Pages.Resources.ForgotPasswordResource> forgotPasswordResource
@inject AuthenticationWebApiClient authenticationWebApiClient
@inject NavigationManager navigationManager
@inject ILogger<ForgotPassword> logger
@inject IDialogService dialogService

<div style="background-size: cover; background-position: center; height: 100vh; width: 100%; background-image: url('_content/FluentBlue.UI.Main/images/login-background.png')">
    <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center" VerticalAlignment="VerticalAlignment.Center" Style="height:100vh">
        <FluentCard Width="350px" Height="auto">
            @if (!isSubmitted)
            {
                <EditForm Model="@forgotPasswordData" OnValidSubmit="@OnSubmit">
                    <DataAnnotationsValidator />

                    <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                        <FluentLabel Typo="Typography.H1">@forgotPasswordResource["Title"]</FluentLabel>
                        <FluentLabel Style="text-align: center; margin-bottom: 20px;">@forgotPasswordResource["Subtitle"]</FluentLabel>
                        
                        <FluentTextField Id="emailTxtField" 
                                       @bind-Value="@forgotPasswordData.Email" 
                                       Placeholder="@forgotPasswordResource["EmailPlaceholder"]" 
                                       Label="@forgotPasswordResource["EmailLabel"]"
                                       Maxlength="50" 
                                       Autofocus="true" 
                                       Style="width:85%;" 
                                       Class="mb-1" 
                                       AutoComplete="off"
                                       TextFieldType="TextFieldType.Email">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Mail())" Color="@Color.Neutral" Slot="end" />
                        </FluentTextField>
                        <div style="width:85%;">
                            <FluentValidationMessage For="@(() => forgotPasswordData.Email)" />
                        </div>

                        <FluentButton Type="@ButtonType.Submit" 
                                    Loading="@isLoading" 
                                    Appearance="@Appearance.Accent"
                                    Style="margin-top: 10px;">
                            @forgotPasswordResource["SendResetLinkButton"]
                        </FluentButton>
                        
                        <FluentLabel Color="Color.Warning">@resultMessage</FluentLabel>
                    </FluentStack>
                </EditForm>
            }
            else
            {
                <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                    <FluentLabel Typo="Typography.H1">@forgotPasswordResource["Title"]</FluentLabel>
                    <FluentIcon Value="@(new Icons.Regular.Size48.CheckmarkCircle())" Color="@Color.Success" Style="margin: 20px 0;" />
                    <FluentLabel Style="text-align: center; margin-bottom: 20px;" Color="@Color.Success">
                        @forgotPasswordResource["SuccessMessage"]
                    </FluentLabel>
                </FluentStack>
            }
            
            <br />
            <p style="text-align: center;">
                <FluentAnchor Href="/login" Appearance="Appearance.Hypertext">
                    @forgotPasswordResource["BackToLoginButton"]
                </FluentAnchor>
            </p>
        </FluentCard>
    </FluentStack>
</div>

<FluentDialogProvider />
<FluentTooltipProvider />
<FluentToastProvider MaxToastCount="10" />
