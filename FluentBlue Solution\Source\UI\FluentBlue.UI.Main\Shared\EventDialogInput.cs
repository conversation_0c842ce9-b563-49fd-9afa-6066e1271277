﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.WebApi.Shared.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Shared
{
    public class EventDialogInput
    {
        public required Event Event { get; set; }
        public RecurrentEventHandlingType RecurrentEventHandlingType { get; set; } = RecurrentEventHandlingType.Current;
        public bool Restricted { get; set; } = false;  // If true, then some options/actions are not available in the dialog  

        public EventDialogPreloadedData? PreloadedData { get; set; }
    }
}
