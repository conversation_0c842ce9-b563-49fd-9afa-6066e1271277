using FluentBlue.Shared;
using FluentBlue.UI.Main.Pages.Resources;
using FluentBlue.UI.Main.Shared;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.UI.Main.Pages
{
    public partial class ForgotPassword
    {
        public class ForgotPasswordData
        {
            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [EmailAddress(ErrorMessageResourceName = "InvalidEmailAddress", ErrorMessageResourceType = typeof(Main.GlobalResource))]
            public string Email { get; set; } = string.Empty;
        }

        private ForgotPasswordData forgotPasswordData = new ForgotPasswordData();
        private bool isLoading = false;
        private bool isSubmitted = false;
        private string resultMessage = " ";

        protected override void OnInitialized()
        {
            // Initialize component
        }

        private async void OnSubmit()
        {
            try
            {
                this.isLoading = true;
                this.resultMessage = " ";

                // Call the API to request password reset
                await authenticationWebApiClient.ForgotPasswordAsync(forgotPasswordData.Email);
                
                this.isSubmitted = true;
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
                this.resultMessage = Resources.ForgotPasswordResource.ErrorMessage;
            }
            finally
            {
                this.isLoading = false;
                this.StateHasChanged();
            }
        }


    }
}
