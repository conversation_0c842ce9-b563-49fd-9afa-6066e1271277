-- Migration script to add encryption support to FluentBlue database
-- This script should be run after deploying the new application version

-- Step 1: Update column sizes for encrypted data
-- Contact table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[Contacts].[Contact]') AND name = 'FirstName' AND max_length < 1000)
BEGIN
    ALTER TABLE [Contacts].[Contact] ALTER COLUMN [FirstName] NVARCHAR(500) NOT NULL;
    PRINT 'Updated FirstName column size to 500';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[Contacts].[Contact]') AND name = 'LastName' AND max_length < 1000)
BEGIN
    ALTER TABLE [Contacts].[Contact] ALTER COLUMN [LastName] NVARCHAR(500) NOT NULL;
    PRINT 'Updated LastName column size to 500';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[Contacts].[Contact]') AND name = 'MiddleName' AND max_length < 1000)
BEGIN
    ALTER TABLE [Contacts].[Contact] ALTER COLUMN [MiddleName] NVARCHAR(500) NOT NULL;
    PRINT 'Updated MiddleName column size to 500';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[Contacts].[Contact]') AND name = 'TIN' AND max_length < 1000)
BEGIN
    ALTER TABLE [Contacts].[Contact] ALTER COLUMN [TIN] NVARCHAR(500) NOT NULL;
    PRINT 'Updated TIN column size to 500';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[Contacts].[Contact]') AND name = 'SSN' AND max_length < 1000)
BEGIN
    ALTER TABLE [Contacts].[Contact] ALTER COLUMN [SSN] NVARCHAR(500) NOT NULL;
    PRINT 'Updated SSN column size to 500';
END

-- ContactEmail table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[Contacts].[ContactEmail]') AND name = 'EmailAddress' AND max_length < 1000)
BEGIN
    ALTER TABLE [Contacts].[ContactEmail] ALTER COLUMN [EmailAddress] NVARCHAR(500) NOT NULL;
    PRINT 'Updated EmailAddress column size to 500';
END

-- ContactPhone table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[Contacts].[ContactPhone]') AND name = 'PhoneNumber' AND max_length < 1000)
BEGIN
    ALTER TABLE [Contacts].[ContactPhone] ALTER COLUMN [PhoneNumber] NVARCHAR(500) NOT NULL;
    PRINT 'Updated PhoneNumber column size to 500';
END

PRINT 'Encryption migration completed successfully';
PRINT 'Note: Application encryption key will be generated automatically on first use';
PRINT 'Note: Existing data will be encrypted on first access'; 