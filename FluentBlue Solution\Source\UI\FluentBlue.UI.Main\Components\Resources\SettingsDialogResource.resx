﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Calendar" xml:space="preserve">
    <value>Calendar</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="LanguageTimeTitle" xml:space="preserve">
    <value>Language &amp; Time</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="CalendarWorkDays" xml:space="preserve">
    <value>Work days</value>
  </data>
  <data name="CalendarWorkHours" xml:space="preserve">
    <value>Work hours</value>
  </data>
  <data name="EndHour" xml:space="preserve">
    <value>End hour</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>Friday</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>Monday</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>Saturday</value>
  </data>
  <data name="StartHour" xml:space="preserve">
    <value>Start hour</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>Sunday</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>Thursday</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>Tuesday</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>Wednesday</value>
  </data>
  <data name="FirstDayOfWeek" xml:space="preserve">
    <value>First day of week</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="CalendarTimeScaleInterval" xml:space="preserve">
    <value>Time interval</value>
  </data>
  <data name="CalendarDefaultView" xml:space="preserve">
    <value>Default view</value>
  </data>
  <data name="CalendarDefaultViewOnMobiles" xml:space="preserve">
    <value>Default view on mobiles</value>
  </data>
  <data name="BehaviourTitle" xml:space="preserve">
    <value>Behaviour</value>
  </data>
  <data name="CalendarScrollToTime" xml:space="preserve">
    <value>Scroll to time</value>
  </data>
  <data name="TimeZone" xml:space="preserve">
    <value>Time zone</value>
  </data>
  <data name="CultureInfo" xml:space="preserve">
    <value>Regional Settings</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Roles</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="UsersRoles" xml:space="preserve">
    <value>Users &amp; Roles</value>
  </data>
  <data name="NewUserBtn.Text" xml:space="preserve">
    <value>New User</value>
  </data>
  <data name="UserFullName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="UserEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UserNotExists" xml:space="preserve">
    <value>User does not exist.</value>
  </data>
  <data name="NewRoleBtn.Text" xml:space="preserve">
    <value>New Role</value>
  </data>
  <data name="RoleNotExists" xml:space="preserve">
    <value>Role does not exist.</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>Ρόλος</value>
  </data>
  <data name="EventCategoriesBtn.Text" xml:space="preserve">
    <value>Edit Event Categories</value>
  </data>
  <data name="EventCategoriesTitle" xml:space="preserve">
    <value>Event Categories</value>
  </data>
  <data name="EventStatesBtn.Text" xml:space="preserve">
    <value>Edit Event States</value>
  </data>
  <data name="EventStatesTitle" xml:space="preserve">
    <value>Event States</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="ShowSsnInContactLists" xml:space="preserve">
    <value>Include SSN in lists of contacts</value>
  </data>
  <data name="ShowTinInContactLists" xml:space="preserve">
    <value>Include TIN in lists of contacts</value>
  </data>
  <data name="ContactCategoriesBtn.Text" xml:space="preserve">
    <value>Edit Contact Categories</value>
  </data>
  <data name="ContactCategoriesTitle" xml:space="preserve">
    <value>Contact Categories</value>
  </data>
  <data name="AppearanceTitle" xml:space="preserve">
    <value>Appearance</value>
  </data>
  <data name="DateFormat" xml:space="preserve">
    <value>Date format</value>
  </data>
  <data name="TimeFormat" xml:space="preserve">
    <value>Time format</value>
  </data>
  <data name="LanguageChangeRequiresRestartConfirmation" xml:space="preserve">
    <value>Change of language requires restarting the app. Are you sure you want to change the language?</value>
  </data>
  <data name="EventTextDisplay" xml:space="preserve">
    <value>What to display in event's text</value>
  </data>
  <data name="EventTextSeparator" xml:space="preserve">
    <value>Separator in event's text</value>
  </data>
  <data name="EventTextDisplaySampleTitle" xml:space="preserve">
    <value>Event text sample:</value>
  </data>
</root>