﻿using Android.App;
using Android.Content;
using Android.Gms.Common.Logging;
using AndroidX.Core.App;
using Firebase.Messaging;
using FluentBlue.UI.Devices.Services;
using FluentBlue.WebApi.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices.Platforms.Android.Services
{
    [Service(Exported = true)]
    [IntentFilter(new[] { "com.google.firebase.MESSAGING_EVENT" })]
    public class FirebaseService : FirebaseMessagingService
    {
        private ILogger? logger;

        public FirebaseService()
        {
            logger = MainApplication.Services.GetService<ILogger<FirebaseService>>();
        }
        public override void OnNewToken(string token)
        {
            try
            {
                base.OnNewToken(token);

                //Clears the existing device token if it exists and sets the new one.
                if (Preferences.ContainsKey("DeviceToken"))
                {
                    Preferences.Remove("DeviceToken");
                }
                Preferences.Set("DeviceToken", token);

                Task.Run(() => RegisterDeviceToken(token));  //Register the device token to database for the user.
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Failed to register device token.");
            }
        }

        private async Task RegisterDeviceToken(string token)
        {
            try
            {
                var registrationService = MainApplication.Services.GetService<UserDevicesWebApiClient>();
                if (registrationService != null)
                {
                    await registrationService.RegisterDeviceAsync(token, DeviceInfo.Platform.ToString());
                    logger?.LogInformation("Device token registered successfully.");
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Failed to register device token.");
            }
        }

        public override void OnMessageReceived(RemoteMessage message)
        {
            try
            {
                base.OnMessageReceived(message);

                RemoteMessage.Notification? notification = message.GetNotification();

                if (notification != null)
                {
                    ShowNotification(notification!.Body ?? "", notification!.Title ?? "", message.Data);
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Failed to register device token.");
            }
        }

        private void ShowNotification(string messageBody, string title, IDictionary<string, string> data)
        {
            try
            {
                var intent = new Intent(this, typeof(MainActivity));
                intent.AddFlags(ActivityFlags.ClearTop);
                intent.AddFlags(ActivityFlags.SingleTop);


                foreach (var key in data.Keys)
                {
                    string value = data[key];
                    intent.PutExtra(key, value);
                }

                var pendingIntent = PendingIntent.GetActivity(this,
                    MainActivity.notificationID, intent, PendingIntentFlags.OneShot | PendingIntentFlags.Immutable);

                var notificationBuilder = new NotificationCompat.Builder(this, MainActivity.channelID)
                    .SetContentTitle(title)
                    .SetSmallIcon(Resource.Mipmap.appicon)
                    .SetContentText(messageBody)
                    .SetChannelId(MainActivity.channelID)
                    .SetContentIntent(pendingIntent)
                    .SetAutoCancel(true)
                    .SetPriority((int)NotificationPriority.Max);

                var notificationManager = NotificationManagerCompat.From(this);
                notificationManager.Notify(MainActivity.notificationID, notificationBuilder.Build());
            }
            catch (Exception ex)
            {
                // Log the error but don't let it stop the main function
                logger?.LogError(ex, $"Failed to send notification: {ex.Message}");
            }
        }
    }
}
