﻿namespace FluentBlue.UI.Web
{
    public partial class App
    {
        private bool IsPublicRoute(Type pageType)
        {
            // Define which pages should be accessible without authentication
            var publicPages = new[]
            {
                typeof(Main.Pages.Login),
                typeof(Main.Pages.ForgotPassword),
                typeof(Main.Pages.Register),
                typeof(Main.Pages.ResetPassword)
            };

            return publicPages.Contains(pageType);
        }
    }
}
