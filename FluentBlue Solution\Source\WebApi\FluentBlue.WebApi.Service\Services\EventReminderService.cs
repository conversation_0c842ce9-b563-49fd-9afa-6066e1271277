﻿using Azure.Core;
using FluentBlue.Application.Business;
using FluentBlue.Data.Model;
using FluentBlue.Shared.SignalR;
using FluentBlue.WebApi.Service.Controllers;
using FluentBlue.WebApi.Service.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System;

namespace FluentBlue.WebApi.Service.Services
{
    public class EventReminderService : BackgroundService
    {
        private IServiceProvider serviceProvider;
        private IHubContext<NotificationHub> notificationHub;
        private IEventsBusiness eventsBusiness;
        private ILogger<EventReminderService> logger;
        private IWebApiCallerInfo webApiCallerInfo;
        private FcmPushNotificationService fcmPushNotificationService;

        public EventReminderService(IServiceProvider serviceProvider, IHubContext<NotificationHub> notificationHub, FcmPushNotificationService fcmPushNotificationService, ILogger<EventReminderService> logger)
        {
            this.serviceProvider = serviceProvider;
            //this.webApiCallerInfo = webApiCallerInfo;
            this.notificationHub = notificationHub;
            //this.eventsBusiness = eventsBusiness;
            this.logger = logger;
            this.fcmPushNotificationService = fcmPushNotificationService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await CheckRemindersAsync();
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Check every minute
            }
        }

        private async Task CheckRemindersAsync()
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                FluentBlueDbContext context = scope.ServiceProvider.GetRequiredService<Data.Model.FluentBlueDbContext>();
                IWebApiCallerInfo webApiCallerInfo = scope.ServiceProvider.GetRequiredService<IWebApiCallerInfo>();

                var utcNow = DateTime.UtcNow;
                var reminders = await context.EventReminders.Include(x => x.Event).ThenInclude(x => x.EventUsers).Where(x => x.PushSent == false && x.ReminderTimeUtc <= utcNow && x.ReminderTimeUtc >= utcNow.AddMinutes(-15))
                    .ToListAsync();

                foreach (var reminder in reminders)
                {
                    #region  Notifies User about Event with SignalR.
                    foreach (Guid userId in reminder.Event!.EventUserIds)
                    {
                        try
                        {
                            //Send SignalR notification to clients
                            NotificationData notificationData = new NotificationData();
                            notificationData.NotificationType = "EventReminder";
                            notificationData.Title = reminder.Event!.Subject;
                            notificationData.Message = reminder.Event!.Description;
                            notificationData.SenderId = webApiCallerInfo.ApplicationId?.ToString() ?? "";
                            string notificationDataJson = System.Text.Json.JsonSerializer.Serialize(notificationData);  //convert payload to JSON 
                            await notificationHub.Clients.Group(userId.ToString()).SendAsync("FluentBlueNotification", notificationDataJson);
                        }
                        catch (Exception ex)
                        {
                            this.logger.LogError(ex, ex.Message, $"Event reminder with SignalR failed for EventID='{reminder.EventId}'");
                        }
                    }
                    #endregion

                    #region  Notifies User about Event with Push Notification.
                    try
                    {
                        // Assuming the Event entity has a property linking it to a user, e.g., CreatedByUserId
                        foreach (Guid userId in reminder.Event!.EventUserIds)
                        {
                            var deviceTokens = await context.UserDevices
                                .Where(d => d.UserId == userId)
                                .Select(d => d.DeviceToken)
                                .ToListAsync();

                            if (!deviceTokens.Any())
                            {
                                logger.LogWarning($"No device tokens found for user '{userId}' for EventId='{reminder.EventId}'");
                                continue;
                            }

                            string body = reminder!.Event!.PlainDescription.Substring(0, reminder.Event.PlainDescription.Length > 50 ? 50 : reminder.Event.PlainDescription.Length);
                            Dictionary<string, string> data = new Dictionary<string, string>
                            {
                                { "EventId", reminder!.EventId!.ToString()! }
                            };

                            foreach (var token in deviceTokens)
                            {
                                await this.fcmPushNotificationService.SendPushNotificationAsync(token, reminder.Event.Subject, body, data);
                            }

                            logger.LogInformation($"Push notification sent successfully for EventId='{reminder.EventId}'");
                        }

                        await context.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        // Log the error but don't let it stop the main function
                        logger.LogWarning(ex, $"Push notification failed for EventId='{reminder.EventId}'");
                    }
                    #endregion

                    try
                    {
                        reminder.PushSent = true;
                        reminder.ObjectState = ObjectState.Modified;
                        await context.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        this.logger.LogError(ex, ex.Message, $"Push notification failed to be saved for EventID='{reminder.EventId}'");
                    }
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                logger.LogError(ex, ex.Message);
                //new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    logger.LogError(ex, ex.Message);
                    //new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    //new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                //new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
    }
}
