﻿CREATE TABLE [Tenants].[Tenant] (
    [TenantId]        UNIQUEIDENTIFIER CONSTRAINT [DF_Tenant_TenantId1] DEFAULT (newsequentialid()) NOT NULL,
    [Name]            NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Tenant_Name] DEFAULT (N'') NOT NULL,
    [Email]           NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Tenant_Email] DEFAULT (N'') NOT NULL,
    [DateCreatedUtc]  DATETIME         CONSTRAINT [DF_Tenant_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc] DATETIME         CONSTRAINT [DF_Tenant_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_Tenant_1] PRIMARY KEY CLUSTERED ([TenantId] ASC)
);



GO


GO
