﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public static class EnumExtensions
    {
        public static CultureInfo GetCultureInfo(this Language language)
        {
            switch (language)
            {
                case Language.EnglishGeneral:
                    {
                        CultureInfo cultureInfo = new CultureInfo("en-001"); //CultureInfo.InvariantCulture;

                        return cultureInfo;
                    }
                case Language.GreekGeneral:
                    {
                        CultureInfo cultureInfo = new CultureInfo("el");

                        return cultureInfo;
                    }
                default:
                    {
                        CultureInfo cultureInfo = new CultureInfo("en-001");  //CultureInfo.InvariantCulture;

                        return cultureInfo;
                    }
            }
        }

        public static int GetMinutes(this ReminderTime reminderTime)
        {
            switch (reminderTime)
            {
                case ReminderTime.ZeroMinutes:
                    {
                        return 0;
                    }
                case ReminderTime.FiveMinutes:
                    {
                        return 5;
                    }
                case ReminderTime.FifteenMinutes:
                    {
                        return 15;
                    }
                case ReminderTime.ThirtyMinutes:
                    {
                        return 30;
                    }
                case ReminderTime.OneHour:
                    {
                        return 60;
                    }
                default:
                    {
                        return 0;
                    }
            }
        }

        public static string GetValue(this EventTextSeparator language)
        {
            switch (language)
            {
                case EventTextSeparator.Space:
                    {
                        return " ";
                    }
                case EventTextSeparator.Comma:
                    {
                        return ", ";
                    }
                case EventTextSeparator.Dash:
                    {
                        return " - ";
                    }
                case EventTextSeparator.Pipe:
                    {
                        return " | ";
                    }
                default:
                    {
                        return " ";
                    }
            }
        }
    }
}
