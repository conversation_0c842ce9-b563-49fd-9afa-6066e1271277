﻿@implements IDialogContentComponent<List<Data.Model.DBOs.Contacts.ContactCategory>>

@using Blazored.FluentValidation
@using FluentBlue.Data.Model.DBOs.Contacts
@using FluentBlue.Shared.Utilities
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Caching.Hybrid
@using Microsoft.JSInterop

@inject HttpClient httpClient
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject HybridCache cache
@inject ILogger<FluentBlue.UI.Main.Components.ContactCategoriesDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.ContactCategoriesWebApiClient> contactCategoriesWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject IFormFactor formFactor
@inject IJSRuntime JS

<FluentDialogHeader Class="hidden"></FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto">
    <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4">@Resources.ContactCategoriesDialogResource.Name</FluentLabel>
    <EditForm id="editForm" Model="@Content">
        <ChildContent Context="context2">
            <FluentValidationValidator @ref="fluentValidationValidator" />

            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                <FluentButton IconStart="@(new Icons.Regular.Size16.Add())" Appearance="Appearance.Accent" OnClick="AddBtnOnClick"><span>@Resources.ContactCategoriesDialogResource.AddBtn_Text</span></FluentButton>
                @*  <FluentButton IconStart="@(new Icons.Regular.Size16.Save())" OnClick="SaveBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.ContactCategoriesDialogResource.SaveBtn_Text</span></FluentButton>
                <FluentButton IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.ContactCategoriesDialogResource.CancelBtn_Text</span></FluentButton>*@ 
            </FluentToolbar>

            @if (Content != null && Content.Count > 0)
            {
                <FluentSortableList TItem="ContactCategory" Items="@Content" Fallback="true" Handle="true" Context="contactCategoryContext" OnUpdate="HandleReorder">
                    <ItemTemplate>
                        <div class="sortable-grab cursor-move">
                            <FluentIcon Value="@(new Icons.Regular.Size16.ChevronUpDown())" Color="Color.Neutral" />
                        </div>
                        <div class="sortable-item-content flex flex-1 gap-2 items-center p-1 overflow-hidden" style="height:31px">
                            <div class="flex-1">
                                <FluentTextField @bind-Value="@contactCategoryContext.Name" Appearance="FluentInputAppearance.Filled" Placeholder="@Resources.ContactCategoriesDialogResource.NamePlaceholder" Maxlength="50" Class="flex-1" AutoComplete="off" />
                            </div>
                            @* <FluentTextField Id="@contactCategoryContext.ContactCategoryId.ToString()" @bind-Value="@contactCategoryContext.Color" Appearance="FluentInputAppearance.Filled" TextFieldType="TextFieldType.Color" Class="flex-none w-12" AutoComplete="off" /> *@
                            <FluentIcon Id="@contactCategoryContext.ContactCategoryId.ToString()" Value="@(new Icons.Filled.Size28.Square())" Color="Color.Custom" CustomColor="@contactCategoryContext.Color" />
                            <FluentMenu Anchor="@contactCategoryContext.ContactCategoryId.ToString()" Trigger="MouseButton.Left" VerticalPosition="VerticalPosition.Center" Style="height: 150px !important; overflow-y: scroll !important; scrollbar-width: thin !important;">
                                @foreach (EventCategoryColor eventCategoryColor in this.predefinedColors)
                                {
                                    <FluentMenuItem OnClick="@((e) => {contactCategoryContext.Color = eventCategoryColor.Color;})">
                                        <div slot="start" class="flex items-center"><FluentIcon Value="@(new Icons.Filled.Size16.Square())" Color="Color.Custom" CustomColor="@eventCategoryColor.Color" Class="mr-2" Slot="start" /> @eventCategoryColor.Name</div>
                                    </FluentMenuItem>
                                }
                            </FluentMenu>
                            <FluentButton IconStart="@(new Icons.Regular.Size16.Delete())" OnClick="() => DeleteBtnOnClick(contactCategoryContext)" BackgroundColor="transparent" Class="flex-none w-8" />
                        </div>
                    </ItemTemplate>
                </FluentSortableList>
            }
        </ChildContent>
    </EditForm>
</FluentDialogBody>

<FluentDialogFooter>
    <FluentStack Orientation="Orientation.Vertical" VerticalAlignment="VerticalAlignment.Bottom">
        <FluentDivider Class="w-full" Role="DividerRole.Presentation"></FluentDivider>
        <FluentStack Orientation="Orientation.Horizontal" HorizontalAlignment="HorizontalAlignment.Right" VerticalAlignment="VerticalAlignment.Bottom" Class="self-end">
            <FluentButton Loading="@this.isSaving" IconStart="@(new Icons.Regular.Size16.Save())" Appearance="Appearance.Accent" OnClick="SaveBtnOnClick"><span>@GlobalResource.Save</span></FluentButton>
            <FluentButton Disabled="@this.isSaving" IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtnOnClick"><span>@GlobalResource.Cancel</span></FluentButton>
        </FluentStack>
    </FluentStack>
</FluentDialogFooter>

