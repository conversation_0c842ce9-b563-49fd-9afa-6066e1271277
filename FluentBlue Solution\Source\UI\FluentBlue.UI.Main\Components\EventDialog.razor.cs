using Blazored.FluentValidation;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared.Utilities;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Pages;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using FluentBlue.WebApi.Shared.Request;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Schedule;
using System.Diagnostics;

namespace FluentBlue.UI.Main.Components
{
    public partial class EventDialog
    {
        //Event
        //[Parameter] public string? EventId { get; set; }
        [Parameter] public EventDialogInput Content { get; set; } = default!;
        private List<Data.Model.DTOs.UserLI>? users;

        List<Data.Model.DBOs.Calendar.EventCategory> eventCategories = new List<Data.Model.DBOs.Calendar.EventCategory>();
        private EditContext eventContext = new EditContext(typeof(Data.Model.DBOs.Calendar.Event));
        private ContactLI selectedContact = ContactLI.Empty;
        private EventCategory? selectedCategory = EventCategory.Empty;
        private IEnumerable<UserLI>? selectedEventUsers = new List<UserLI>();
        List<Data.Model.DBOs.Calendar.EventState> eventStates = new List<Data.Model.DBOs.Calendar.EventState>();
        private EventState? selectedState = EventState.Empty;
        private FluentAutocomplete<UserLI> userSelect = default!;
        private string initialRecurrenceRule = string.Empty;  //"FREQ=DAILY;INTERVAL=2;COUNT=8;";
        //private string? newRecurrenceRule = null;
        private List<DateTime> newRecurrenceDates = new List<DateTime>();
        private ReminderTime? selectedReminder = null;
        private List<ReminderTime?> reminderTimes = Enum.GetValues<ReminderTime>().Cast<ReminderTime?>().OrderBy(x => x).ToList();
        //private static readonly HybridCacheEntryOptions userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

        FluentAutocomplete<ContactLI> contactCmbBox = default!;
        private List<ContactLI>? contacts;  // All Contacts to be used in controls
        private List<ContactLI>? filteredContacts;  // Filtered Contacts for the ComboBox
        IEnumerable<ContactLI> selectedContacts = new List<ContactLI>().AsEnumerable();  //Οι Contacts που έχει επιλέξει (στο dropdown) ο χρήστης.
        private bool showTinInContactLists = false;
        private bool showSsnInContactLists = false;

        //General
        [CascadingParameter]
        public FluentDialog CurrentDialog { get; set; } = default!;
        IDialogReference? dialog;
        private FluentValidationValidator? fluentValidationValidator = new FluentValidationValidator();
        private FluentValidation.IValidator? validator = new InsertUpdateEventValidator();

        private UserSetting? userSetting = null;
        private List<ToolbarItemModel> toolbarItems = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.StrikeThrough },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.FontColor },
            new ToolbarItemModel() { Command = ToolbarCommand.BackgroundColor },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.Formats },
            new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.Outdent },
            new ToolbarItemModel() { Command = ToolbarCommand.Indent },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
            //new ToolbarItemModel() { Command = ToolbarCommand.Image },
            new ToolbarItemModel() { Command = ToolbarCommand.CreateTable },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo }
        };
        private bool isSaving = false;
        private bool isDeleting = false;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    Console.WriteLine("EventDialog OnAfterRenderAsync firstRender");
                    this.initialRecurrenceRule = this.Content!.Event!.CustomRecurrenceRule;  //Store the initial RecurrenceRule

                    this.messageService.Clear();  //Καθαρίζει messages που μπορεί να υπάρχουν από πριν.
                    //Αν έχουμε ένα επαναλαμβανόμενο Event, τότε εμφανίζει το αντίστοιχο μήνυμα στον χρήστη για να γνωρίζει τι είδους recurring event επεξεργάζεται.
                    if (this.Content!.Event!.CustomRecurrenceRule != "")
                    {
                        if (this.Content!.RecurrentEventHandlingType == WebApi.Shared.Request.RecurrentEventHandlingType.Current)
                        {
                            await messageService.ShowMessageBarAsync(@Resources.EventDialogResource.EditingSingleRecurrentEventMessage, MessageIntent.Info, "MESSAGES_TOP");
                        }
                        else if (this.Content!.RecurrentEventHandlingType == WebApi.Shared.Request.RecurrentEventHandlingType.CurrentAndFollowing)
                        {
                            await messageService.ShowMessageBarAsync(@Resources.EventDialogResource.EditingCurrentAndFollowingRecurrentEventsMessage, MessageIntent.Info, "MESSAGES_TOP");
                        }
                        else if (this.Content!.RecurrentEventHandlingType == WebApi.Shared.Request.RecurrentEventHandlingType.All)
                        {
                            await messageService.ShowMessageBarAsync(@Resources.EventDialogResource.EditingAllRecurrentEventsMessage, MessageIntent.Info, "MESSAGES_TOP");
                        }
                    }

                    this.reminderTimes.Insert(0, null);

                    ////Γεμίζει με data το combobox για τον User.
                    //Task<List<Data.Model.DTOs.UserLI>> usersTask = new WebApi.Client.UsersWebApiClient(httpClient, usersWebApiClientLogger).GetUsersList(AuthenticatedUserData.TenantId);
                    //this.users = await usersTask;
                    ////this.users.Insert(0, UserLI.Empty);
                    //Console.WriteLine("Point 2");
                    //// Reads the UserSettings from cache or database
                    ////var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
                    //this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                    //    async cancel =>
                    //    {
                    //        UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    //        return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                    //    }, userSettingCacheOptions);
                    //Console.WriteLine("Point 3");

                    ////Γεμίζει με data το combobox για το Contact.
                    //Task<List<Data.Model.DTOs.ContactLI>> contactsTask = new WebApi.Client.ContactsWebApiClient(httpClient, contactsWebApiClientLogger).GetContactsList(AuthenticatedUserData.TenantId);
                    //this.contacts = await contactsTask;
                    //this.contacts.Insert(0, ContactLI.Empty);
                    //Console.WriteLine("Point 4");

                    ////Γεμίζει με data το combobox για το EventCategory.
                    //EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                    //this.eventCategories = await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                    //this.eventCategories.Insert(0, EventCategory.Empty);
                    //Console.WriteLine("Point 5");

                    ////Γεμίζει με data το combobox για το EventState.
                    //EventStatesWebApiClient eventStatesWebApiClient = new EventStatesWebApiClient(httpClient, eventStatesWebApiClientLogger);
                    //this.eventStates = await eventStatesWebApiClient.GetAllEventStates(AuthenticatedUserData.TenantId);
                    //this.eventStates.Insert(0, EventState.Empty);

                    // Use preloaded data if available, otherwise load from cache/API
                    await LoadDataAsync();

                    // Reads the UserSettings from cache or database
                    //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, PredefinedHybridCacheOptions.UserSettingCacheOptions);

                    //Ρυθμίζει τα controls
                    this.showTinInContactLists = this.userSetting?.ShowTinInContactLists ?? false;
                    this.showSsnInContactLists = this.userSetting?.ShowSsnInContactLists ?? false;
                    Console.WriteLine("Point 6");
                    this.SetDataToUI();
                    StateHasChanged();
                    Console.WriteLine("Point 7");
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task LoadDataAsync()
        {
            if (Content?.PreloadedData != null)
            {
                // Use preloaded data
                this.users = Content.PreloadedData.Users;
                this.contacts = Content.PreloadedData.Contacts;
                this.eventCategories = Content.PreloadedData.EventCategories;
                this.eventStates = Content.PreloadedData.EventStates;
            }
            else
            {
                // Create tasks for parallel execution, converting ValueTask to Task
                var usersListTask = cache.GetOrCreateAsync(Keywords.UsersList, async _ =>
                {
                    var usersWebApiClient = new WebApi.Client.UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    return await usersWebApiClient.GetUsersList(AuthenticatedUserData.TenantId);
                }, PredefinedHybridCacheOptions.UsersListCacheOptions, new[] { "Deletable" }).AsTask();

                //var contactsListTask = cache.GetOrCreateAsync(Keywords.ContactsList, async _ =>
                //{
                //    var contactsWebApiClient = new WebApi.Client.ContactsWebApiClient(httpClient, contactsWebApiClientLogger);
                //    return await contactsWebApiClient.GetContactsList(AuthenticatedUserData.TenantId);
                //}, PredefinedHybridCacheOptions.ContactsListCacheOptions, new[] { "Deletable" }).AsTask();

                var contactsListTask = Task.Run(async () =>
                {
                    var contactsWebApiClient = new WebApi.Client.ContactsWebApiClient(httpClient, contactsWebApiClientLogger);
                    return await contactsWebApiClient.GetContactsList(AuthenticatedUserData.TenantId);
                });

                var eventCategoriesTask = cache.GetOrCreateAsync(Keywords.EventCategories, async _ =>
                {
                    var eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                    return await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                }, PredefinedHybridCacheOptions.EventCategoriesCacheOptions, new[] { "Deletable" }).AsTask();

                var eventStatesTask = cache.GetOrCreateAsync(Keywords.EventStates, async _ =>
                {
                    var eventStatesWebApiClient = new EventStatesWebApiClient(httpClient, eventStatesWebApiClientLogger);
                    return await eventStatesWebApiClient.GetAllEventStates(AuthenticatedUserData.TenantId);
                }, PredefinedHybridCacheOptions.EventStatesCacheOptions, new[] { "Deletable" }).AsTask();

                // Execute all tasks in parallel
                await Task.WhenAll(usersListTask, contactsListTask, eventCategoriesTask, eventStatesTask);

                this.users = await usersListTask;
                this.contacts = await contactsListTask;
                this.eventCategories = await eventCategoriesTask;
                this.eventStates = await eventStatesTask;
            }

            // Add empty items for dropdowns
            this.contacts?.Insert(0, ContactLI.Empty);
            this.eventCategories?.Insert(0, EventCategory.Empty);
            this.eventStates?.Insert(0, EventState.Empty);
        }

        private async Task SaveBtnOnClick()
        {
            try
            {
                await this.Save(true);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CloseBtnOnClick()
        {
            try
            {
                await CurrentDialog.CancelAsync();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task DeleteBtnOnClick()
        {
            try
            {
                this.isDeleting = true;
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                if (result.Cancelled == false)
                {
                    EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                    await eventsWebApiClient.DeleteEvent(this.Content!.Event!.EventId, null);
                    await CurrentDialog.CloseAsync(Content.Event);
                }
            }
            catch (ApplicationException ex)
            {
                this.isDeleting = false;
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                    await CurrentDialog.CloseAsync(Content.Event);
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
                this.isDeleting = false;
            }
        }

        private async Task Save(bool close)
        {
            try
            {
                this.isSaving = true;

                this.GetDataFromUI();

                if (this.Content!.Event!.ObjectState != ObjectState.Added)
                {
                    this.Content!.Event!.ObjectState = ObjectState.Modified;
                }

                if (await this.ValidateData())
                {
                    //If we are going to create a new Event.
                    if (this.Content.Event.ObjectState == ObjectState.Added)
                    {
                        EventsWebApiClient eventsWebApiClientBatch = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                        await eventsWebApiClientBatch.CreateEvent(this.Content.Event, this.newRecurrenceDates);
                    }
                    //If we are going to update Event, then we will ask user the type of update depending on the recurrence changes.
                    if (this.Content.Event.ObjectState == ObjectState.Modified)
                    {
                        ////Explanation: Asks user how he wants to update the recurrent event, based on the following cases:
                        //// Case 1) If Event was recurrent and continue to be recurrence (recurrence may change or not).
                        //// Case 2) If Event was recurrent but the recurrence is removed.

                        //RecurrentEventUpdateType? recurrentEventUpdateType = null;

                        //bool recurrenceRulePossibleChange = initialRecurrenceRule != "" && this.Content.Event!.CustomRecurrenceRule != "";
                        //bool recurrenceRuleWasRemoved = initialRecurrenceRule != "" && this.Content.Event!.CustomRecurrenceRule == "";

                        //if (recurrenceRulePossibleChange)
                        //{
                        //    var parameters = new DialogParameters()
                        //    {
                        //        Title = "",
                        //        PreventDismissOnOverlayClick = true,
                        //        PrimaryAction = "",
                        //        SecondaryAction = "",
                        //        //Width = dialogWidth,
                        //        //Height = dialogHeight,
                        //        Modal = true,
                        //        ShowDismiss = false
                        //    };
                        //    var dialog = await dialogService.ShowDialogAsync<UpdateRecurrentEventDialog>(parameters);
                        //    var dialogResult = await dialog.Result;

                        //    if (dialogResult.Cancelled)  //If user wants to cancel the update.
                        //    {
                        //        return;
                        //    }
                        //    else
                        //    {
                        //        recurrentEventUpdateType = (RecurrentEventUpdateType)dialogResult.Data!;
                        //    }
                        //}
                        //else if (recurrenceRuleWasRemoved)
                        //{
                        //    //recurrentEventUpdateType = RecurrentEventUpdateType.
                        //}

                        ////If user selected RecurrentEventHandlingType CurrentAndFollowing or All, then we need to create the new recurrence dates. So the old recurrence dates will be deleted and the new ones will be created.
                        //List<DateTime>? recurrenceDates = null;
                        //if (this.Content!.RecurrentEventHandlingType == WebApi.Shared.Request.RecurrentEventHandlingType.CurrentAndFollowing || this.Content!.RecurrentEventHandlingType == WebApi.Shared.Request.RecurrentEventHandlingType.All)
                        //{
                        //    int firstDayOfWeekInt = (int)((DayOfWeek)this.userSetting!.FirstDayOfWeek);

                        //    recurrenceDates = RecurrenceHelper.GetRecurrenceDateTimeCollection(this.Content!.Event.CustomRecurrenceRule, "", firstDayOfWeekInt, this.Content!.Event!.StartTimeLocal!.Value, this.Content!.Event!.EndTimeLocal!.Value, null, null, 30).ToList();
                        //}

                        EventsWebApiClient eventsWebApiClientBatch = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                        await eventsWebApiClientBatch.UpdateEvent(this.Content.Event, this.Content!.RecurrentEventHandlingType, this.newRecurrenceDates);
                    }

                    if (close)
                    {
                        await CurrentDialog.CloseAsync(Content.Event);
                    }
                    else
                    {
                        this.Content.Event!.ObjectState = ObjectState.Unchanged;

                        this.StateHasChanged();
                    }
                }
            }
            //catch (Exception ex)
            //{
            //    logger.LogError(ex, ex.Message);
            //    throw;
            //}
            finally
            {
                this.isSaving = false;
            }
        }

        private async Task<bool> ValidateData()
        {
            try
            {
                int firstDayOfWeekInt = (int)((DayOfWeek)this.userSetting!.FirstDayOfWeek);

                bool valid = this.fluentValidationValidator!.Validate();
                if (valid == false)
                {
                    // Convert error messages to HTML bullet list
                    string errorMessage = GlobalResource.CorrectInvalidFields;
                    RenderFragment errorRF = FluentBlue.Shared.Utilities.ValidationErrorsToBulletsConverter.ConvertValidationErrorsToBullets(errorMessage, this.fluentValidationValidator.GetFailuresFromLastValidation().Select(e => e.ErrorMessage).ToArray(), "");

                    // Show errors in dialog
                    await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                    {
                        ShowTitle = false,
                        ShowDismiss = false,
                        DialogType = Microsoft.FluentUI.AspNetCore.Components.DialogType.MessageBox,
                        PrimaryAction = UI.Main.GlobalResource.Close,
                        SecondaryAction = "",
                        Modal = true,
                        PreventDismissOnOverlayClick = true
                    });
                }

                //If we have a recurrence.
                if (this.Content.Event!.CustomRecurrenceRule != "")
                {
                    //Creates again the new recurrence dates in case user changed dates/times.
                    this.newRecurrenceDates = RecurrenceHelper.GetRecurrenceDateTimeCollection(this.Content.Event!.CustomRecurrenceRule, "", firstDayOfWeekInt, this.Content.Event!.StartTimeLocal!.Value, this.Content.Event!.EndTimeLocal!.Value, null, null, 50).ToList();

                    //Checks for overlapping events.
                    TimeSpan duration = this.Content.Event!.EndTimeUtc - this.Content.Event!.StartTimeUtc;
                    var recurrenceDateTimeRanges = this.newRecurrenceDates.Select(date => (startDateTime: date, endDateTime: date.Add(duration))).ToList();   // Converts recurrenceDates to List<(startDateTime, endDateTime)> by using duration

                    if (DateTimeUtilities.HasOverlappingDates(recurrenceDateTimeRanges))
                    {
                        dialogService.ShowInfo(Resources.EventDialogResource.RecurrencesOverlapping, "", GlobalResource.Close);
                        return false;
                    }
                }

                return valid;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private void GetDataFromUI()
        {
            //TODO: Αυτή η function υπάρχει για ορισμένα controls όπως τα FluentCombobox όπου το @bind-Value δεν δουλεύει σωστά και αντί για το Value επιστρέφει το Text του control.
            //Αυτό θα πρέπει να αλλάξει και να φύγει η function αυτή.
            this.Content!.Event!.ContactId = this.selectedContact?.ContactId;
            this.Content!.Event!.EventCategoryId = this.selectedCategory?.EventCategoryId; //this.selectedCategory?.EventCategoryId == Guid.Empty ? null : this.selectedCategory!.EventCategoryId;
            this.Content!.Event!.EventStateId = this.selectedState?.EventStateId;

            #region Διαβάζει τα EventUsers
            //Δημιουργεί τα EventUsers που προστέθηκαν.
            if (this.selectedEventUsers != null)
            {
                foreach (UserLI user in this.selectedEventUsers)
                {
                    //Αν ο χρήστης δεν έχει εισαχθει στο EventUsers, τότε το δημιουργει.
                    if (!this.Content.Event.EventUsers.Where(x => x.UserId == user.UserId).Any())
                    {
                        EventUser eventUser = this.Content.Event.AddNewEventUser(user.UserId);
                        //eventUser.EventId = this.Content.EventEventId;
                        //eventUser.UserId = user.UserId;
                    }
                }
            }

            //Διαγράφει τα EventUsers που προστέθηκαν.
            foreach (EventUser eventUser in this.Content.Event.EventUsers.ToList())
            {
                //Αν δεν υπάρχει στους επιλεγμένους Users.
                if (!this.selectedEventUsers!.Where(x => x.UserId == eventUser.UserId).Any())
                {
                    eventUser.ObjectState = ObjectState.Deleted;
                }
            }
            #endregion

            #region  Φτιάχνει το Reminder
            if (this.selectedReminder != null)
            {
                this.Content!.Event.SetReminder(selectedReminder.Value);

                StateHasChanged();
            }
            else
            {
                this.Content!.Event.RemoveReminder();
            }
            #endregion
        }

        private void SetDataToUI()
        {
            this.selectedContact = this.contacts!.Where(x => x.ContactId == this.Content!.Event!.ContactId).FirstOrDefault()!;
            this.selectedCategory = this.eventCategories!.Where(x => x.EventCategoryId == (this.Content.Event!.EventCategoryId ?? Guid.Empty)).FirstOrDefault();
            this.selectedState = this.eventStates!.Where(x => x.EventStateId == (this.Content!.Event!.EventStateId ?? Guid.Empty)).FirstOrDefault();

            #region  EventUsers
            //Δημιουργεί τα EventUsers που προστέθηκαν.
            foreach (EventUser eventUser in this.Content.Event!.EventUsers.ToList())
            {
                UserLI? user = this.users!.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                if (user != null)
                {
                    this.selectedEventUsers = this.selectedEventUsers!.Append(user);
                }
            }
            //this.StateHasChanged();
            #endregion

            this.selectedReminder = Content.Event.EventReminders != null && Content.Event.EventReminders.Count > 0 ? Content.Event.EventReminders.FirstOrDefault()?.ReminderOffset : null;
        }

        private async Task StartDate_ValueChanged(DateTime? value)
        {
            try
            {
                if (value.HasValue)  //Με αυτό τον έλεγχο δεν επιτρέπουμε στο χρήστη να αφήσει την ημερομηνία κενή.
                {
                    TimeSpan timeSpan = this.Content!.Event!.EndTimeUtc - this.Content.Event!.StartTimeUtc;  //Κάνουμε την διαφορα σε Utc

                    this.Content.Event!.StartTimeLocal = value.Value;
                    this.Content.Event!.EndTimeLocal = value.Value.Add(timeSpan);
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task StartTime_ValueChanged(DateTime? value)
        {
            //TODO: H function StartTime_ValueChanged πρέπει να φύγει γιατί είναι ίδια με την StartDate_ValueChanged και να χρησιμοποιηθεί η 2η.
            try
            {
                if (value.HasValue)  //Με αυτό τον έλεγχο δεν επιτρέπουμε στο χρήστη να αφήσει την ημερομηνία κενή.
                {
                    TimeSpan timeSpan = this.Content!.Event!.EndTimeUtc! - this.Content.Event!.StartTimeUtc!;  //Υπολογίζει την διάρκεια σε UTC

                    this.Content.Event!.StartTimeLocal = value.Value;
                    this.Content.Event!.EndTimeLocal = value.Value.Add(timeSpan);
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task EndDate_ValueChanged(DateTime? value)
        {
            try
            {
                if (value.HasValue)  //Με αυτό τον έλεγχο δεν επιτρέπουμε στο χρήστη να αφήσει την ημερομηνία κενή.
                {
                    this.Content.Event!.EndTimeLocal = value.Value;
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task Contact_SelectedOptionChanged(ContactLI? contact)
        {
            try
            {
                this.Content.Event!.ContactId = contact.ContactId;
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task OnCategorySelect(string value)
        {
            try
            {
                this.Content!.Event!.EventCategoryId = value == null ? null : Guid.Parse(value);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task NewContactBtnOnClick()
        {
            try
            {
                Contact contact = Contact.CreateContact(AuthenticatedUserData.TenantId);
                await this.ShowContact(contact);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task EditContactBtnOnClick()
        {
            try
            {
                //Αν έχει επιλεγεί Contact από το DropDown.
                if (this.contactCmbBox.SelectedOption != null && this.contactCmbBox.SelectedOption!.ContactId != Guid.Empty)
                {
                    ContactsWebApiClient contactsWebApiClient = new ContactsWebApiClient(httpClient, contactsWebApiClientLogger);
                    Data.Model.DBOs.Contacts.Contact? contact = await contactsWebApiClient.GetContact(this.contactCmbBox.SelectedOption!.ContactId);

                    if (contact != null)
                    {
                        contact.UserTimeZoneId = this.userSetting!.TimeZone;
                        await this.ShowContact(contact);
                    }
                    else
                    {
                        var dialog = await dialogService.ShowInfoAsync(Pages.Resources.ContactsResource.ContactNotExists);  //TODO: ίσως να μπει ερώτηση στο χρήστη αν θέλει να γίνει αυτόματα το sync.
                        DialogResult? result = await dialog.Result;
                    }
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ShowContact(Data.Model.DBOs.Contacts.Contact contact)
        {
            try
            {
                string dialogWidth = "1100px", dialogHeight = "90%";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                //εμφανίζει το dialog
                DialogParameters<FluentBlue.UI.Main.Shared.ContactDialogInput> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnContactDialogResult),
                    Title = UI.Main.Components.Resources.ContactDialogResource.Title,
                    PrimaryAction = "",  //GlobalResource.Save,
                    SecondaryAction = "", //=GlobalResource.Cancel,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                ContactDialogInput contactDialogInput = new ContactDialogInput() { Contact = contact, Restricted = true };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.ContactDialog>(contactDialogInput, parameters);
                DialogResult? result = await dialog.Result;

                if (result.Cancelled == false)
                {
                    if (result.Data != null)
                    {
                        Contact newContact = (Contact)result.Data;
                        if (newContact.ObjectState == ObjectState.Added)  //Αν είναι νέο Contacts
                        {
                            //Προσθέτει το νέο Contact στo DropDown.
                            ContactLI contactLI = new ContactLI() { ContactId = newContact.ContactId, FullName = newContact.FullName, Image = newContact.Image };
                            this.contacts!.Add(contactLI);
                            this.Content.Event!.ContactId = contactLI.ContactId;
                            this.SetDataToUI();
                        }
                    }
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnContactDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false && result.Data != null)
                {
                    Contact newContact = (Contact)result.Data!;  //Αν ο χρήστης έχει κάνει αλλαγές στο Contact, τότε το παίρνει από το result.

                    //Αν το ContactId έμεινε το ίδιο.
                    if (this.Content.Event.ContactId == newContact.ContactId)
                    {
                        ContactLI? newContactLI = new ContactLI() { ContactId = newContact.ContactId, FirstName = newContact.FirstName, LastName = newContact.LastName, FullName = newContact.FullName, Occupation = newContact.Occupation, SSN = newContact.SSN, TIN = newContact.TIN, PhoneNumbers = newContact.PhoneNumbers, Image = newContact.Image };
                        this.contacts!.Remove(this.contacts.Where(x => x.ContactId == newContact.ContactId).First());  //Ενημερώνει το Contact που έχει αλλάξει.
                        this.contacts.Add(newContactLI);
                        this.Content.Event.ContactId = newContactLI.ContactId;
                        ContactLI? contactToRemove = this.contacts.Where(x => x.ContactId == newContact.ContactId).First();
                        await contactCmbBox.RemoveSelectedItemAsync(contactToRemove);
                        contactCmbBox!.SelectedOption = newContactLI;  //Ενημερώνει το Contact στο combobox.
                    }
                    else  //Αν το ContactId άλλαξε
                    {
                        //Αν το ContactId δεν υπάρχει στα Contacts.
                        if (!this.contacts!.Any(x => x.ContactId == newContact.ContactId))
                        {
                            this.contacts!.Add(new ContactLI() { ContactId = newContact.ContactId, FullName = newContact.FullName, Image = newContact.Image });
                        }
                    }
                    this.StateHasChanged();
                }
                if (dialog != null)
                {
                    //await dialog.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task UserSelectOnSelectedOptionsChanged(IEnumerable<UserLI>? selectedUsers)
        {
            try
            {
                this.selectedEventUsers = selectedUsers;
                //Αν ο χρήστης επέλεξε τουλάχιστον έναν User.
                //if (selectedUsers != null && selectedUsers.Count() > 0)
                //{
                //    this.selectedCalendarDisplayUsers = selectedUsers;
                //    //await localStorage.SetItemAsync<List<Data.Model.DTOs.UserLI>>(Keywords.LastSelectedCalendarDisplayUsers, selectedUsers.ToList());
                //}
                //else  //Αν δεν επιλέχθηκε κανένας User.
                //{
                //    //UserLI? userLI = this.users.Where(x => x.AvailableInEventUsers == true).FirstOrDefault();  //Εδώ θα πρέπει πάντα να υπάρχει τουλάχιστον ένας User που να επιτρέπεται να εμφανίζεται στο ημερολόγιο.
                //    //List<UserLI> usersToSelect = new List<UserLI>() { userLI! };
                //    //this.selectedCalendarDisplayUsers = usersToSelect.AsEnumerable();
                //}

                //await this.LoadEvents();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task UserSelectOnOptionsSearch(OptionsSearchEventArgs<UserLI> e)
        {
            try
            {
                if (this.users != null)
                {
                    //e.Items = this.users.Where(x => x.FullName.Contains(e.Text, StringComparison.OrdinalIgnoreCase) && x.AvailableInEventUsers == true).OrderBy(x => x.FullName).ToList();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task ContactCmbBoxOnKeyPress(KeyboardEventArgs e)
        {
            try
            {
                //Αν ο χρήστης πατήσει το πλήκτρο Enter, τότε να εκτελεστεί το κουμπί "NewContactBtn".
                if (this.contactCmbBox.Value != null)
                {
                    string filter = contactCmbBox.Value;
                    //this.filteredContacts = this.contacts!.Where(x => x.FullName.Contains(filter, StringComparison.CurrentCultureIgnoreCase) || x.Occupation.Contains(filter, StringComparison.CurrentCultureIgnoreCase)).ToList();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ContactCmbBoxOnValueChanged(string value)
        {
            try
            {
                //Αν ο χρήστης πατήσει το πλήκτρο Enter, τότε να εκτελεστεί το κουμπί "NewContactBtn".
                if (value != null && value != "")
                {
                    this.filteredContacts = this.contacts!.Where(x => x.FullName.Contains(value, StringComparison.CurrentCultureIgnoreCase) || x.Occupation.Contains(value, StringComparison.CurrentCultureIgnoreCase)).ToList();
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnContactCmbBoxSearch(OptionsSearchEventArgs<ContactLI> e)
        {
            try
            {
                var inputValue = e.Text.ToLower() ?? string.Empty;

                if (inputValue.Length > 0)
                {
                    // Filter contacts based on input
                    filteredContacts = this.contacts!.Where(contact =>
                        contact.FullName.ToLower().Contains(inputValue) ||
                        (!string.IsNullOrEmpty(contact.TIN) && contact.TIN.ToLower().Contains(inputValue)) ||
                        (!string.IsNullOrEmpty(contact.SSN) && contact.SSN.ToLower().Contains(inputValue))
                    ).ToList();

                    e.Items = filteredContacts;
                }
                else
                {
                    e.Items = this.contacts;
                }
                this.StateHasChanged();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        //private void OnInputChanged(Microsoft.AspNetCore.Components.ChangeEventArgs e)
        //{
        //    try
        //    {
        //        //return; //δεν δουλεύει το filtering οπότε δεν έχει νόημα να τρέχει (το αφήνουμε για αργότερα).
        //        var inputValue = e.Value?.ToString().ToLower() ?? string.Empty;

        //        // Filter contacts based on input
        //        filteredContacts = this.contacts!.Where(contact =>
        //            contact.FullName.ToLower().Contains(inputValue) ||
        //            (!string.IsNullOrEmpty(contact.TIN) && contact.TIN.ToLower().Contains(inputValue)) ||
        //            (!string.IsNullOrEmpty(contact.SSN) && contact.SSN.ToLower().Contains(inputValue))
        //        ).ToList();

        //        //this.contactCmbBox.Items = filteredContacts;


        //        StateHasChanged(); // Ensure the UI updates

        //    }
        //    catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
        //    {
        //        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
        //    }
        //    catch (ApplicationException ex)
        //    {
        //        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
        //    }
        //    catch (Exception ex)
        //    {
        //        logger.LogError(ex, ex.Message);
        //        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
        //    }
        //}


        private async Task ShowRecurrenceDialog()
        {
            try
            {
                string dialogWidth = "550px", dialogHeight = "";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                DialogParameters<string> parameters = new()
                {
                    Title = Resources.EventDialogResource.Recurrence,
                    PreventDismissOnOverlayClick = true,
                    DismissTitle = GlobalResource.Close,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    Modal = true
                };

                RecurrenceDialogInput recurrenceDialogInput = new RecurrenceDialogInput
                {
                    RecurrenceRule = this.Content.Event!.CustomRecurrenceRule,
                    Event = this.Content!.Event!,
                };

                var dialogInstance = await dialogService.ShowDialogAsync<RecurrenceDialog>(recurrenceDialogInput, parameters);
                var result = await dialogInstance.Result;

                if (!result.Cancelled && result.Data != null)
                {
                    var recurrenceDialogOutput = (RecurrenceDialogOutput)result.Data;  //Saves in temp variable of dialog the result from RecurrenceDialog
                    //this.newRecurrenceRule = recurrenceDialogOutput.RecurrenceRule;
                    this.Content.Event!.CustomRecurrenceRule = recurrenceDialogOutput.RecurrenceRule;
                    this.newRecurrenceDates = recurrenceDialogOutput.RecurrenceDates;
                    //this.Content.Event!.CustomRecurrenceRule = this.newRecurrenceRule;

                    //Αν ο χρήστης επέλεξε ένα νέο RecurrenceRule (όχι το κενό).
                    if (this.Content.Event!.CustomRecurrenceRule != "")
                    {
                        //If Event is going to be recurrent for the first time.
                        if (this.Content.Event!.CustomRecurrence == false && this.Content.Event!.CustomRecurrenceId == null)
                        {
                            this.Content.Event!.CustomRecurrence = true;
                            this.Content.Event!.CustomRecurrenceId = Guid.CreateVersion7();
                        }
                    }
                    //await dialogService.ShowSuccessAsync($"You entered: {recurrenceDialogOutput.RecurrenceRule}", "Success");
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        //// This method is called when the user selects a new reminder time
        //// The actual update to EventReminders will happen in GetDataFromUI when saving
        //public void OnReminderTimeSelectedOptionChanged(ReminderTime? reminderTime)
        //{
        //    try
        //    {
        //        this.selectedReminder = reminderTime;

        //        //if (reminderTime.HasValue)
        //        //{
        //        //    this.Content!.Event.SetReminder(reminderTime.Value);

        //        //    StateHasChanged();
        //        //}
        //        //else
        //        //{
        //        //    this.Content!.Event.RemoveReminder();
        //        //}
        //    }
        //    catch (ApplicationException ex)
        //    {
        //        logger.LogError(ex, ex.Message);
        //        new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
        //    }
        //    catch (Exception ex)
        //    {
        //        logger.LogError(ex, ex.Message);
        //        new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
        //    }
        //}

        public async Task OnReminderTimeValueChanged(string reminderTime)
        {
            try
            {
                this.selectedReminder = !string.IsNullOrEmpty(reminderTime)
                    ? (ReminderTime)Enum.Parse(typeof(ReminderTime), reminderTime)
                    : null;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
    }
}

