﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>FluentBlue.Data.Database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{2b4c57db-9399-4608-b763-9a605c0713c0}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>FluentBlue.Data.Database</RootNamespace>
    <AssemblyName>FluentBlue.Data.Database</AssemblyName>
    <ModelCollation>1032,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>Greek_CI_AI</DefaultCollation>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="Tenants\" />
    <Folder Include="Tenants\Tables\" />
    <Folder Include="Settings\" />
    <Folder Include="Settings\Tables\" />
    <Folder Include="Contacts\" />
    <Folder Include="Contacts\Tables\" />
    <Folder Include="Calendar\" />
    <Folder Include="Calendar\Tables\" />
    <Folder Include="Security\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="Tenants\Tables\Branch.sql" />
    <Build Include="Tenants\Tables\Company.sql" />
    <Build Include="Tenants\Tables\Role.sql" />
    <Build Include="Tenants\Tables\Tenant.sql" />
    <Build Include="Tenants\Tables\User.sql" />
    <Build Include="Tenants\Tables\PasswordResetToken.sql" />
    <Build Include="Settings\Tables\UserSetting.sql" />
    <Build Include="Contacts\Tables\ContactPhone.sql" />
    <Build Include="Contacts\Tables\Contact.sql" />
    <Build Include="Contacts\Tables\ContactAddress.sql" />
    <Build Include="Contacts\Tables\ContactEmail.sql" />
    <Build Include="Calendar\Tables\EventCategory.sql" />
    <Build Include="Calendar\Tables\Event.sql" />
    <Build Include="Security\Tenants.sql" />
    <Build Include="Security\Settings.sql" />
    <Build Include="Security\Contacts.sql" />
    <Build Include="Security\Calendar.sql" />
    <Build Include="Calendar\Tables\EventUser.sql" />
    <Build Include="Contacts\Tables\ContactCategoryMapping.sql" />
    <Build Include="Contacts\Tables\ContactCategory.sql" />
    <Build Include="Calendar\Tables\EventState.sql" />
    <Build Include="Calendar\Tables\EventReminder.sql" />
    <Build Include="Tenants\Tables\UserDevice.sql" />
    <Build Include="Calendar\Tables\Calendar.sql" />
  </ItemGroup>
</Project>