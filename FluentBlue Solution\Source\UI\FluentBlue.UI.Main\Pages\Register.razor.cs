using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.UI.Main.Pages.Resources;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Shared.Request;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.UI.Main.Pages
{
    public partial class Register
    {
        public class RegistrationData
        {
            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [MaxLength(100)]
            public string FirstName { get; set; } = string.Empty;

            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [MaxLength(100)]
            public string LastName { get; set; } = string.Empty;

            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [EmailAddress(ErrorMessageResourceName = "InvalidEmailAddress", ErrorMessageResourceType = typeof(Main.GlobalResource))]
            [MaxLength(50)]
            public string Email { get; set; } = string.Empty;

            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [MaxLength(30)]
            public string Username { get; set; } = string.Empty;

            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [MaxLength(30)]
            public string Password { get; set; } = string.Empty;

            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [Compare(nameof(Password), ErrorMessageResourceName = "PasswordMismatchMessage", ErrorMessageResourceType = typeof(RegisterResource))]
            public string ConfirmPassword { get; set; } = string.Empty;

            [MaxLength(30)]
            public string Mobile { get; set; } = string.Empty;
        }

        private RegistrationData registrationData = new RegistrationData();
        private bool isLoading = false;
        private bool isRegistered = false;
        private string resultMessage = " ";

        protected override void OnInitialized()
        {
            // Initialize component
        }

        private async void OnSubmit()
        {
            try
            {
                this.isLoading = true;
                this.resultMessage = " ";

                // Validate password confirmation
                if (registrationData.Password != registrationData.ConfirmPassword)
                {
                    this.resultMessage = Resources.RegisterResource.PasswordMismatchMessage;
                    return;
                }

                // Create registration request
                var request = new RegisterUserRequest
                {
                    FirstName = registrationData.FirstName,
                    LastName = registrationData.LastName,
                    Email = registrationData.Email,
                    Username = registrationData.Username,
                    Password = registrationData.Password,
                    Mobile = registrationData.Mobile
                };

                // Call the API to register the user
                await authenticationWebApiClient.RegisterUserAsync(request);
                
                this.isRegistered = true;
            }
            catch (ApplicationException ex)
            {
                // Handle specific application exceptions (like user already exists)
                this.resultMessage = ex.Message;
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                this.resultMessage = Resources.RegisterResource.ErrorMessage;
            }
            finally
            {
                this.isLoading = false;
                this.StateHasChanged();
            }
        }


    }
}
