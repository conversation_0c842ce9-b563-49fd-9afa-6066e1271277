﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace FluentBlue.Application.Business
{
    public class RolesBusiness : IRolesBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        private IMapper mapper;
        private ILogger logger;

        public RolesBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, I<PERSON>apper mapper, ILogger logger)
        {
            this.dbContext = dbContext;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>>> GetRoles(ReadPagedDataParameters parameters)
        {
            try
            {
                //Validation
                int resultsCount;
                parameters.Filter = parameters.Filter?.Trim() ?? "";
                if (parameters.PageIndex <= 0)
                {
                    parameters.PageIndex = 1;
                }
                if (parameters.PageSize <= 0)
                {
                    parameters.PageSize = 10;
                }

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.Role> query = this.dbContext.Roles.AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == parameters.TenantId);
                if (parameters.Filter != null && parameters.Filter != "")
                {
                    query = query.Where(x => x.Name.Contains(parameters.Filter));
                }
                if (parameters.SortColumns != null && parameters.SortColumns.Count > 0)
                {
                    foreach (var sortColumn in parameters.SortColumns)
                    {
                        query = sortColumn.Value == SortOrder.Ascending
                            ? query.OrderBy(e => EF.Property<object>(e, sortColumn.Key))
                            : query.OrderByDescending(e => EF.Property<object>(e, sortColumn.Key));
                    }
                }

                //Διαβάζει το σύνολο των records.
                resultsCount = query.Count();

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Tenants.Role> roles = await query.Skip((parameters.PageIndex - 1) * parameters.PageSize).Take(parameters.PageSize).ToListAsync();

                PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>> pagedData = new PagedData<List<Data.Model.DBOs.Tenants.Role>>();
                pagedData.Data = roles;
                pagedData.DataTotalCount = resultsCount;
                return pagedData;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DTOs.RoleLI>> GetRolesLI(Guid tenantId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.Role> query = this.dbContext.Roles.AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == tenantId).OrderBy(x => x.Name);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Tenants.Role> roles = await query.ToListAsync();
                List<FluentBlue.Data.Model.DTOs.RoleLI> rolesLIs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Tenants.Role>, List<FluentBlue.Data.Model.DTOs.RoleLI>>(roles);

                return rolesLIs;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Tenants.Role?> GetRole(Guid roleId)
        {
            try
            {
                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.Role> query = this.dbContext.Roles.AsNoTracking().Where(x => x.RoleId == roleId);
                Data.Model.DBOs.Tenants.Role? role = await query.FirstOrDefaultAsync();

                //Result
                return role;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Tenants.Role?> GetRoleOfUser(Guid tenantId, Guid userId)
        {
            try
            {
                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.Role> query = this.dbContext.Users
                    .AsNoTracking()
                    .Where(u => u.UserId == userId)
                    .Join(this.dbContext.Roles,
                          user => user.RoleId,
                          role => role.RoleId,
                          (user, role) => role);

                Data.Model.DBOs.Tenants.Role? role = await query.FirstOrDefaultAsync();

                //Result
                return role;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task CreateOrUpdateRole(Role role)
        {
            try
            {
                //Validation
                if (role == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                RoleValidator validator = new RoleValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(role);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage;  // + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                //List<ValidationResult> validationResults = new List<ValidationResult>();
                //ValidationContext validationContext = new ValidationContext(role);
                //if (Validator.TryValidateObject(role, validationContext, validationResults, true) == false)
                //{
                //    string validationErrors = string.Empty;
                //    foreach (CompositeValidationResult compValidationResult in validationResults)
                //    {
                //        foreach (ValidationResult validationResult in compValidationResult.Results)
                //        {
                //            validationErrors += validationResult.ErrorMessage + ". ";
                //        }
                //    }
                //    throw new ApplicationException(validationErrors);
                //}

                //Query
                this.dbContext.Attach(role);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { role.RoleId });
                throw;
            }
        }

        public async Task DeleteRole(Guid roleId)
        {
            Role? role = await this.dbContext.Roles.Where(x => x.RoleId == roleId).AsNoTracking().FirstAsync();
            if (role != null)
            {
                role.ObjectState = ObjectState.Deleted;
                this.dbContext.Attach(role);
                this.dbContext.SaveChanges();
            }
        }

        public async Task<Role?> CheckRoleExists(Guid tenantId, Guid roleId, string name)
        {
            Role? role = await this.dbContext.Roles.Where(x => x.RoleId != roleId && x.Name == name && x.TenantId == tenantId).FirstOrDefaultAsync();
            return role;
        }
    }
}
