@using FluentBlue.Data.Model.DBOs.Calendar
@using FluentBlue.Shared.Utilities
@using Microsoft.Extensions.Caching.Hybrid
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.JSInterop
@using Syncfusion.Blazor.Schedule

@implements IDialogContentComponent<RecurrenceDialogInput>

@inject IDialogService dialogService
@inject ILogger<FluentBlue.UI.Main.Components.RecurrenceDialog> logger
@inject HttpClient httpClient
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject IFormFactor formFactor
@inject IJSRuntime JS
@inject HybridCache cache

<FluentDialogBody>
    <SfRecurrenceEditor @ref="@recurrenceEditor" Value="@selectedRecurrenceRule" ValueChanged="@ValueChanged" EndTypes="endTypes"></SfRecurrenceEditor>
    <div class="h-28 overflow-y-auto border border-gray-300 rounded p-2">
        @infoText
    </div>
</FluentDialogBody>


<FluentDialogFooter>
    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Vertical" VerticalAlignment="VerticalAlignment.Bottom">
        <FluentDivider Class="w-full" Role="DividerRole.Presentation"></FluentDivider>
        <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" HorizontalAlignment="HorizontalAlignment.Right" VerticalAlignment="VerticalAlignment.Bottom" Class="self-end">
            <FluentButton Appearance="Appearance.Accent" OnClick="SaveBtnOnClick"><span>@GlobalResource.Save</span></FluentButton>
            <FluentButton OnClick="CancelBtnOnClick"><span>@GlobalResource.Cancel</span></FluentButton>
        </FluentStack>
    </FluentStack>
</FluentDialogFooter>