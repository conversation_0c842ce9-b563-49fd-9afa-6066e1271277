﻿using AKSoftware.Blazor.Utilities;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Shared.Utilities;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Components;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using Syncfusion.Blazor.Popups;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Layout
{
    public partial class ThemeLayout
    {
        private Data.Model.DBOs.Settings.UserSetting? userSettings;
        private DesignThemeModes currentDesignThemeMode;  //Χρησιμοποιείται από το FluentDesignTheme
        private string currentThemeColorText =string.Empty;  //Η τιμή π.χ. #dd23F0 του currentThemeColor
        private FluentDesignTheme fluentDesignTheme = default!;

        //protected override async Task OnInitializedAsync()
        //{
        //    // Subscribe to the authentication state changes
        //    //AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

        //    // Optionally, check initial authentication state
        //    //var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
          
        //}

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {

                }
                else
                {

                }

                if (AuthenticatedUserData.UserId != Guid.Empty && this.userSettings == null)
                {

                    UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    this.userSettings = await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);

                    await this.fluentDesignTheme!.ClearLocalStorageAsync();
                    this.currentThemeColorText = this.userSettings!.ThemeColor.GetEnumDescription();
                    this.currentDesignThemeMode = this.ConvertThemeColorModeToThemeMode(this.userSettings!.ThemeColorMode);

                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
            }
        }

        private async void OnAuthenticationStateChanged(Task<AuthenticationState> task)
        {
            AuthenticationState authState = await task;
        }

      
        private DesignThemeModes ConvertThemeColorModeToThemeMode(ThemeColorMode colorMode)
        {
            if (colorMode == ThemeColorMode.Light)
            {
                return DesignThemeModes.Light;
            }
            else if (colorMode == ThemeColorMode.Dark)
            {
                return DesignThemeModes.Dark;
            }
            //else if (colorMode == ThemeColorMode.System)
            //{
            //    return DesignThemeModes.System;
            //}
            else
            {
                return DesignThemeModes.System;  //Δεν θα τρέξει ποτέ.
            }
        }
    }
}
