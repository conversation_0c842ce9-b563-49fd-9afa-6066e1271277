using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Mail;

namespace FluentBlue.Application.Business
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration configuration;
        private readonly ILogger<EmailService> logger;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
        {
            this.configuration = configuration;
            this.logger = logger;
        }

        public async Task SendPasswordResetEmailAsync(string toEmail, string resetToken, string userName)
        {
            var resetUrl = $"{configuration["WebApi:BaseUrl"]}reset-password?token={resetToken}";
            
                var subject = "Password Reset Request - FluentBlue";
                var body = $@"
                    <!DOCTYPE html>
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset='utf-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    </head>
                    <body style='margin: 0; padding: 0; font-family: ""Segoe UI"", ""SF Pro Display"", -apple-system, BlinkMacSystem<PERSON>ont, Roboto, Arial, sans-serif; color: #323130; background-color: #f5f5f5;'>
                        <div style='max-width: 600px; margin: 10 auto; padding: 40px 20px; background-color: white; border-radius: 4px; box-shadow: 0 1.6px 3.6px 0 rgba(0,0,0,0.132), 0 0.3px 0.9px 0 rgba(0,0,0,0.108);'>
                            <div style='text-align: center; margin-bottom: 24px;'>
                                <h1 style='font-size: 24px; font-weight: 600; color: #323130; margin: 0 0 16px 0;'>Password Reset Request</h1>
                            </div>
                        
                            <div style='margin-bottom: 24px;'>
                                <p style='font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;'>Hello {userName},</p>
                                <p style='font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;'>You have requested to reset your password for your FluentBlue account.</p>
                            </div>

                            <div style='margin: 32px 0; text-align: center;'>
                                <a href='{resetUrl}' style='display: inline-block; background-color: #0078d4; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: 600; font-size: 14px; transition: background-color 0.2s ease-in-out;'>Reset Password</a>
                            </div>

                            <div style='margin-bottom: 24px; padding: 16px; background-color: #f3f2f1; border-radius: 4px;'>
                                <p style='font-size: 14px; line-height: 1.5; margin: 0 0 8px 0; color: #605e5c;'>If you cannot click the button, copy and paste this URL into your browser:</p>
                                <p style='font-size: 14px; line-height: 1.5; margin: 0; color: #0078d4; word-break: break-all;'>{resetUrl}</p>
                            </div>

                            <div style='border-top: 1px solid #edebe9; padding-top: 24px; margin-top: 24px;'>
                                <p style='font-size: 14px; line-height: 1.5; margin: 0 0 8px 0; color: #605e5c;'>This link will expire in 24 hours.</p>
                                <p style='font-size: 14px; line-height: 1.5; margin: 0 0 24px 0; color: #605e5c;'>If you did not request this password reset, please ignore this email.</p>
                            
                                <p style='font-size: 14px; line-height: 1.5; margin: 0; color: #605e5c;'>
                                    Best regards,<br>
                                    FluentBlue Team
                                </p>
                            </div>
                        </div>
                    </body>
                    </html>";

            await SendEmailAsync(toEmail, subject, body);
        }

        public async Task SendWelcomeEmailAsync(string toEmail, string userName)
        {
            var loginUrl = $"{configuration["WebApi:BaseUrl"]}login";
            
            var subject = "Welcome to FluentBlue!";
            var body = $@"
                <!DOCTYPE html>
                <!DOCTYPE html>
                <!DOCTYPE html>
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='utf-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                </head>
                <body style='margin: 0; padding: 0; font-family: ""Segoe UI"", ""SF Pro Display"", -apple-system, BlinkMacSystemFont, Roboto, Arial, sans-serif; color: #323130; background-color: #f5f5f5;'>
                    <div style='max-width: 600px; margin: 10 auto; padding: 40px 20px; background-color: white; border-radius: 4px; box-shadow: 0 1.6px 3.6px 0 rgba(0,0,0,0.132), 0 0.3px 0.9px 0 rgba(0,0,0,0.108);'>
                        <div style='text-align: center; margin-bottom: 24px;'>
                            <h1 style='font-size: 24px; font-weight: 600; color: #323130; margin: 0 0 16px 0;'>Welcome to FluentBlue!</h1>
                        </div>
                    
                        <div style='margin-bottom: 24px;'>
                            <p style='font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;'>Hello {userName},</p>
                            <p style='font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;'>Your account has been successfully created! You can now sign in to your account using the credentials you provided during registration.</p>
                        </div>

                        <div style='margin: 32px 0; text-align: center;'>
                            <a href='{loginUrl}' style='display: inline-block; background-color: #0078d4; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: 600; font-size: 14px; transition: background-color 0.2s ease-in-out;'>Sign In</a>
                        </div>

                        <div style='border-top: 1px solid #edebe9; padding-top: 24px; margin-top: 24px;'>
                            <p style='font-size: 14px; line-height: 1.5; margin: 0 0 24px 0; color: #605e5c;'>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                        
                            <p style='font-size: 14px; line-height: 1.5; margin: 0; color: #605e5c;'>
                                Best regards,<br>
                                FluentBlue Team
                            </p>
                        </div>
                    </div>
                </body>
                </html>";

            await SendEmailAsync(toEmail, subject, body);
        }

        public async Task SendEmailAsync(string toEmail, string subject, string body)
        {
            try
            {
                var smtpSettings = configuration.GetSection("SmtpSettings");
                var smtpHost = smtpSettings["Host"];
                var smtpPort = int.Parse(smtpSettings["Port"] ?? "587");
                var smtpUsername = smtpSettings["Username"];
                var smtpPassword = smtpSettings["Password"];
                var fromEmail = smtpSettings["FromEmail"];
                var fromName = smtpSettings["FromName"] ?? "FluentBlue";

                using var client = new SmtpClient(smtpHost, smtpPort);
                client.EnableSsl = true;
                client.Credentials = new NetworkCredential(smtpUsername, smtpPassword);

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(fromEmail!, fromName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };

                mailMessage.To.Add(toEmail);

                await client.SendMailAsync(mailMessage);
                
                //_logger.LogInformation("Email sent successfully to {Email} with subject: {Subject}", toEmail, subject);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to send email to {Email} with subject: {Subject}", toEmail, subject);
                throw;
            }
        }
    }
}
