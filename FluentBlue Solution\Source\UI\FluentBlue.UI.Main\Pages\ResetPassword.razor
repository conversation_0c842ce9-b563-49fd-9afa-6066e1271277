@page "/reset-password"
@layout EmptyLayout

@using FluentBlue.Shared
@using FluentBlue.WebApi.Client
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<FluentBlue.UI.Main.Pages.Resources.ResetPasswordResource> resetPasswordResource
@inject AuthenticationWebApiClient authenticationWebApiClient
@inject NavigationManager navigationManager
@inject ILogger<ResetPassword> logger
@inject IDialogService dialogService

<div style="background-size: cover; background-position: center; height: 100vh; width: 100%; background-image: url('_content/FluentBlue.UI.Main/images/login-background.png')">
    <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center" VerticalAlignment="VerticalAlignment.Center" Style="height:100vh">
        <FluentCard Width="450px" Height="auto">
            @if (!isPasswordReset)
            {
                <EditForm Model="@resetPasswordData" OnValidSubmit="@OnSubmit">
                    <DataAnnotationsValidator />

                    <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                        <FluentLabel Typo="Typography.H1">@resetPasswordResource["Title"]</FluentLabel>
                        <FluentLabel Style="text-align: center; margin-bottom: 20px;">@resetPasswordResource["Subtitle"]</FluentLabel>
                        
                        @if (!string.IsNullOrEmpty(tokenValidationMessage))
                        {
                            <FluentLabel Color="Color.Error" Style="text-align: center; margin-bottom: 20px;">
                                @tokenValidationMessage
                            </FluentLabel>
                        }
                        
                        <FluentTextField @bind-Value="@resetPasswordData.NewPassword" 
                                       Label="@resetPasswordResource["NewPasswordLabel"]"
                                       TextFieldType="TextFieldType.Password" 
                                       Maxlength="30" 
                                       Style="width:85%;" 
                                       Class="mb-1" 
                                       Required="true"
                                       AutoComplete="off"
                                       Disabled="@(!isTokenValid)">
                            <FluentIcon Value="@(new Icons.Regular.Size16.LockClosed())" Color="@Color.Neutral" Slot="end" />
                        </FluentTextField>
                        <div style="width:85%;">
                            <FluentValidationMessage For="@(() => resetPasswordData.NewPassword)" />
                        </div>

                        <FluentTextField @bind-Value="@resetPasswordData.ConfirmPassword" 
                                       Label="@resetPasswordResource["ConfirmPasswordLabel"]"
                                       TextFieldType="TextFieldType.Password" 
                                       Maxlength="30" 
                                       Style="width:85%;" 
                                       Class="mb-1" 
                                       Required="true"
                                       AutoComplete="off"
                                       Disabled="@(!isTokenValid)">
                            <FluentIcon Value="@(new Icons.Regular.Size16.LockClosed())" Color="@Color.Neutral" Slot="end" />
                        </FluentTextField>
                        <div style="width:85%;">
                            <FluentValidationMessage For="@(() => resetPasswordData.ConfirmPassword)" />
                        </div>

                        <FluentButton Type="@ButtonType.Submit" 
                                    Loading="@isLoading" 
                                    Appearance="@Appearance.Accent"
                                    Style="margin-top: 10px;"
                                    Disabled="@(!isTokenValid)">
                            @resetPasswordResource["ResetPasswordButton"]
                        </FluentButton>
                        
                        <FluentLabel Color="Color.Warning">@resultMessage</FluentLabel>
                    </FluentStack>
                </EditForm>
            }
            else
            {
                <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                    <FluentLabel Typo="Typography.H1">@resetPasswordResource["Title"]</FluentLabel>
                    <FluentIcon Value="@(new Icons.Regular.Size48.CheckmarkCircle())" Color="@Color.Success" Style="margin: 20px 0;" />
                    <FluentLabel Style="text-align: center; margin-bottom: 20px;" Color="@Color.Success">
                        @resetPasswordResource["SuccessMessage"]
                    </FluentLabel>
                </FluentStack>
            }
            
            <br />
            <p style="text-align: center;">
                <FluentAnchor Href="/login" Appearance="Appearance.Hypertext">
                    @resetPasswordResource["BackToLoginButton"]
                </FluentAnchor>
            </p>
        </FluentCard>
    </FluentStack>
</div>

<FluentDialogProvider />
<FluentTooltipProvider />
<FluentToastProvider MaxToastCount="10" />
