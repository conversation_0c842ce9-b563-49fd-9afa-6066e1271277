using System;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business.Services
{
    public interface IApplicationKeyService
    {
        Task<string> GetApplicationKeyAsync();
        Task SetApplicationKeyAsync(string applicationKey);
        Task<string> GenerateAndStoreApplicationKeyAsync();
        void SetCurrentApplicationKey(string applicationKey);
        void ClearCurrentApplicationKey();
    }
} 