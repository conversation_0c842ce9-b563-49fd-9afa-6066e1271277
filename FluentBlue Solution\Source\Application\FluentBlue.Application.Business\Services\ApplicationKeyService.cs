using FluentBlue.Shared.Cryptography.AES;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business.Services
{
    public class ApplicationKeyService : IApplicationKeyService
    {
        private readonly IConfiguration configuration;
        private readonly IEncryptionService encryptionService;
        private readonly ILogger<ApplicationKeyService> logger;
        private static string currentApplicationKey = string.Empty;

        public ApplicationKeyService(
            IConfiguration configuration,
            IEncryptionService encryptionService,
            ILogger<ApplicationKeyService> logger)
        {
            this.configuration = configuration;
            this.encryptionService = encryptionService;
            this.logger = logger;
        }

        public async Task<string> GetApplicationKeyAsync()
        {
            try
            {
                // First try to get from configuration
                var configKey = configuration["Encryption:ApplicationKey"];
                if (!string.IsNullOrEmpty(configKey))
                {
                    return configKey;
                }

                // If not in config, try to get from environment variable
                var envKey = Environment.GetEnvironmentVariable("FLUENTBLUE_ENCRYPTION_KEY");
                if (!string.IsNullOrEmpty(envKey))
                {
                    return envKey;
                }

                // If no key found, generate a new one
                logger.LogWarning("No application encryption key found. Generating new key.");
                return await GenerateAndStoreApplicationKeyAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving application key");
                throw;
            }
        }

        public async Task SetApplicationKeyAsync(string applicationKey)
        {
            try
            {
                if (string.IsNullOrEmpty(applicationKey))
                {
                    throw new ArgumentException("Application key cannot be null or empty", nameof(applicationKey));
                }

                // Store in environment variable for this session
                Environment.SetEnvironmentVariable("FLUENTBLUE_ENCRYPTION_KEY", applicationKey);
                
                // Set the current key for the encryption service
                SetCurrentApplicationKey(applicationKey);
                
                logger.LogInformation("Application encryption key set successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error setting application key");
                throw;
            }
        }

        public async Task<string> GenerateAndStoreApplicationKeyAsync()
        {
            try
            {
                var newKey = encryptionService.GenerateApplicationKey();
                await SetApplicationKeyAsync(newKey);
                
                logger.LogInformation("Generated and stored new application encryption key");
                return newKey;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error generating and storing application key");
                throw;
            }
        }

        public void SetCurrentApplicationKey(string applicationKey)
        {
            currentApplicationKey = applicationKey ?? string.Empty;
            EncryptionService.SetApplicationKey(applicationKey);
            logger.LogDebug("Set current application encryption key");
        }

        public void ClearCurrentApplicationKey()
        {
            currentApplicationKey = string.Empty;
            EncryptionService.SetApplicationKey(string.Empty);
            logger.LogDebug("Cleared current application encryption key");
        }
    }
} 