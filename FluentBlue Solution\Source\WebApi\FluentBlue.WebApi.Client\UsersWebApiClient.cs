﻿using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace FluentBlue.WebApi.Client
{
    public class UsersWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<UsersWebApiClient> logger;

        public UsersWebApiClient(HttpClient httpClient, ILogger<UsersWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>> GetUsers(Guid tenantId, string filter, UInt16 pageIndex, UInt16 pageSize)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Users/<USER>";

                RequestDataParameters requestDataParams = new RequestDataParameters();
                requestDataParams.TenantId = tenantId;
                requestDataParams.Filter = filter.Trim();
                requestDataParams.PageIndex = pageIndex;
                requestDataParams.PageSize = pageSize;
                requestDataParams.SortColumns = new Dictionary<string, SortOrder>();
                requestDataParams.SortColumns.Add("FirstName", SortOrder.Ascending);
                requestDataParams.SortColumns.Add("LastName", SortOrder.Ascending);
                string getEntitiesParamsJson = JsonConvert.SerializeObject(requestDataParams);  //Μετατρέπουμε τις παραμέτρους για το διάβασμα entities σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContent = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");

                string urlParameters = UrlHelpers.ToQueryString(requestDataParams, "&");
                //requestUri = requestUri + "?" + urlParameters;

                //Εκτελούμε το request.
                //this.httpClient.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiIyY2FhYmI1NC1mZDM4LTRhY2MtOTMyYS1kNWM3Yjk0ZmRkMmYiLCJVc2VybmFtZSI6IjMzIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZSI6IlAgTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IlN1cGVyQWRtaW4iLCJUZW5hbnRJZCI6IjNmYTg1ZjY0LTU3MTctNDU2Mi1iM2ZjLTJjOTYzZjY2YWZhNiIsImV4cCI6MTc0NzA0Mjk2Mn0.ZqUnCMKdLJ37E2PBZJJVTaFXpVBa5skxbHhBY18wTjs");
                //HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);  //Διαβάζουμε το HttpResponse

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<PagedData<List<Data.Model.DTOs.UserView>>> response = JsonConvert.DeserializeObject<ApiResponse<PagedData<List<Data.Model.DTOs.UserView>>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new PagedData<List<Data.Model.DTOs.UserView>> { Data = null, DataTotalCount = 0 };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetUsers({@tenantId},{fiter},{@pageIndex}, {@pageSize})", tenantId, filter, pageIndex, pageSize);
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DTOs.UserLI>> GetUsersList(Guid tenantId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Users/<USER>";

                //Εκτελούμε το request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);   

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DTOs.UserLI>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DTOs.UserLI>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new List<Data.Model.DTOs.UserLI>();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetUsersLI({@tenantId})", tenantId);
                throw;
            }
        }

        public async Task<User?> GetUser(Guid userId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "User/Get?UserId=" + userId.ToString();  // + "&tenantId=" + tenantId;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<User?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Tenants.User?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetUser({@userId})", userId);
                throw;
            }
        }

        public async Task<User?> CreateOrUpdateUser(User user)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/User/CreateOrUpdate";

                //string UserJson = Newtonsoft.Json.JsonConvert.SerializeObject(User, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το User object σε json.
                string UserJson = System.Text.Json.JsonSerializer.Serialize(user);  //Μετατρέπουμε το User object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(UserJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<User>? response = JsonConvert.DeserializeObject<ApiResponse<User>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateUser({@tenant})", user);
                throw;
            }
        }

        public async Task DeleteUser(Guid userId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/User/Delete?UserId=" + userId.ToString();

                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (Exception)
            {
                throw;
            }

        }

        public async Task<UserSetting?> GetUserSettings(Guid userId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/User/Settings/Get?userId=" + userId.ToString();

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<UserSetting?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Settings.UserSetting?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetUserSettings({@userId})", userId);
                throw;
            }
        }

        public async Task<Role?> GetUserRole(Guid userId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/User/Role/Get?UserId=" + userId.ToString();

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<Role?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Tenants.Role?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetUserSettings({@userId})", userId);
                throw;
            }
        }

        public async Task<User?> CheckUserExists(Guid userId, string email)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "User/CheckExists?UserId=" + userId.ToString() + "&Email=" + email;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<User?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Tenants.User?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CheckUserExists({@userId}, {@email})", userId, email);
                throw;
            }
        }

      
    }
}
