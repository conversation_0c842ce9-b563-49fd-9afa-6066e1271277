﻿@using FluentBlue.Shared.Utilities
@using FluentBlue.UI.Main.Auth
@using FluentBlue.UI.Main.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Caching.Hybrid
@using Microsoft.Extensions.Hosting
@using Microsoft.JSInterop
@using Syncfusion.Blazor
@using Blazored.LocalStorage
@using FluentBlue.UI.Main.Services

@inherits LayoutComponentBase

@inject HttpClient httpClient
@inject ILoginService loginService
@inject IDialogService dialogService
@inject ILogger<MainLayout> logger
@inject ILogger<FluentBlue.WebApi.Client.SettingsWebApiClient> settingsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.RolesWebApiClient> rolesWebApiClientLogger
@inject FluentBlue.WebApi.Client.UserDevicesWebApiClient userDevicesWebApiClient
@inject IJSRuntime JS
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager navManager
@inject IFormFactor formFactor
@inject IRestartAppService restartAppService
@inject IDevicePreferences devicePreferences
@inject ILocalStorageService localStorage
@inject HybridCache cache
@inject SignalRNotificationService signalRNotificationService
@inject IToastService toastService
@inject IWebUpdateCheckerService webUpdateChecker


<FluentDesignTheme @ref="fluentDesignTheme" @bind-Mode="@currentDesignThemeMode" @bind-CustomColor="@currentThemeColorText" StorageName="Theme" />



<ErrorBoundary @ref="errorBoundary">
    <ChildContent>
        <div class="relative">
            <FluentLayout>
                <FluentHeader>
                    <FluentToolbar Style="width:100%; background-color:transparent;" Orientation="Orientation.Horizontal">
                        @if (ScreenSizeTracker.CurrentBreakpoint != "xs" && ScreenSizeTracker.CurrentBreakpoint != "sm")
                        {
                            <FluentLabel Slot="start" Class="xs:hidden sm:hidden">@GlobalResource.AppTitle</FluentLabel>
                        }
                        <span id="smNavMenu" Slot="start">
                            @if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                            {
                                <FluentButton BackgroundColor="var(--neutralFillInput)" IconEnd="@(new Icons.Filled.Size20.Navigation())" Appearance="Appearance.Accent" OnClick="@(() => smNavMenuOpened = !smNavMenuOpened)"></FluentButton>
                                @* <FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Neutral" class="sm:visible md:hidden lg:hidden xl:hidden xxl:hidden" OnClick="@(() => smNavMenuOpened = !smNavMenuOpened)" /> *@
                            }
                        </span>
                        <div slot="end">
                            <FluentButton Id="quickMenuBtn" BackgroundColor="var(--neutralFillInput)" IconEnd="@(new Icons.Filled.Size20.Add())" Appearance="Appearance.Accent" OnClick="@(() => quickMenuOpened = !quickMenuOpened)"></FluentButton>
                            <FluentMenu @bind-Open="quickMenuOpened" Anchor="quickMenuBtn" Anchored="true" UseMenuService="true">
                                <FluentMenuItem onclick="NewContact">
                                    <span slot="start">
                                        <FluentIcon Value="@(new Icons.Regular.Size20.PeopleAdd())" Color="Color.Neutral" />
                                    </span>
                                    @Resources.MainlLayoutResource.NewContact
                                </FluentMenuItem>
                                <FluentMenuItem OnClick="NewEvent">
                                    <span slot="start"><FluentIcon Value="@(new Icons.Regular.Size20.CalendarAdd())" Color="Color.Neutral" /></span>
                                    @Resources.MainlLayoutResource.NewEvent
                                </FluentMenuItem>
                            </FluentMenu>
                        </div>
                        <div Slot="end">
                            <FluentSpacer Width="10" />
                        </div>
                        <div Slot="end">
                            <FluentButton BackgroundColor="var(--neutralFillInput)" IconEnd="@(new Icons.Regular.Size20.Settings())" Appearance="Appearance.Accent" OnClick="SettingsOnClick"></FluentButton>
                        </div>
                        <div Slot="end">
                            <FluentSpacer Width="10" />
                        </div>
                        <div Slot="end">
                            <FluentButton id="userMenuBtn" BackgroundColor="var(--neutralFillInput)" IconEnd="@(new Icons.Regular.Size20.Person())" Appearance="Appearance.Accent" OnClick="@(() => userMenuOpened = !userMenuOpened)"></FluentButton>
                            <FluentMenu @bind-Open="userMenuOpened" Anchor="userMenuBtn" Anchored="true" UseMenuService="true">
                                <FluentMenuItem OnClick="AccountOnClick">
                                    <span slot="start">
                                        <FluentIcon Value="@(new Icons.Regular.Size20.PersonSettings())" Color="Color.Neutral" Slot="start" />
                                    </span>
                                    @Resources.MainlLayoutResource.Account
                                </FluentMenuItem>
                                <FluentMenuItem OnClick="LogoutOnClick">
                                    <span slot="start"><FluentIcon Value="@(new Icons.Regular.Size20.SyncOff())" Color="Color.Neutral" Slot="start" /></span>
                                    @Resources.MainlLayoutResource.Logout
                                </FluentMenuItem>
                            </FluentMenu>
                        </div>
                        @*   <div Slot="end">
                <div id="userMenu" class="p-1 block">
                <FluentIcon Value="@(new Icons.Regular.Size20.Person())" Color="Color.Neutral" OnClick="@(() => userMenuOpened = !userMenuOpened)" />
                </div>
                <FluentMenu Anchor="userMenu" @bind-Open="userMenuOpened">
                <FluentMenuItem>
                <span slot="start">
                <FluentIcon Value="@(new Icons.Regular.Size20.PersonSettings())" Color="Color.Neutral" Slot="start" />
                </span>
                @Resources.MainlLayoutResource.Account
                </FluentMenuItem>
                <FluentMenuItem OnClick="Logout_OnClick">
                <span slot="start"><FluentIcon Value="@(new Icons.Regular.Size20.SyncOff())" Color="Color.Neutral" Slot="start" /></span>
                @Resources.MainlLayoutResource.Logout
                </FluentMenuItem>
                </FluentMenu>
                </div> *@

                    </FluentToolbar>
                </FluentHeader>
                <FluentStack Class="main" Orientation="Orientation.Horizontal" Width="100%">
                    <NavMenu Class="visible xs:hidden sm:hidden" />
                    <FluentBodyContent>
                        <ErrorBoundary>
                            <ChildContent>
                                @if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                                {
                                    <ErrorBoundary @ref="errorBoundary">
                                        <ChildContent>
                                            @Body
                                        </ChildContent>
                                        <ErrorContent>
                                            <div class="px-3 pt-3 flex items-center justify-center min-h-full">
                                                <div>
                                                    <div class="text-center">
                                                        <FluentLabel Typo="Typography.Header" Color="@Color.Warning">@GlobalResource.ErrorOccured</FluentLabel>
                                                        <FluentLabel Typo="Typography.Body" Style="color: chocolate;"> @GlobalResource.ErrorOccuredGuide</FluentLabel>
                                                    </div>
                                                </div>
                                            </div>
                                        </ErrorContent>
                                    </ErrorBoundary>
                                }
                                else
                                {
                                    <div class="content">
                                        <FluentCard Class="p-0">
                                            <ErrorBoundary @ref="errorBoundary">
                                                <ChildContent>
                                                    @Body
                                                </ChildContent>
                                                <ErrorContent>
                                                    <div class="px-3 pt-3 flex items-center justify-center min-h-full">
                                                        <div>
                                                            <div class="text-center">
                                                                <FluentLabel Typo="Typography.Header" Color="@Color.Warning">@GlobalResource.ErrorOccured</FluentLabel>
                                                                <FluentLabel Typo="Typography.Body" Style="color: chocolate;"> @GlobalResource.ErrorOccuredGuide</FluentLabel>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ErrorContent>
                                            </ErrorBoundary>
                                        </FluentCard>
                                    </div>
                                }

                            </ChildContent>
                            <ErrorContent Context="ex">
                                <div class="blazor-error-boundary">@ex.Message</div>
                            </ErrorContent>
                        </ErrorBoundary>
                    </FluentBodyContent>
                </FluentStack>
                @*  <FluentFooter>
        <div class="link1">
        <a href="https://www.fluentui-blazor.net" target="_blank">Documentation and demos</a>
        </div>
        <div class="link2">
        <a href="https://learn.microsoft.com/en-us/aspnet/core/blazor" target="_blank">About Blazor</a>
        </div>
        </FluentFooter> *@
            </FluentLayout>

            <FluentDialogProvider />
            <FluentMenuProvider />
            <FluentTooltipProvider />
            <FluentToastProvider MaxToastCount="10" />

            <div style="@("display:" + (!smNavMenuOpened ? "none" : "block")); width:260px; background-color: var(--neutral-fill-rest)" class="fixed top-0 left-0 bottom-0 z-50" onclick="@(() => { smNavMenuOpened = false; })">
                <NavMenu AlwaysExpanded="true"></NavMenu>
            </div>
        </div>
    </ChildContent>
    <ErrorContent>
        <div class="px-3 pt-3 flex items-center justify-center min-h-full">
            <div>
                <div class="text-center">
                    <FluentLabel Typo="Typography.Header" Color="@Color.Warning">@GlobalResource.ErrorOccured</FluentLabel>
                    <FluentAnchor Href="javascript:window.location.reload()">Reload</FluentAnchor>
                </div>
            </div>
        </div>
    </ErrorContent>
</ErrorBoundary>




<ScreenSizeTracker OnBreakpointChanged="OnBreakpointChanged" />