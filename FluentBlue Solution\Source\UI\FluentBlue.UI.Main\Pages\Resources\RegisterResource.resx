<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Title" xml:space="preserve">
    <value>Create Account</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>Fill in the information below to create your account</value>
  </data>
  <data name="FirstNameLabel" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="LastNameLabel" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="EmailLabel" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="UsernameLabel" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="PasswordLabel" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="ConfirmPasswordLabel" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="MobileLabel" xml:space="preserve">
    <value>Mobile Phone</value>
  </data>
  <data name="CreateAccountButton" xml:space="preserve">
    <value>Create Account</value>
  </data>
  <data name="BackToLoginButton" xml:space="preserve">
    <value>Back to Login</value>
  </data>
  <data name="SuccessMessage" xml:space="preserve">
    <value>Account created successfully! You can now sign in.</value>
  </data>
  <data name="ErrorMessage" xml:space="preserve">
    <value>An error occurred while creating your account. Please try again.</value>
  </data>
  <data name="UserExistsMessage" xml:space="preserve">
    <value>A user with this email or username already exists.</value>
  </data>
  <data name="PasswordMismatchMessage" xml:space="preserve">
    <value>Passwords do not match.</value>
  </data>
</root>
