﻿using FluentBlue.UI.Main;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{

    public class RestartAppService : IRestartAppService
    {
        public async Task Restart()
        {
            //MainThread.BeginInvokeOnMainThread(async () =>
            //{
            //    //App.Current.MainPage = new MainPage();
            //    Application.Current.MainPage = new MainPage(); 
            //});
            ProcessModule? module = Process.GetCurrentProcess().MainModule;
            string? filename = module?.FileName;
            if (filename != null)
            {
                // Start a new instance of the application
                Process.Start(filename);
                // Close the current process
                Application.Current?.Quit();

                //(Application.Current as App).MainPage.Dispatcher.Dispatch(() =>
                //{
                //    (Application.Current as App).MainPage = new MainPage();
                //});
            }
            else
            {
                 throw new InvalidOperationException("Unable to determine the application's executable path.");
            }

            await Task.CompletedTask;
        }
    }
}
