using System;
using System.Security.Cryptography;
using System.Text;

namespace FluentBlue.Shared.Cryptography.AES
{
    public interface IEncryptionService
    {
        string Encrypt(string plainText);
        string Decrypt(string encryptedText);
        string GenerateApplicationKey();
    }

    public class EncryptionService : IEncryptionService
    {
        private const int KeySize = 256;
        private const int BlockSize = 128;
        private static string applicationKey = string.Empty;

        public static void SetApplicationKey(string applicationKey)
        {
            EncryptionService.applicationKey = applicationKey ?? string.Empty;
        }

        public string Encrypt(string plainText)
        {
            if (string.IsNullOrEmpty(plainText) || string.IsNullOrEmpty(applicationKey))
                return plainText;

            try
            {
                using var aes = Aes.Create();
                aes.KeySize = KeySize;
                aes.BlockSize = BlockSize;

                // Derive encryption key from application key
                var derivedKey = DeriveKeyFromApplicationKey(applicationKey);
                aes.Key = derivedKey;
                aes.GenerateIV();

                byte[] encryptedData;

                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new MemoryStream())
                {
                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (var swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }

                    encryptedData = msEncrypt.ToArray();
                }

                // Combine IV and encrypted data
                var combined = new byte[aes.IV.Length + encryptedData.Length];
                Array.Copy(aes.IV, 0, combined, 0, aes.IV.Length);
                Array.Copy(encryptedData, 0, combined, aes.IV.Length, encryptedData.Length);

                return Convert.ToBase64String(combined);
            }
            catch (Exception ex)
            {
                throw new CryptographicException($"Encryption failed: {ex.Message}", ex);
            }
        }

        public string Decrypt(string encryptedText)
        {
            if (string.IsNullOrEmpty(encryptedText) || string.IsNullOrEmpty(applicationKey))
                return encryptedText;

            try
            {
                var combined = Convert.FromBase64String(encryptedText);

                // Extract IV and encrypted data
                var iv = new byte[16]; // AES block size is 16 bytes
                var encryptedData = new byte[combined.Length - 16];

                Array.Copy(combined, 0, iv, 0, 16);
                Array.Copy(combined, 16, encryptedData, 0, encryptedData.Length);

                using var aes = Aes.Create();
                aes.KeySize = KeySize;
                aes.BlockSize = BlockSize;

                // Derive decryption key from application key
                var derivedKey = DeriveKeyFromApplicationKey(applicationKey);
                aes.Key = derivedKey;
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(encryptedData);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);

                return srDecrypt.ReadToEnd();
            }
            catch (Exception ex)
            {
                throw new CryptographicException($"Decryption failed: {ex.Message}", ex);
            }
        }

        public string GenerateApplicationKey()
        {
            var bytes = new byte[32]; // 256 bits
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }
            return Convert.ToBase64String(bytes);
        }

        private byte[] DeriveKeyFromApplicationKey(string applicationKey)
        {
            using var deriveBytes = new Rfc2898DeriveBytes(
                applicationKey, 
                Encoding.UTF8.GetBytes("FluentBlue_Salt"), 
                10000, 
                HashAlgorithmName.SHA256);
            
            return deriveBytes.GetBytes(32); // 256 bits
        }
    }
} 