﻿using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared.Utilities;
using Microsoft.Extensions.Options;
using NodaTime.TimeZones;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Net;
using System.Runtime.CompilerServices;

namespace FluentBlue.Data.Model.DBOs.Settings
{
    [Table("UserSetting", Schema = "Settings")]
    public class UserSetting : IObjectState
    {
        public UserSetting()
        {
            UserSettingId = Guid.CreateVersion7();
            Language = Language.EnglishGeneral;
            TimeZone = "";
            //Culture = cultureInfo;
            DateFormat = "dd/MM/yyyy";
            TimeFormat = "HH:mm";
            ThemeColorMode = ThemeColorMode.Light;
            FirstDayOfWeek = DayOfWeek.Monday;
            ThemeColor = ThemeColor.Default;
            FullNameDisplay = FullNameDisplay.FirstLast;
            ShowTinInContactLists = false;
            ShowSsnInContactLists = false;
            ShowPhonesInContactLists = false;
            CalendarWorkDays ="12345";
            CalendarWorkStart = new TimeSpan(9, 0, 0);
            CalendarWorkEnd = new TimeSpan(17, 0, 0);
            CalendarTimeScaleInterval = TimeScaleInterval.SixtyMinutes;
            CalendarDefaultView = CalendarView.Week;
            CalendarDefaultViewOnMobiles = CalendarView.Agenda;
            CalendarScrollToTime = CalendarScrollToTime.None;
            DateModifiedUtc = DateTime.UtcNow;
            ObjectState = ObjectState.Unchanged;
        }

        [Key]
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.UserSettingId))]
        public Guid UserSettingId { get; set; }

        //[Required]
        public Guid UserId { get; set; }

        [ForeignKey("UserId")]
        public User? User { get; set; }

        #region General settings
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.TimeZone))]
        //[MaxLength(50)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string TimeZone { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.Language))]
        //[MaxLength(50)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public Language Language { get; set; } = Language.EnglishGeneral;

        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CultureField))]
        //[MaxLength(50)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Culture { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.DateFormat))]
        public string DateFormat
        {
            get; set;
        } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.TimeFormat))]
        public string TimeFormat
        {
            get; set;
        } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.ColorMode))]
        public ThemeColorMode ThemeColorMode
        {
            get; set;
        } = ThemeColorMode.Light;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.FirstDayOfWeek))]
        public DayOfWeek FirstDayOfWeek
        {
            get; set;
        } = DayOfWeek.Monday;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.Color))]
        public ThemeColor ThemeColor
        {
            get; set;
        } = 0; //Default, αντιστοιχεί στο Windows color του FluentUI.
        #endregion


        #region  Contacts settings
        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.FullNameDisplay))]
        public FullNameDisplay FullNameDisplay { get; set; } = FullNameDisplay.FirstLast;  //Καθορίζει πως θα εμφανίζεται το Ονοματεπώνυμο στις επαφές.

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.ShowTinInContactLists))]
        public bool ShowTinInContactLists { get; set; } = false; //Καθορίζει αν θα φαίνεται το TIN σε λίστες με επαφές.

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.ShowSsnInContactLists))]
        public bool ShowSsnInContactLists { get; set; } = false; //Καθορίζει αν θα φαίνεται το AMKA σε λίστες με επαφές.

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.ShowPhonesInContactLists))]
        public bool ShowPhonesInContactLists { get; set; } = false; //Καθορίζει αν θα φαίνεται τα τηλέφωνα σε λίστες με επαφές.
        #endregion


        #region  Calendar settings
        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarWorkDays))]
        public string CalendarWorkDays
        {
            get; set;
        } = "12345";

        //[NotZeroTimeSpan]
        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarWorkStart))]
        public TimeSpan CalendarWorkStart
        {
            get; set;
        } = new TimeSpan(9, 0, 0);


        //[NotZeroTimeSpan]
        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarWorkEnd))]
        public TimeSpan CalendarWorkEnd
        {
            get; set;
        } = new TimeSpan(17, 0, 0);

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarTimeScaleInterval))]
        public TimeScaleInterval CalendarTimeScaleInterval
        {
            get; set;
        } = TimeScaleInterval.SixtyMinutes;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarDefaultView))]
        public CalendarView CalendarDefaultView
        {
            get; set;
        } = CalendarView.Week;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarDefaultView))]
        public CalendarView CalendarDefaultViewOnMobiles
        {
            get; set;
        } = CalendarView.Agenda;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarScrollToTime))]
        public CalendarScrollToTime CalendarScrollToTime
        {
            get; set;
        } = CalendarScrollToTime.None;

        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.EventTextDisplay))]
        public EventTextDisplay EventTextDisplay { get; set; } = EventTextDisplay.Subject;  //Καθορίζει τι θα εμφανίζεται στο label του Event στο ημερολόγιο.


        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.EventTextSeparator))]
        public EventTextSeparator EventTextSeparator { get; set; } = EventTextSeparator.Space;  //Καθορίζει με ποιο σύμβολο θα διαχωρίζονται οι πληροφοριες στο label του Event στο ημερολόγιο.

        #endregion


        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        [Column(TypeName = "datetime")]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; } = new byte[0];

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        #region  Helper Properties
        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarWorkStart))]
        public DateTime? CalendarWorkStartDateTime
        {
            get
            {
                if (CalendarWorkStart == TimeSpan.Zero)
                {
                    return null;
                }
                else
                {
                    return DateTime.Today.Add(CalendarWorkStart);
                }
            }
            set
            {
                CalendarWorkStart = value == null ? TimeSpan.Zero : value.Value.TimeOfDay;
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Settings.GeneralSettingResource), Name = nameof(Model.Resources.Settings.GeneralSettingResource.CalendarWorkEnd))]
        public DateTime? CalendarWorkEndDateTime
        {
            get
            {
                if (CalendarWorkEnd == TimeSpan.Zero)
                {
                    return null;
                }
                else
                {
                    return DateTime.Today.Add(CalendarWorkEnd);
                }
            }
            set
            {
                CalendarWorkEnd = value == null ? TimeSpan.Zero : value.Value.TimeOfDay;
            }
        }

        //public static UserSetting CreateUserSetting(string languageText, string timeZone)
        //{
        //    Language? language;
        //    CultureInfo? cultureInfo;

        //    //If greek language was found in the user
        //    if (languageText == "el-GR" || languageText.Contains("el"))
        //    {
        //        language = Data.Model.Language.GreekGeneral;
        //        cultureInfo = new CultureInfo("el");
        //    }
        //    else
        //    {
        //        language = Data.Model.Language.EnglishGeneral;
        //        cultureInfo = CultureInfo.InvariantCulture;
        //    }

        //    if (TzdbDateTimeZoneSource.Default.ZoneLocations!.Select(x => (TzdbZoneLocation)x).Where(x => x.CountryName == timeZone).Any())
        //    {

        //    }


        //    return new UserSetting
        //    {
        //        UserSettingId = Guid.CreateVersion7(),
        //        Language = null,
        //        TimeZone = timeZone,
        //        //Culture = cultureInfo,
        //        DateFormat = "dd/MM/yyyy",
        //        TimeFormat = "HH:mm",
        //        ThemeColorMode = ThemeColorMode.Light,
        //        FirstDayOfWeek = cultureInfo.DateTimeFormat.FirstDayOfWeek,
        //        ThemeColor = ThemeColor.Default,
        //        ShowTinInContactLists = false,
        //        ShowSsnInContactLists = false,
        //        ShowPhonesInContactLists = false,
        //        CalendarWorkDays = (new int[] { 1, 2, 3, 4, 5 }).ToString(),
        //        CalendarWorkStart = new TimeSpan(9, 0, 0),
        //        CalendarWorkEnd = new TimeSpan(17, 0, 0),
        //        CalendarTimeScaleInterval = TimeScaleInterval.SixtyMinutes,
        //        CalendarDefaultView = CalendarView.Week,
        //        CalendarDefaultViewOnMobiles = CalendarView.Agenda,
        //        CalendarScrollToTime = CalendarScrollToTime.None,
        //        DateModifiedUtc = DateTime.UtcNow,
        //        ObjectState = ObjectState.Added
        //    };
        //}
        #endregion
    }
}
