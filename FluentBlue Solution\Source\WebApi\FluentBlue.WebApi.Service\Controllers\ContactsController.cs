﻿using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Diagnostics;


namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]  //[Route("v{version:apiVersion}/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class ContactsController : ControllerBase
    {
        private IContactsBusiness contactsBusiness;
        private IEventsBusiness eventsBusiness;
        private ILogger<ContactsController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public ContactsController(IContactsBusiness contactsBusiness, IEventsBusiness eventsBusiness, ILogger<ContactsController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            try
            {
                this.contactsBusiness = contactsBusiness;
                this.eventsBusiness = eventsBusiness;
                this.logger = logger;
                this.mapper = mapper;
                this.webApiCallerInfo = webApiCallerInfo;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }


        /// <summary>
        /// Retrieves contacts in paged mode.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Contacts/Get")]
        public async Task<ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>>> GetContacts([FromBody] RequestPagedContactsParameters parameters)
        {
            try
            {
                //Map data
                ReadPagedContactsParameters readPagedContactsParameters = this.mapper.Map<ReadPagedContactsParameters>(parameters);

                //Validation
                if (readPagedContactsParameters.TenantId != this.webApiCallerInfo.TenantId)
                {
                    return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = "TenantId missmacth." };
                }

                //Query
                PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>> data = await this.contactsBusiness.GetContacts(readPagedContactsParameters);
                
                //Response
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, parameters);
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        /// <summary>
        /// Returns all the Contacts of Tenant. This list should be used for displaying in lists, dropdowns.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Contacts/GetList")]
        //[ResponseCache(Duration = 30, VaryByQueryKeys = new[] { "*" })] // Cache for 5 minutes
        public async Task<ApiResponse<List<FluentBlue.Data.Model.DTOs.ContactLI>>> GetContactsList()
        {
            try
            {
                //Query
                List<FluentBlue.Data.Model.DTOs.ContactLI> data = await this.contactsBusiness.GetContactsLI(this.webApiCallerInfo.TenantId!.Value);

                //Response
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.ContactLI>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.ContactLI>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, this.webApiCallerInfo.TenantId!.Value);
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.ContactLI>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }


        [HttpGet]
        [Route("Contact/Get")]
        public async Task<ApiResponse<Contact?>> GetContact([FromQuery] string contactId)
        {
            try
            {
                // Query
                Contact? contact = await this.contactsBusiness.GetContact(Guid.Parse(contactId));

                // Validation
                if (contact?.TenantId != this.webApiCallerInfo.TenantId)
                {
                    return new ApiResponse<Contact?>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Ok };
                }

                // Response
                return new ApiResponse<Contact?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = contact };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Contact?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "ContactId=" + contactId);
                return new ApiResponse<Contact?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }



        //[HttpPost]
        //[Route("create")]
        //public async Task<ApiResponse<Transform>> CreateTransform([FromBody] Transform transform)
        //{
        //    try
        //    {
        //        //Validation
        //        if (transform == null)
        //        {
        //            throw new Exception(GlobalResources.InvalidDataMessage);
        //        }

        //        if (ModelState.IsValid == false)
        //        {
        //            throw new Exception(ModelState.ValidationState.ToString());
        //        }

        //        //Query
        //        this.dbContext.Add(transform);
        //        await this.dbContext.SaveChangesAsync();

        //        ////Αν δημιουργούμε νέο transform
        //        //if (transform.ObjectState == ObjectState.Added)
        //        //{
        //        //    //Ξεκινάει το Python
        //        //    ScriptRuntimeSetup setup = Python.CreateRuntimeSetup(null);
        //        //    ScriptRuntime runtime = new ScriptRuntime(setup);
        //        //    ScriptEngine engine = Python.GetEngine(runtime);
        //        //    ScriptSource source = engine.CreateScriptSourceFromFile("Python1.py");
        //        //    ScriptScope scope = engine.CreateScope();
        //        //    List<String> argv = new List<String>();
        //        //    //Do some stuff and fill argv
        //        //    argv.Add("foo");
        //        //    argv.Add("bar");
        //        //    engine.GetSysModule().SetVariable("argv", argv);
        //        //    source.Execute(scope);
        //        //}

        //        //Αν δημιουργούμε νέο transform
        //        if (transform.ObjectState == ObjectState.Added)
        //        {
        //            Task.Run(() => this.TransformFiles(transform, configuration.GetValue<string>("FileTransformerConnectionString")));
        //        }

        //        //Response
        //        return new ApiResponse<Transform>() { ResultCode = ApiResponseResultCode.Ok, Data = transform };
        //    }
        //    catch (Exception ex)
        //    {
        //        new ExceptionHandler(hostEnvironment, "FluentBlue.Api").LogException(ex);
        //        return new ApiResponse<Transform>() { ResultCode = ApiResponseResultCode.Exception, Exception = ex };
        //    }
        //}

        [HttpPost]
        [Route("Contact/CreateOrUpdate")]
        public async Task<ApiResponse<Contact>> CreateOrUpdateContact([FromBody] Contact contact)
        {
            try
            {
                //Validation
                if (contact == null)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Query
                await this.contactsBusiness.CreateOrUpdateContact(contact);

                //Response
                Contact? conctact = await this.contactsBusiness.GetContact(contact.ContactId);
                return new ApiResponse<Contact>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = contact };
            }
            catch (DbUpdateConcurrencyException ex)
            {
                return new ApiResponse<Contact>() { ResultCode = ApiResponseResultCode.DbConcurrencyException };
            }
            catch (ApplicationException ex)
            {
                this.logger.LogError(ex, ex.Message, contact);
                return new ApiResponse<Contact>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, contact);
                return new ApiResponse<Contact>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("Contact/Delete")]
        public ApiResponse DeleteContact([FromQuery] Guid contactId)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.

                this.contactsBusiness.DeleteContact(contactId);

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (DbUpdateConcurrencyException ex)
            {
                return new ApiResponse<Contact>() { ResultCode = ApiResponseResultCode.DbConcurrencyException };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "ContactId=" + contactId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        /// <summary>
        /// Retrieves all events of Contact.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("Contact/Events/GetAll")]
        public async Task<ApiResponse<List<Data.Model.DBOs.Calendar.Event>>> GetAllEventsOfContact([FromQuery] string contactId)
        {
            try
            {
                //Validation
                if (contactId == null || contactId == "")
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                //Query
                List<FluentBlue.Data.Model.DBOs.Calendar.Event> data = await this.eventsBusiness.GetAllEventsOfContact(Guid.Parse(contactId));

                //Response
                return new ApiResponse<List<Data.Model.DBOs.Calendar.Event>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<Data.Model.DBOs.Calendar.Event>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception exp)
            {
                //ExceptionHandler.RecordException(exp);
                return new ApiResponse<List<Data.Model.DBOs.Calendar.Event>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = exp.Message };
            }
        }
    }
}
