﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;

namespace FluentBlue.Data.Model.DBOs.Validators
{


    public class InsertUpdateEventValidator : AbstractValidator<Event>
    {
        public InsertUpdateEventValidator()
        {
            RuleFor(x => x.EventId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Event.EventId));
            RuleFor(x => x.TenantId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Event.TenantId));
            RuleFor(x => x.StartTimeUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Event.StartTimeLocal));
            RuleFor(x => x.EndTimeUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Event.EndTimeLocal));
            RuleFor(x => x.EndTimeLocal).Must((model, endDate) => model.StartTimeUtc < model.EndTimeUtc).WithLocalizedMessage(Data.Model.Resources.Calendar.EventResource.EndTimeMustBeAfterStartTime, nameof(Event.EndTimeLocal));  //We validate based on UTC dates because they always have values, while Local dates may not have values.
            RuleFor(x => x.EventUsers).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Event.EventUsers));
        }

        protected override bool PreValidate(ValidationContext<Event> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }


    }
}
