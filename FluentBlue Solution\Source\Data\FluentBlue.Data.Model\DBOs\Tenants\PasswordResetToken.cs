using FluentBlue.Shared;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FluentBlue.Data.Model.DBOs.Tenants
{
    [Table("PasswordResetToken", Schema = "Tenants")]
    public class PasswordResetToken : IObjectState
    {
        public PasswordResetToken()
        {
            TokenId = Guid.CreateVersion7();
            Token = string.Empty;
            DateCreatedUtc = DateTime.UtcNow;
            ExpiresUtc = DateTime.UtcNow.AddHours(24); // 24 hour expiry
            IsUsed = false;
            ObjectState = ObjectState.Unchanged;
        }

        [Key]
        public Guid TokenId { get; set; }

        [Required]
        public Guid UserId { get; set; }

        [ForeignKey("UserId")]
        public User? User { get; set; }

        [Required]
        [MaxLength(255)]
        public string Token { get; set; }

        [Required]
        public DateTime DateCreatedUtc { get; set; }

        [Required]
        public DateTime ExpiresUtc { get; set; }

        public bool IsUsed { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        public bool IsValid => !IsUsed && DateTime.UtcNow < ExpiresUtc;

        public static PasswordResetToken CreateToken(Guid userId)
        {
            var token = new PasswordResetToken
            {
                UserId = userId,
                Token = GenerateSecureToken(),
                ObjectState = ObjectState.Added
            };
            return token;
        }

        private static string GenerateSecureToken()
        {
            // Generate a cryptographically secure random token
            var bytes = new byte[32];
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }
            return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }
    }
}
