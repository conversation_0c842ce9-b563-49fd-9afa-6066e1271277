﻿using FluentBlue.Shared.Utilities;
using Microsoft.JSInterop;
using Syncfusion.Licensing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{
    class MapsNavigation : IMapsNavigation
    {
        private readonly IJSRuntime _jsRuntime;

        public MapsNavigation(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public async Task NavigateToAddress(string address)
        {
            try
            {
                var encodedAddress = Uri.EscapeDataString(address);
                Uri uri;

                uri = new Uri($"https://www.google.com/maps/dir/?api=1&destination={encodedAddress}");

                // JavaScript to open in new window
                var script = $"window.open('{uri}', '_blank')";
                await _jsRuntime.InvokeVoidAsync("eval", script);
            }
            catch
            {
                throw;
            }
        }
    }
}
