﻿CREATE TABLE [Tenants].[UserDevice] (
    [UserDeviceId]   UNIQUEIDENTIFIER CONSTRAINT [DF_UserDevice_UserDeviceId] DEFAULT (newsequentialid()) NOT NULL,
    [UserId]         UNIQUEIDENTIFIER NOT NULL,
    [DeviceToken]    VARCHAR (512)    NOT NULL,
    [Platform]       NVARCHAR (30)    NOT NULL,
    [DateUpdatedUtc] DATETIME2 (7)    CONSTRAINT [DF__UserDevic__LastU__1980B20F] DEFAULT (getutcdate()) NOT NULL,
    CONSTRAINT [PK__UserDevi__FD151C597047A931] PRIMARY KEY CLUSTERED ([UserDeviceId] ASC),
    CONSTRAINT [FK_UserDevice_User] FOREIGN KEY ([UserId]) REFERENCES [Tenants].[User] ([UserId]) ON DELETE CASCADE
);

