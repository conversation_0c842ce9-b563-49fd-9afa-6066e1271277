{
  "FluentBlueConnectionString": "Server=MAIN\\SQLEXPRESS;Database=FluentBlue;Trusted_Connection=True;Encrypt=False",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "SignalR": {
    "HubUrl": "https://********:5122/notificationHub" // Replace with your actual Hub URL
  },
  "WebApi": {
    "BaseUrl": "http://localhost:5122/"
  },
  "SmtpSettings": {
    "Host": "winzone59.grserver.gr",
    "Port": "587",
    "Username": "<EMAIL>",
    "Password": "infUser@33",
    "FromEmail": "<EMAIL>",
    "FromName": "FluentBlue"
  }
}
