﻿using FluentBlue.Shared.Utilities;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public enum ObjectState
    {
        Unchanged = 1,
        Added = 2,
        Modified = 3,
        Deleted = 4
    }

    #region  General
    public enum ThemeColorMode : byte
    {
        //System = 1,
        Light = 2,
        Dark = 3
    }

    public enum ThemeColor : byte
    {
        /// <summary>
        /// The default Fluent UI accent color
        /// </summary>
        [Description("#036ac4")]
        [Display(ResourceType = typeof(Data.Model.Resources.PhoneTypeResource), Name = nameof(Data.Model.Resources.PhoneTypeResource.CellPhoneType))]
        Default = 0,

        /// <summary/>
        [Description("#a4373a")]
        RedWine = 1,  //αντιστοιχεί στο Access

        /// <summary/>
        [Description("#00a99d")]
        Pinewater = 2,  //αντιστοιχεί στο Booking

        /// <summary/>
        [Description("#217346")]
        Nature = 3,  //αντιστοιχεί στο Excel

        /// <summary/>
        [Description("#00bcf2")]
        ElectricSky = 4,  //αντιστοιχεί στο GroupMe

        /// <summary/>
        [Description("#7719aa")]
        DeepViolet = 5, //αντιστοιχεί στο OneNote

        /// <summary/>
        [Description("#742774")]
        PurpleRain = 6,  //αντιστοιχεί στο PowerApps

        /// <summary/>
        [Description("#f2c811")]
        GolderSun = 7,  //αντιστοιχεί στο PowerBI

        /// <summary/>
        [Description("#bc1948")]
        Rose = 8,  //αντιστοιχεί στο Stream 

        /// <summary/>
        [Description("#008272")]
        TropicalSea = 9,  //αντιστοιχεί στο Sway

        /// <summary/>
        [Description("#6264a7")]
        SteelViolet = 10,  //αντιστοιχεί στο Teams

        ///// <summary/>
        //[Description("#3955a3")]
        //MidnightBlue = 11,  //αντιστοιχεί στο Visio

        /// <summary/>
        [Description("#2b579a")]
        SlateAzure = 12,  //αντιστοιχεί στο Word

        ///// <summary/>
        //[Description("#b7472a")]
        //PowerPoint = 13,

        ///// <summary/>
        //[Description("#31752f")]
        //Project = 14,

        ///// <summary/>
        //[Description("#077568")]
        //Publisher = 15,

        ///// <summary/>
        //[Description("#0078d4")]
        //SharePoint = 16,

        ///// <summary/>
        //[Description("#0078d4")]
        //Skype = 17,

        ///// <summary />
        //[Description("#0f6cbd")]
        //Outlook = 9,

        ///// <summary/>
        //[Description("#0078d4")]
        //Windows = 22,

        ///// <summary/>
        //[Description("#0078d4")]
        //Exchange = 3,

        ///// <summary/>
        //[Description("#d83b01")]
        //Office = 6,

        ///// <summary/>
        //[Description("#0078d4")]
        //OneDrive = 7,

        /// <summary/>
        //[Description("#31752f")]
        //Planner = 10,

        ///// <summary/>
        //[Description("#106ebe")]
        //Yammer = 24
    }

    public enum Language : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.LanguageResource), Name = nameof(Data.Model.Resources.LanguageResource.EnglishGeneral))]
        EnglishGeneral = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.LanguageResource), Name = nameof(Data.Model.Resources.LanguageResource.GreekGeneral))]
        GreekGeneral = 2
    }

    #endregion  General


    #region  Contacts
    public enum ContactType : byte
    {
        Customer = 1,
        Supplier = 2,
        Other = 3
    }

    public enum AddressType : byte
    {
        Home = 1,
        Work = 2
    }

    public enum EmailType : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.EmailTypeResource), Name = nameof(Data.Model.Resources.EmailTypeResource.PersonalEmailType))]
        Personal = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.EmailTypeResource), Name = nameof(Data.Model.Resources.EmailTypeResource.WorkEmailType))]
        Work = 2
    }

    public enum PhoneType : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.PhoneTypeResource), Name = nameof(Data.Model.Resources.PhoneTypeResource.CellPhoneType))]
        Cell = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.PhoneTypeResource), Name = nameof(Data.Model.Resources.PhoneTypeResource.HomePhoneType))]
        Home = 2,
        [Display(ResourceType = typeof(Data.Model.Resources.PhoneTypeResource), Name = nameof(Data.Model.Resources.PhoneTypeResource.WorkPhoneType))]
        Work = 3,
        [Display(ResourceType = typeof(Data.Model.Resources.PhoneTypeResource), Name = nameof(Data.Model.Resources.PhoneTypeResource.OtherPhoneType))]
        Other = 4
    }

    public enum FullNameDisplay : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.Contacts.ContactResource), Name = nameof(Data.Model.Resources.Contacts.ContactResource.FirstLast))]
        FirstLast = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.Contacts.ContactResource), Name = nameof(Data.Model.Resources.Contacts.ContactResource.LastFirst))]
        LastFirst = 2
    }

    #endregion  Contacts


    #region  Calendar
    public enum TimeScaleInterval : byte
    {

        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.SixtyMinutes))]
        SixtyMinutes = 60,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.ThirtyMinutes))]
        ThirtyMinutes = 30,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.TwentyMinutes))]
        TwentyMinutes = 20,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.FifteenMinutes))]
        FifteenMinutes = 15,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.TenMinutes))]
        TenMinutes = 10,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.FiveMinutes))]
        FiveMinutes = 5
    }

    public enum CalendarView : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarDayView))]
        Day = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarWeekView))]
        Week = 2,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarWorkWeekView))]
        WorkWeek = 3,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarMonthView))]
        Month = 4,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarAgendaView))]
        Agenda = 5,
    }

    public enum CalendarScrollToTime : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToNone))]
        None = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToOne))]
        One = 3,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToTwo))]
        Two = 5,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToThree))]
        Three = 7,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToFour))]
        Four = 9,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToFive))]
        Five = 11,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToSix))]
        Six = 13,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToSeven))]
        Seven = 15,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToEight))]
        Eight = 17,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToNine))]
        Nine = 19,
        [Display(ResourceType = typeof(Data.Model.Resources.Settings.GeneralSettingResource), Name = nameof(Data.Model.Resources.Settings.GeneralSettingResource.CalendarScrollToTen))]
        Ten = 21,
    }

    public enum ReminderTime : byte
    {
        //[Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.NoReminder))]
        //None = -1,

        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.ZeroMinReminder))]
        ZeroMinutes = 0,

        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.FiveMinReminder))]
        FiveMinutes = 1,

        //[Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.TenMinReminder))]
        //FifteenMinutes = 2,

        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.FifteenMinReminder))]
        FifteenMinutes = 3,

        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.ThirtyMinReminder))]
        ThirtyMinutes = 4,

        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.OneHourReminder))]
        OneHour = 5
    }

    public enum EventTextDisplay : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.EventDisplaySubject))]
        Subject = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.EventDisplaySubjectContactName))]
        SubjectContactName = 2,
        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.EventDisplayContactNameSubject))]
        ContactNameSubject = 3
    }

    public enum EventTextSeparator : byte
    {
        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.EventTextSeparatorSpace))]
        Space = 1,
        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.EventTextSeparatorComma))]
        Comma = 2,
        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.EventTextSeparatorDash))]
        Dash = 3,
        [Display(ResourceType = typeof(Data.Model.Resources.Calendar.EventResource), Name = nameof(Data.Model.Resources.Calendar.EventResource.EventTextSeparatorPipe))]
        Pipe = 4

    }
    #endregion  Calendar
}
