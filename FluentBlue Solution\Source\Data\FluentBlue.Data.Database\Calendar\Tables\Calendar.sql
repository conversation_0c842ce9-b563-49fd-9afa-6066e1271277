﻿CREATE TABLE [Calendar].[Calendar] (
    [CalendarId]      UNIQUEIDENTIFIER CONSTRAINT [DF_Calendars_CalendarId] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]        UNIQUEIDENTIFIER NOT NULL,
    [Name]            NVARCHAR (50)    CONSTRAINT [DF_Calendars_Name] DEFAULT ('') NOT NULL,
    [DateCreatedUtc]  DATETIME         CONSTRAINT [DF_Calendars_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc] DATETIME         CONSTRAINT [DF_Calendars_DateModifiedUtc] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_Calendars] PRIMARY KEY CLUSTERED ([CalendarId] ASC)
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueName]
    ON [Calendar].[Calendar]([TenantId] ASC, [Name] ASC);


GO
CREATE NONCLUSTERED INDEX [TenantIdIndex]
    ON [Calendar].[Calendar]([TenantId] ASC);

