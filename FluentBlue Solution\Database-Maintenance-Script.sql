-- =============================================
-- FluentBlue Database Maintenance Script
-- Password Reset and User Registration Features
-- =============================================
-- This script provides maintenance operations for:
-- 1. Cleaning up expired password reset tokens
-- 2. User account maintenance
-- 3. Database optimization
-- 4. Security auditing
-- =============================================

USE [FluentBlue]
GO

-- =============================================
-- 1. CLEANUP EXPIRED PASSWORD RESET TOKENS
-- =============================================

PRINT '============================================='
PRINT 'Starting Database Maintenance...'
PRINT '============================================='
PRINT ''

DECLARE @ExpiredTokens INT;
DECLARE @UsedTokens INT;

-- Count expired tokens
SELECT @ExpiredTokens = COUNT(*)
FROM [Tenants].[PasswordResetToken]
WHERE [ExpiresUtc] < GETUTCDATE() AND [IsUsed] = 0;

-- Count used tokens
SELECT @UsedTokens = COUNT(*)
FROM [Tenants].[PasswordResetToken]
WHERE [IsUsed] = 1;

PRINT '1. Password Reset Token Cleanup:'
PRINT '   Expired tokens to clean: ' + CAST(@ExpiredTokens AS VARCHAR(10));
PRINT '   Used tokens to clean: ' + CAST(@UsedTokens AS VARCHAR(10));

-- Execute cleanup
EXEC [Tenants].[CleanupExpiredPasswordResetTokens];

PRINT '   ✓ Cleanup completed'
PRINT ''

-- =============================================
-- 2. ANALYZE PASSWORD RESET USAGE
-- =============================================

PRINT '2. Password Reset Usage Analysis:'

-- Active tokens
DECLARE @ActiveTokens INT;
SELECT @ActiveTokens = COUNT(*)
FROM [Tenants].[PasswordResetToken]
WHERE [ExpiresUtc] > GETUTCDATE() AND [IsUsed] = 0;

PRINT '   Active tokens: ' + CAST(@ActiveTokens AS VARCHAR(10));

-- Tokens created in last 24 hours
DECLARE @RecentTokens INT;
SELECT @RecentTokens = COUNT(*)
FROM [Tenants].[PasswordResetToken]
WHERE [DateCreatedUtc] > DATEADD(HOUR, -24, GETUTCDATE());

PRINT '   Tokens created in last 24h: ' + CAST(@RecentTokens AS VARCHAR(10));

-- Most active users (password reset requests)
PRINT '   Top 5 users by password reset requests:'
SELECT TOP 5 
    u.Email,
    u.FirstName + ' ' + u.LastName as FullName,
    COUNT(prt.TokenId) as ResetRequests
FROM [Tenants].[User] u
INNER JOIN [Tenants].[PasswordResetToken] prt ON u.UserId = prt.UserId
WHERE prt.DateCreatedUtc > DATEADD(DAY, -30, GETUTCDATE())
GROUP BY u.Email, u.FirstName, u.LastName
ORDER BY COUNT(prt.TokenId) DESC;

PRINT ''

-- =============================================
-- 3. USER ACCOUNT ANALYSIS
-- =============================================

PRINT '3. User Account Analysis:'

-- Total users
DECLARE @TotalUsers INT;
SELECT @TotalUsers = COUNT(*) FROM [Tenants].[User];
PRINT '   Total users: ' + CAST(@TotalUsers AS VARCHAR(10));

-- Users by role
PRINT '   Users by role:'
SELECT 
    r.Name as RoleName,
    COUNT(u.UserId) as UserCount
FROM [Tenants].[Role] r
LEFT JOIN [Tenants].[User] u ON r.RoleId = u.RoleId
GROUP BY r.Name
ORDER BY COUNT(u.UserId) DESC;

-- Recent registrations (last 7 days)
DECLARE @RecentRegistrations INT;
SELECT @RecentRegistrations = COUNT(*)
FROM [Tenants].[User]
WHERE [DateCreatedUtc] > DATEADD(DAY, -7, GETUTCDATE());

PRINT '   New registrations (last 7 days): ' + CAST(@RecentRegistrations AS VARCHAR(10));

PRINT ''

-- =============================================
-- 4. SECURITY AUDIT
-- =============================================

PRINT '4. Security Audit:'

-- Users with weak passwords (for development/testing)
DECLARE @WeakPasswords INT;
SELECT @WeakPasswords = COUNT(*)
FROM [Tenants].[User]
WHERE LEN([Password]) < 6 OR [Password] IN ('password', '123456', 'admin', 'test');

IF @WeakPasswords > 0
BEGIN
    PRINT '   ⚠️  WARNING: ' + CAST(@WeakPasswords AS VARCHAR(10)) + ' users have weak passwords!';
    
    -- Show users with weak passwords (don't show actual passwords)
    SELECT 
        Email,
        Username,
        FirstName + ' ' + LastName as FullName,
        'Weak password detected' as SecurityIssue
    FROM [Tenants].[User]
    WHERE LEN([Password]) < 6 OR [Password] IN ('password', '123456', 'admin', 'test');
END
ELSE
BEGIN
    PRINT '   ✓ No obvious weak passwords detected';
END

-- Check for duplicate emails
DECLARE @DuplicateEmails INT;
SELECT @DuplicateEmails = COUNT(*)
FROM (
    SELECT Email, COUNT(*) as EmailCount
    FROM [Tenants].[User]
    GROUP BY Email
    HAVING COUNT(*) > 1
) duplicates;

IF @DuplicateEmails > 0
BEGIN
    PRINT '   ⚠️  WARNING: ' + CAST(@DuplicateEmails AS VARCHAR(10)) + ' duplicate email addresses found!';
END
ELSE
BEGIN
    PRINT '   ✓ No duplicate email addresses';
END

PRINT ''

-- =============================================
-- 5. DATABASE OPTIMIZATION
-- =============================================

PRINT '5. Database Optimization:'

-- Update statistics on password reset token table
UPDATE STATISTICS [Tenants].[PasswordResetToken];
PRINT '   ✓ Updated statistics for PasswordResetToken table';

-- Check index fragmentation
DECLARE @FragmentationLevel FLOAT;
SELECT @FragmentationLevel = AVG(avg_fragmentation_in_percent)
FROM sys.dm_db_index_physical_stats(
    DB_ID(), 
    OBJECT_ID('[Tenants].[PasswordResetToken]'), 
    NULL, NULL, 'LIMITED'
)
WHERE index_id > 0;

IF @FragmentationLevel > 30
BEGIN
    PRINT '   ⚠️  High index fragmentation detected: ' + CAST(@FragmentationLevel AS VARCHAR(10)) + '%';
    PRINT '   Consider rebuilding indexes';
END
ELSE
BEGIN
    PRINT '   ✓ Index fragmentation is acceptable: ' + CAST(@FragmentationLevel AS VARCHAR(10)) + '%';
END

PRINT ''

-- =============================================
-- 6. RECOMMENDATIONS
-- =============================================

PRINT '6. Maintenance Recommendations:'

-- Check if cleanup procedure should be scheduled
IF @ExpiredTokens + @UsedTokens > 100
BEGIN
    PRINT '   📅 Consider scheduling automatic cleanup of password reset tokens';
END

-- Check for high password reset activity
IF @RecentTokens > 50
BEGIN
    PRINT '   🔍 High password reset activity detected - investigate potential issues';
END

-- Check for old unused tokens
DECLARE @OldTokens INT;
SELECT @OldTokens = COUNT(*)
FROM [Tenants].[PasswordResetToken]
WHERE [DateCreatedUtc] < DATEADD(DAY, -7, GETUTCDATE()) AND [IsUsed] = 0;

IF @OldTokens > 0
BEGIN
    PRINT '   🧹 ' + CAST(@OldTokens AS VARCHAR(10)) + ' old unused tokens found - consider cleanup';
END

-- General recommendations
PRINT '   💡 General recommendations:';
PRINT '      - Run this maintenance script weekly';
PRINT '      - Monitor password reset patterns for security issues';
PRINT '      - Implement password hashing in production';
PRINT '      - Set up automated backups';
PRINT '      - Configure email monitoring for failed sends';

PRINT ''

-- =============================================
-- 7. MAINTENANCE SUMMARY
-- =============================================

PRINT '============================================='
PRINT 'Database Maintenance Complete!'
PRINT '============================================='
PRINT 'Summary:'
PRINT '  ✓ Cleaned up expired/used password reset tokens'
PRINT '  ✓ Analyzed user account statistics'
PRINT '  ✓ Performed security audit'
PRINT '  ✓ Optimized database performance'
PRINT '  ✓ Generated maintenance recommendations'
PRINT ''
PRINT 'Next maintenance recommended: ' + CONVERT(VARCHAR, DATEADD(DAY, 7, GETUTCDATE()), 120)
PRINT '============================================='

-- =============================================
-- 8. CREATE MAINTENANCE LOG ENTRY (Optional)
-- =============================================

-- Uncomment the following section if you want to log maintenance activities

/*
-- Create maintenance log table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MaintenanceLog]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[MaintenanceLog] (
        [LogId] INT IDENTITY(1,1) PRIMARY KEY,
        [MaintenanceDate] DATETIME DEFAULT GETUTCDATE(),
        [MaintenanceType] NVARCHAR(100),
        [Details] NVARCHAR(MAX),
        [TokensCleaned] INT,
        [RecommendationsCount] INT
    );
END

-- Log this maintenance session
INSERT INTO [dbo].[MaintenanceLog] (
    [MaintenanceType], 
    [Details], 
    [TokensCleaned], 
    [RecommendationsCount]
)
VALUES (
    'Password Reset Maintenance',
    'Cleaned expired tokens, analyzed usage, performed security audit',
    @ExpiredTokens + @UsedTokens,
    5 -- Number of recommendation categories checked
);
*/

GO
