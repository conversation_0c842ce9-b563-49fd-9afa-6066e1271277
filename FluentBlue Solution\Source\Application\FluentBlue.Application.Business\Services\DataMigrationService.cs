using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared.Cryptography.AES;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business.Services
{
    public class DataMigrationService : IDataMigrationService
    {
        private readonly FluentBlueDbContext dbContext;
        private readonly IEncryptionService encryptionService;
        private readonly ILogger<DataMigrationService> logger;

        public DataMigrationService(
            FluentBlueDbContext dbContext,
            IEncryptionService encryptionService,
            ILogger<DataMigrationService> logger)
        {
            this.dbContext = dbContext;
            this.encryptionService = encryptionService;
            this.logger = logger;
        }

        public async Task<bool> IsEncryptionMigrationRequiredAsync()
        {
            try
            {
                // Check if there's any unencrypted data by looking for non-base64 strings
                // This is a simple heuristic - in production you might want a more sophisticated approach
                var hasUnencryptedData = await dbContext.Contacts
                    .AsNoTracking()
                    .AnyAsync(c => 
                        !IsBase64String(c.FirstName) || 
                        !IsBase64String(c.LastName) || 
                        !IsBase64String(c.SSN) || 
                        !IsBase64String(c.TIN));

                return hasUnencryptedData;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error checking if encryption migration is required");
                return false;
            }
        }

        public async Task MigrateExistingDataToEncryptionAsync()
        {
            try
            {
                logger.LogInformation("Starting encryption migration for all data");

                // Get all contacts with their related entities
                var contacts = await dbContext.Contacts
                    .Include(c => c.Emails)
                    .Include(c => c.Phones)
                    .ToListAsync();

                var migratedCount = 0;

                foreach (var contact in contacts)
                {
                    var contactModified = false;

                    // Check and mark contact fields for encryption
                    if (!IsBase64String(contact.FirstName) && !string.IsNullOrEmpty(contact.FirstName))
                    {
                        contact.ObjectState = ObjectState.Modified;
                        contactModified = true;
                    }

                    if (!IsBase64String(contact.LastName) && !string.IsNullOrEmpty(contact.LastName))
                    {
                        contact.ObjectState = ObjectState.Modified;
                        contactModified = true;
                    }

                    if (!IsBase64String(contact.MiddleName) && !string.IsNullOrEmpty(contact.MiddleName))
                    {
                        contact.ObjectState = ObjectState.Modified;
                        contactModified = true;
                    }

                    if (!IsBase64String(contact.SSN) && !string.IsNullOrEmpty(contact.SSN))
                    {
                        contact.ObjectState = ObjectState.Modified;
                        contactModified = true;
                    }

                    if (!IsBase64String(contact.TIN) && !string.IsNullOrEmpty(contact.TIN))
                    {
                        contact.ObjectState = ObjectState.Modified;
                        contactModified = true;
                    }

                    if (!IsBase64String(contact.Notes) && !string.IsNullOrEmpty(contact.Notes))
                    {
                        contact.ObjectState = ObjectState.Modified;
                        contactModified = true;
                    }

                    // Check and mark email fields for encryption
                    foreach (var email in contact.Emails)
                    {
                        if (!IsBase64String(email.EmailAddress) && !string.IsNullOrEmpty(email.EmailAddress))
                        {
                            email.ObjectState = ObjectState.Modified;
                            contactModified = true;
                        }
                    }

                    // Check and mark phone fields for encryption
                    foreach (var phone in contact.Phones)
                    {
                        if (!IsBase64String(phone.PhoneNumber) && !string.IsNullOrEmpty(phone.PhoneNumber))
                        {
                            phone.ObjectState = ObjectState.Modified;
                            contactModified = true;
                        }
                    }

                    if (contactModified)
                    {
                        migratedCount++;
                    }
                }

                if (migratedCount > 0)
                {
                    await dbContext.SaveChangesAsync();
                    logger.LogInformation("Migrated {Count} contacts to encryption", migratedCount);
                }
                else
                {
                    logger.LogInformation("No data requires migration - all data is already encrypted");
                }

                logger.LogInformation("Completed encryption migration for all data");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during encryption migration");
                throw;
            }
        }

        private bool IsBase64String(string value)
        {
            if (string.IsNullOrEmpty(value))
                return true; // Consider empty strings as "encrypted"

            try
            {
                Convert.FromBase64String(value);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 