﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Components;
using FluentBlue.UI.Main.Helpers;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor.Inputs;
using Syncfusion.Blazor.Lists;
using Syncfusion.Blazor.Popups;
using System.Security.Cryptography;
using System.Text;
using static Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20;

namespace FluentBlue.UI.Main.Pages
{
    public partial class Contacts
    {
        [Inject]
        private HybridCache cache { get; set; } = default!;

        //Contacts
        IQueryable<Data.Model.DTOs.ContactView> contacts = null; // new List<Data.Model.DTOs.ContactView>().AsQueryable();
        PaginationState pagination = new PaginationState { ItemsPerPage = 10 };


        List<ContactCategory>? contactCategories = new List<Data.Model.DBOs.Contacts.ContactCategory>();  //We store all ContactCategories from database (clear data, no manipulation).
        List<SelectableContactCategory>? selectableContactCategories = new List<SelectableContactCategory>();  //We store the ContactCategories but casted to type of SelectableContactCategory so as to be selected by user in menu (with the checkbox).
        bool noContactCategoryMenuItemChecked = false;
        FluentMenuItem editContactMenuItem = new FluentMenuItem();

        //General
        bool loading = false;
        bool mobileView = false;
        FluentSearch? searchTxtBox;
        bool filtersMenuOpened = false;
        IDialogReference? dialog = null;
        UserSetting? userSetting;
        FluentDataGrid<ContactView>? contactsDataGrid = new();
        GridSort<ContactView> fullNameSortBy = GridSort<ContactView>.ByAscending(x => x.FullName);
        private SummaryDataDto? summaryData = new();
        private FluentMenu? contactsDataGridMenu;
        private Guid? selectedContactId;
        //private bool prerequisiteDataInitialized = false;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                pagination.TotalItemCountChanged += (sender, eventArgs) => StateHasChanged();

                await cache.RemoveByTagAsync("Deletable");
                await cache.RemoveAsync("UserSettings");

               
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                //// If one the prerequicite data is null 
                //if (this.userSetting == null || this.contactCategories == null || this.summaryData == null)
                //{
                //    return;
                //}

                if (firstRender)
                {
                    #region Reads the UserSettings from cache or database
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);
                    #endregion

                    #region  Reads the ContactCategories and sets the filter menu.
                    var contactCategoriesCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
                    this.contactCategories = await cache.GetOrCreateAsync(
                        Keywords.ContactCategories,
                        async cancel =>
                        {
                            ContactCategoriesWebApiClient contactCategoriesWebApiClient = new ContactCategoriesWebApiClient(httpClient, contactCategoriesWebApiClientLogger);
                            return await contactCategoriesWebApiClient.GetAllContactCategories(AuthenticatedUserData.TenantId);
                        },
                        contactCategoriesCacheOptions,
                        new List<string> { "Deletable" }
                    );

                    //Converts the ContactCategories to SelectableContactCategories to be used in the filter menu.
                    this.selectableContactCategories = this.contactCategories.Select(cc => new SelectableContactCategory
                    {
                        ContactCategoryId = cc.ContactCategoryId,
                        Name = cc.Name,
                        IsSelected = true
                    }).ToList();
                    this.noContactCategoryMenuItemChecked = true;
                    //selectableContactCategories.Insert(0, new SelectableContactCategory { ContactCategoryId = new Guid("11111111-1111-1111-1111-111111111111"), Name = Resources.ContactsResource.NoContactCategory, IsSelected = true });  //Insert the "All Contacts" item at the beginning of the list (at index 0)
                    #endregion

                    #region Διαβάζει τα SummaryData από το cache
                    var generalDataCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(20) };
                    this.summaryData = await cache.GetOrCreateAsync(
                        Keywords.SummaryData,
                        async cancel =>
                        {
                            GeneralWebApiClient generalDataWebApiClient = new GeneralWebApiClient(httpClient, generalWebApiClientLogger);
                            return await generalDataWebApiClient.GetSummaryData(DateTime.Now, userSetting!.TimeZone);
                        },
                        generalDataCacheOptions,
                        new List<string> { "Deletable" }
                    );
                    #endregion

                    //this.prerequisiteDataInitialized = true;

                    //Set the page size of the DataGrid based on total Contacts.
                    if (this.summaryData != null && this.summaryData.ContactsCount > 290)  //Aν οι Contacts είναι λίγο πιο κάτω από 300 (αφήνουμε ένα περιθώριο)
                    {
                        this.pagination.ItemsPerPage = 10;
                    }
                    else
                    {
                        this.pagination.ItemsPerPage = 300;
                    }

                    await this.LoadContacts();
                }
                else
                {

                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task LoadContacts()
        {
            try
            {
                this.loading = true;

                string filter = this.searchTxtBox!.Value ?? "";

                #region Gets the selected SelectableContactCategories from menu.
                List<Guid> selectedContactCategoryIds = new List<Guid>();
                if (this.selectableContactCategories != null)
                {
                    //If ALL items are unchecked. 
                    if (noContactCategoryMenuItemChecked == false && (!this.selectableContactCategories.Where(x => x.IsSelected == true).Any()))
                    {
                        selectedContactCategoryIds.Add(new Guid("*************-9999-9999-************"));  //Adds a dummy Guid so as to get none Contacts.
                    }
                    //If the user has left unchecked any of the ContactCategories in filter menu, then we pass ontly the selected ones to the WebApi. 
                    else if (noContactCategoryMenuItemChecked == false || this.selectableContactCategories.Where(x => x.IsSelected == false).Any())
                    {
                        selectedContactCategoryIds = this.selectableContactCategories!.Where(cc => cc.IsSelected).Select(cc => cc.ContactCategoryId).ToList();
                        if (noContactCategoryMenuItemChecked == true)  // If user selected the "NoneContactCategory" item, then we add the Guid.Empty to the list.
                        {
                            selectedContactCategoryIds.Add(Guid.Empty);
                        }
                    }
                    else
                    {
                        //Otherwise we leave the selectedContactCategoryIds empty so as to get all ContactCategories.
                    }
                }
                #endregion

                Task<PagedData<List<Data.Model.DTOs.ContactView>>> contactsTask = new WebApi.Client.ContactsWebApiClient(httpClient, contactsWebApiClientLogger).GetContacts(AuthenticatedUserData.TenantId, filter, selectedContactCategoryIds.ToArray(), (UInt16)(this.pagination.CurrentPageIndex + 1), (UInt16)this.pagination.ItemsPerPage);
                PagedData<List<Data.Model.DTOs.ContactView>> contactsPagedData = await contactsTask;
                if (contactsPagedData.Data != null)
                {
                    this.contacts = contactsPagedData.Data.AsQueryable();
                    await this.pagination.SetTotalItemCountAsync(contactsPagedData.DataTotalCount);
                }
                //await this.contactsDataGrid!.RefreshDataAsync(true);
                this.StateHasChanged();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                //await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                throw;
            }
            finally
            {
                this.loading = false;
                this.StateHasChanged();
            }
        }

        private async Task ShowContact(Data.Model.DBOs.Contacts.Contact contact)
        {
            try
            {
                string dialogWidth = "1100px", dialogHeight = "90%";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm" || ScreenSizeTracker.CurrentBreakpoint == "md")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                //εμφανίζει το dialog
                DialogParameters<FluentBlue.UI.Main.Shared.ContactDialogInput> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnContactDialogResult),
                    Title = UI.Main.Components.Resources.ContactDialogResource.Title,
                    PrimaryAction = "",  //GlobalResource.Save,
                    SecondaryAction = "", //=GlobalResource.Cancel,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                ContactDialogInput contactDialogInput = new ContactDialogInput() { Contact = contact, Restricted = false };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.ContactDialog>(contactDialogInput, parameters);
                DialogResult? result = await dialog.Result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnContactDialogResult(DialogResult result)
        {
            if (result.Cancelled == false)
            {
                await this.LoadContacts();
            }
            if (dialog != null)
            {
                await dialog.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.
            }
        }

        private async Task NewContactBtnOnClick()
        {
            try
            {
                Contact contact = Contact.CreateContact(AuthenticatedUserData.TenantId);
                contact.UserTimeZoneId = this.userSetting!.TimeZone;
                await this.ShowContact(contact);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnEditContact(Guid contactId)
        {
            try
            {
                ContactsWebApiClient contactsWebApiClient = new ContactsWebApiClient(httpClient, contactsWebApiClientLogger);
                Data.Model.DBOs.Contacts.Contact? contact = await contactsWebApiClient.GetContact(contactId);

                if (contact != null)
                {
                    contact.UserTimeZoneId = this.userSetting!.TimeZone;
                    await this.ShowContact(contact);
                }
                else
                {
                    var dialog = await dialogService.ShowInfoAsync(Pages.Resources.ContactsResource.ContactNotExists);  //TODO: ίσως να μπει ερώτηση στο χρήστη αν θέλει να γίνει αυτόματα το sync.
                    DialogResult? result = await dialog.Result;
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnDeleteContact(Guid contactId)
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();
                if (result.Cancelled == false)
                {
                    ContactsWebApiClient contactsWebApiClient = new ContactsWebApiClient(httpClient, contactsWebApiClientLogger);
                    await contactsWebApiClient.DeleteContact(contactId);

                    await this.LoadContacts();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                this.loading = false;
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                this.loading = false;
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                this.loading = false;
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
                this.loading = false;
            }
        }

        private async void Search_ValueChanged()
        {
            try
            {
                if (this.searchTxtBox.Value == "")
                {
                    await this.pagination.SetCurrentPageIndexAsync(0);  //Αφού ξεκινάμε νέα αναζήτηση αρχικοποιούμε τον index αλλιώς κάποιες φορές δεν εμφανίζονται τα αποτελέσματα.
                    await this.LoadContacts();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task Search_OnKeyUp(KeyboardEventArgs args)
        {
            try
            {
                if (args.Key == "Enter")
                {
                    await this.pagination.SetCurrentPageIndexAsync(0);  //Αφού ξεκινάμε νέα αναζήτηση αρχικοποιούμε τον index αλλιώς κάποιες φορές δεν εμφανίζονται τα αποτελέσματα.
                    await this.LoadContacts();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task Pagination_CurrentPageIndexChanged(int args)
        {
            try
            {
                await this.LoadContacts();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void ContactsDataGridOnRowDoubleClick(FluentDataGridRow<ContactView> e)
        {
            try
            {
                if (e.Item != null)
                {
                    await OnEditContact(e.Item!.ContactId);
                }
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void ContactsDataGridOnRowClick(FluentDataGridRow<ContactView> e)
        {
            try
            {
                if (e.Item != null && this.contactsDataGridMenu != null)
                {
                    //{
                    //    //await OnEditContact(e.Item!.ContactId);
                    this.contactsDataGridMenu.Data = e.Item;
                    //    //this.editContactMenuItem.OnClick = EventCallback.Factory.Create<MouseEventArgs>(this, async (MouseEventArgs ev) => await OnEditContact(e.Item.ContactId));
                    //    // OnClick = EventCallback.Factory.Create<MouseEventArgs>(this, async (MouseEventArgs ev) => await OnEditContact(e.Item.ContactId))

                }
            }
            catch (ApplicationException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void ContactsDataGridOnCellClick(FluentDataGridCell<ContactView> e)
        {
            try
            {
                if (e.Item != null)
                {
                    selectedContactId = e.Item.ContactId;
                    //this.contactsDataGridMenu.Anchor = th;
                    //this.contactsDataGridMenu.Data = e.Item;
                    //this.editContactMenuItem.OnClick = EventCallback.Factory.Create<MouseEventArgs>(this, async (MouseEventArgs ev) => await OnEditContact(e.Item.ContactId));
                    // OnClick = EventCallback.Factory.Create<MouseEventArgs>(this, async (MouseEventArgs ev) => await OnEditContact(e.Item.ContactId))
                    //OnClick = EventCallback.Factory.Create<MouseEventArgs>(this, async (MouseEventArgs ev) => await OnDeleteContact(e.Item.ContactId))
                }
                else
                {
                    if (this.contactsDataGridMenu != null)
                    {
                        this.contactsDataGridMenu.Open = false;
                    }
                }

                if (e.CellType == DataGridCellType.ColumnHeader && e.AdditionalAttributes != null && e.AdditionalAttributes["aria-sort"] != null)
                {
                    if ((e.AdditionalAttributes["aria-sort"]?.ToString() ?? "") != (e.Data?.ToString() ?? ""))
                    {
                        e.Data = e.AdditionalAttributes["aria-sort"].ToString();  //TODO: το sorting δεν δουλεύει (δεν υποστηρίζει το DataGrid), θέλει φτιάξιμο.

                        await this.LoadContacts();
                    }
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        //private async Task ShowContextMenu(MouseEventArgs e)
        //{

        //    await this.contactsDataGridMenu.OpenAsync((int)e.ClientX, (int)e.ClientY);
        //}

        private async Task ExportContactsBtnOnClick()
        {
            try
            {

            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ContactCategoryFilterOnClick(Guid clickedContactCategoryId)
        {
            try
            {
                //If user clicked the "None ContactCategory" item.
                if (clickedContactCategoryId == Guid.Empty)
                {
                    noContactCategoryMenuItemChecked = !noContactCategoryMenuItemChecked;
                    await this.pagination.SetCurrentPageIndexAsync(0);  //Αφού ξεκινάμε νέα αναζήτηση αρχικοποιούμε τον index αλλιώς κάποιες φορές δεν εμφανίζονται τα αποτελέσματα.
                    await this.LoadContacts();
                }
                else  //For all other items.
                {
                    if (this.selectableContactCategories != null)
                    {
                        foreach (SelectableContactCategory selectableContactCategory in this.selectableContactCategories)
                        {
                            if (selectableContactCategory.ContactCategoryId == clickedContactCategoryId)
                            {
                                selectableContactCategory.IsSelected = !selectableContactCategory.IsSelected;
                                await this.pagination.SetCurrentPageIndexAsync(0);  //Αφού ξεκινάμε νέα αναζήτηση αρχικοποιούμε τον index αλλιώς κάποιες φορές δεν εμφανίζονται τα αποτελέσματα.
                                await this.LoadContacts();
                                break;
                            }
                        }
                    }
                }
                this.StateHasChanged();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        //private void EditContactMenuItemOnClick()
        //{
        //    if (selectedContactId.HasValue)
        //    {
        //        OnEditContact(selectedContactId.Value);
        //    }
        //}

        //private void DeleteContactMenuItemOnClick()
        //{
        //    if (selectedContactId.HasValue)
        //    {
        //        OnDeleteContact(selectedContactId.Value);
        //    }
        //}

        //private async Task EditContactMenuItemOnClick(MouseEventArgs args)
        //{
        //    if (this.contactsDataGridMenu.Data != null)
        //    {
        //        int i = 0;
        //    }
        //}
    }
}
