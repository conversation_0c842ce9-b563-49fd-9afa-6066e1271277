using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using FluentBlue.Shared.Cryptography.AES;
using System;
using System.Linq.Expressions;

namespace FluentBlue.Data.Model.Extensions
{
    public class EncryptionValueConverter : ValueConverter<string, string>
    {
        private static readonly IEncryptionService encryptionService = new EncryptionService();

        public EncryptionValueConverter() : base(
            // Convert to database (encrypt)
            v => EncryptValue(v),
            // Convert from database (decrypt)
            v => DecryptValue(v))
        {
        }

        private static string EncryptValue(string value)
        {
            if (string.IsNullOrEmpty(value))
                return value;

            try
            {
                return encryptionService.Encrypt(value);
            }
            catch (Exception)
            {
                // If encryption fails, return original value to prevent data loss
                return value;
            }
        }

        private static string DecryptValue(string value)
        {
            if (string.IsNullOrEmpty(value))
                return value;

            try
            {
                // Check if the value is encrypted (base64 format)
                if (IsBase64String(value))
                {
                    return encryptionService.Decrypt(value);
                }
                return value; // Return as-is if not encrypted
            }
            catch (Exception)
            {
                // If decryption fails, return original value to prevent data loss
                return value;
            }
        }

        private static bool IsBase64String(string value)
        {
            try
            {
                Convert.FromBase64String(value);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 