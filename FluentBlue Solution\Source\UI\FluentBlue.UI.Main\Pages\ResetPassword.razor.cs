using FluentBlue.Shared;
using FluentBlue.UI.Main.Pages.Resources;
using FluentBlue.UI.Main.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.UI.Main.Pages
{
    public partial class ResetPassword
    {
        public class ResetPasswordData
        {
            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [MaxLength(30)]
            public string NewPassword { get; set; } = string.Empty;

            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            [Compare(nameof(NewPassword), ErrorMessageResourceName = "PasswordMismatchMessage", ErrorMessageResourceType = typeof(ResetPasswordResource))]
            public string ConfirmPassword { get; set; } = string.Empty;
        }

        [Parameter]
        [SupplyParameterFromQuery(Name = "token")]
        public string? Token { get; set; }

        private ResetPasswordData resetPasswordData = new ResetPasswordData();
        private bool isLoading = false;
        private bool isPasswordReset = false;
        private bool isTokenValid = false;
        private string resultMessage = " ";
        private string tokenValidationMessage = "";

        protected override async Task OnInitializedAsync()
        {
            await ValidateToken();
        }

        private async Task ValidateToken()
        {
            try
            {
                if (string.IsNullOrEmpty(Token))
                {
                    tokenValidationMessage = Resources.ResetPasswordResource.InvalidTokenMessage;
                    isTokenValid = false;
                    return;
                }

                // Validate the token using the API
                var validationResponse = await authenticationWebApiClient.ValidateResetTokenAsync(Token);
                isTokenValid = validationResponse.IsValid;
                
                if (!isTokenValid)
                {
                    tokenValidationMessage = Resources.ResetPasswordResource.InvalidTokenMessage;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error validating reset token");
                tokenValidationMessage = Resources.ResetPasswordResource.InvalidTokenMessage;
                isTokenValid = false;
            }
        }

        private async void OnSubmit()
        {
            try
            {
                this.isLoading = true;
                this.resultMessage = " ";

                // Validate password confirmation
                if (resetPasswordData.NewPassword != resetPasswordData.ConfirmPassword)
                {
                    this.resultMessage = Resources.ResetPasswordResource.PasswordMismatchMessage;
                    return;
                }

                // Call the API to reset the password
                await authenticationWebApiClient.ResetPasswordAsync(Token!, resetPasswordData.NewPassword);
                
                this.isPasswordReset = true;
            }
            catch (ApplicationException ex)
            {
                // Handle specific application exceptions (like invalid token)
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
                this.resultMessage = ex.Message;
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
                logger.LogError(ex, ex.Message);
                this.resultMessage = Resources.ResetPasswordResource.ErrorMessage;
            }
            finally
            {
                this.isLoading = false;
                this.StateHasChanged();
            }
        }


    }
}
