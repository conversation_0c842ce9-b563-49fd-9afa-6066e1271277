﻿using Microsoft.Extensions.Caching.Hybrid;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main
{
    public class PredefinedHybridCacheOptions
    {
        public static readonly HybridCacheEntryOptions UserSettingCacheOptions = new() { LocalCacheExpiration = TimeSpan.FromHours(500) };
        public static readonly HybridCacheEntryOptions UsersCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
        public static readonly HybridCacheEntryOptions EventCategoriesCacheOptions = new() { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
        public static readonly HybridCacheEntryOptions EventStatesCacheOptions = new() { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
        public static readonly HybridCacheEntryOptions CalendarLastViewCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
        public static readonly HybridCacheEntryOptions LastSelectedDisplayUsersCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };  //Δεν θέλουμε να καθαρίσει όσο η εφαρμογή τρέχει.
        public static readonly HybridCacheEntryOptions UsersListCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(30) };  // Χρησιμοποιείται για να αποθηκεύει UserLI που εμφανίζονται σε λίστα Users.
        public static readonly HybridCacheEntryOptions ContactsListCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(30) };  // Χρησιμοποιείται για να αποθηκεύει ContactLI που εμφανίζονται σε λίστα Contacts.
    }
}
