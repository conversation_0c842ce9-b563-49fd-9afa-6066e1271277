<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ColorMode" xml:space="preserve">
    <value>Λειτουργία χρώματος</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Γλώσσα</value>
  </data>
  <data name="TimeZone" xml:space="preserve">
    <value>Ζώνη ώρας</value>
  </data>
  <data name="UserSettingId" xml:space="preserve">
    <value>Αναγνωριστικό Ρυθμίσεων Χρήστη</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Χρώμα</value>
  </data>
  <data name="CalendarWorkDays" xml:space="preserve">
    <value>Εργάσιμες ημέρες</value>
  </data>
  <data name="CalendarWorkEnd" xml:space="preserve">
    <value>Ώρα λήξης</value>
  </data>
  <data name="CalendarWorkStart" xml:space="preserve">
    <value>Ώρα έναρξης</value>
  </data>
  <data name="FirstDayOfWeek" xml:space="preserve">
    <value>Πρώτη ημέρα της εβδομάδας</value>
  </data>
  <data name="CalendarTimeScaleInterval" xml:space="preserve">
    <value>Χρονικό διάστημα</value>
  </data>
  <data name="SixtyMinutes" xml:space="preserve">
    <value>60 λεπτά</value>
  </data>
  <data name="FifteenMinutes" xml:space="preserve">
    <value>15 λεπτά</value>
  </data>
  <data name="FiveMinutes" xml:space="preserve">
    <value>5 λεπτά</value>
  </data>
  <data name="TenMinutes" xml:space="preserve">
    <value>10 λεπτά</value>
  </data>
  <data name="ThirtyMinutes" xml:space="preserve">
    <value>30 λεπτά</value>
  </data>
  <data name="TwentyMinutes" xml:space="preserve">
    <value>20 λεπτά</value>
  </data>
  <data name="CalendarAgendaView" xml:space="preserve">
    <value>Ατζέντα</value>
  </data>
  <data name="CalendarDayView" xml:space="preserve">
    <value>Ημέρα</value>
  </data>
  <data name="CalendarDefaultView" xml:space="preserve">
    <value>Προεπιλεγμένη προβολή</value>
  </data>
  <data name="CalendarDefaultViewOnMobiles" xml:space="preserve">
    <value>Προεπιλεγμένη προβολή σε κινητά</value>
  </data>
  <data name="CalendarMonthView" xml:space="preserve">
    <value>Μήνας</value>
  </data>
  <data name="CalendarScrollToEight" xml:space="preserve">
    <value>08:00</value>
  </data>
  <data name="CalendarScrollToFive" xml:space="preserve">
    <value>05:00</value>
  </data>
  <data name="CalendarScrollToFour" xml:space="preserve">
    <value>04:00</value>
  </data>
  <data name="CalendarScrollToNine" xml:space="preserve">
    <value>09:00</value>
  </data>
  <data name="CalendarScrollToNone" xml:space="preserve">
    <value>Χωρίς κύλιση</value>
  </data>
  <data name="CalendarScrollToOne" xml:space="preserve">
    <value>01:00</value>
  </data>
  <data name="CalendarScrollToSeven" xml:space="preserve">
    <value>07:00</value>
  </data>
  <data name="CalendarScrollToSix" xml:space="preserve">
    <value>06:00</value>
  </data>
  <data name="CalendarScrollToTen" xml:space="preserve">
    <value>10:00</value>
  </data>
  <data name="CalendarScrollToThree" xml:space="preserve">
    <value>03:00</value>
  </data>
  <data name="CalendarScrollToTime" xml:space="preserve">
    <value>Κύλιση στην ώρα</value>
  </data>
  <data name="CalendarScrollToTwo" xml:space="preserve">
    <value>02:00</value>
  </data>
  <data name="CalendarWeekView" xml:space="preserve">
    <value>Εβδομάδα</value>
  </data>
  <data name="CalendarWorkWeekView" xml:space="preserve">
    <value>Εργάσιμη εβδομάδα</value>
  </data>
  <data name="DateFormat" xml:space="preserve">
    <value>Μορφή ημερομηνίας</value>
  </data>
  <data name="TimeFormat" xml:space="preserve">
    <value>Μορφή ώρας</value>
  </data>
  <data name="CultureField" xml:space="preserve">
    <value>Τοπικές Ρυθμίσεις</value>
  </data>
  <data name="ShowPhonesInContactLists" xml:space="preserve">
    <value>Να εμφανίζεται τα τηλέφωνα στις λίστες με τις επαφές</value>
  </data>
  <data name="ShowSsnInContactLists" xml:space="preserve">
    <value>Να εμφανίζεται το ΑΜΚΑ στις λίστες με τις επαφές</value>
  </data>
  <data name="ShowTinInContactLists" xml:space="preserve">
    <value>Να εμφανίζεται το ΑΦΜ στις λίστες με τις επαφές</value>
  </data>
  <data name="FullNameDisplay" xml:space="preserve">
    <value>Εμφάνιση πλήρους ονόματος</value>
  </data>
  <data name="EventTextDisplay" xml:space="preserve">
    <value>Τι θα φαίνεται στο κείμενο του συμβάντος</value>
  </data>
  <data name="EventTextSeparator" xml:space="preserve">
    <value>Διαχωριστικό σύμβολο στο κείμενο του συμβάντος</value>
  </data>
</root>