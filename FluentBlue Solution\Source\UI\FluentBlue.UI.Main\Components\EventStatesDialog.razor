﻿@implements IDialogContentComponent<List<Data.Model.DBOs.Calendar.EventState>>

@using Blazored.FluentValidation
@using FluentBlue.Data.Model
@using FluentBlue.Data.Model.DBOs.Calendar
@using FluentBlue.Data.Model.DBOs.Settings
@using FluentBlue.Data.Model.DBOs.Validators
@using FluentBlue.UI.Main.Auth
@using FluentBlue.UI.Main.Shared
@using FluentBlue.WebApi.Client
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Logging
@using Microsoft.FluentUI.AspNetCore.Components
@using FluentValidation
@using System.ComponentModel
@using System.Linq.Expressions
@using FluentBlue.Shared.Utilities
@using Microsoft.JSInterop
@using Microsoft.Extensions.Caching.Hybrid

@inject HttpClient httpClient
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject ILogger<FluentBlue.UI.Main.Components.EventStatesDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.EventStatesWebApiClient> eventStatesWebApiClientLogger
@inject IFormFactor formFactor
@inject IJSRuntime JS
@inject HybridCache cache


<FluentDialogHeader Class="hidden"></FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto">
    <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4">@Resources.EventStatesDialogResource.Title</FluentLabel>
    <EditForm id="editForm" Model="@Content">
        <ChildContent Context="context2">
            <FluentValidationValidator @ref="fluentValidationValidator" />

            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                <FluentButton IconStart="@(new Icons.Regular.Size16.Add())" Appearance="Appearance.Accent" OnClick="AddBtnOnClick"><span>@Resources.EventStatesDialogResource.AddBtn_Text</span></FluentButton>
            </FluentToolbar>

            @if (Content != null && Content.Count > 0)
            {
                <FluentSortableList TItem="EventState" Items="@Content" Fallback="true" Handle="true" OnUpdate="HandleReorder">
                    <ItemTemplate>
                        <div class="sortable-grab cursor-move">
                            <FluentIcon Value="@(new Icons.Regular.Size20.ArrowSort())" />
                        </div>
                        <div class="sortable-item-content flex flex-1 gap-2 items-center p-1 overflow-hidden" style="height:31px">
                            <div class="flex-1">
                                <FluentTextField @bind-Value="@context.Name" Appearance="FluentInputAppearance.Filled" Maxlength="50" Class="w-full" AutoComplete="off" />
                                <ValidationMessage For="@(() => context.Name)" />
                            </div>
                            @*   <div class="flex-none w-20">
                            <FluentTextField @bind-Value="@context.BackColor" Appearance="FluentInputAppearance.Filled"
                                             TextFieldType="TextFieldType.Color"
                                             Class="w-full" />
                            <ValidationMessage For="@(() => context.BackColor)" />
                        </div> *@
                            <FluentButton IconStart="@(new Icons.Regular.Size16.Delete())"
                                          OnClick="() => DeleteBtnOnClick(context)"
                                          BackgroundColor="transparent"
                                          Class="flex-none w-10 " />
                        </div>
                    </ItemTemplate>
                </FluentSortableList>
            }
        </ChildContent>
    </EditForm>
</FluentDialogBody>

<FluentDialogFooter>
    <FluentStack Orientation="Orientation.Vertical" VerticalAlignment="VerticalAlignment.Bottom">
        <FluentDivider Class="w-full" Role="DividerRole.Presentation"></FluentDivider>
        <FluentStack Orientation="Orientation.Horizontal" HorizontalAlignment="HorizontalAlignment.Right" VerticalAlignment="VerticalAlignment.Bottom" Class="self-end">
            <FluentButton Loading="@this.isSaving" IconStart="@(new Icons.Regular.Size16.Save())" Appearance="Appearance.Accent" OnClick="SaveBtnOnClick"><span>@GlobalResource.Save</span></FluentButton>
            <FluentButton Disabled="@this.isSaving" IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtnOnClick"><span>@GlobalResource.Cancel</span></FluentButton>
        </FluentStack>
    </FluentStack>
</FluentDialogFooter>

