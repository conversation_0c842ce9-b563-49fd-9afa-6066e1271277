-- =============================================
-- FluentBlue Database Deployment Script
-- Password Reset and User Registration Features
-- =============================================
-- This script creates/updates the required database objects for:
-- 1. Password Reset functionality
-- 2. User Registration functionality
-- 3. Email service integration
-- =============================================

USE [FluentBlue]
GO

-- =============================================
-- 1. CREATE SCHEMAS (if they don't exist)
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Tenants')
BEGIN
    EXEC('CREATE SCHEMA [Tenants]')
    PRINT 'Created schema: Tenants'
END
ELSE
BEGIN
    PRINT 'Schema Tenants already exists'
END
GO

-- =============================================
-- 2. CREATE/UPDATE PasswordResetToken TABLE
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Tenants].[PasswordResetToken]') AND type in (N'U'))
BEGIN
    -- Create the table
    CREATE TABLE [Tenants].[PasswordResetToken] (
        [TokenId]        UNIQUEIDENTIFIER CONSTRAINT [DF_PasswordResetToken_TokenId] DEFAULT (newsequentialid()) NOT NULL,
        [UserId]         UNIQUEIDENTIFIER NOT NULL,
        [Token]          NVARCHAR (255)   CONSTRAINT [DF_PasswordResetToken_Token] DEFAULT (N'') NOT NULL,
        [DateCreatedUtc] DATETIME         CONSTRAINT [DF_PasswordResetToken_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
        [ExpiresUtc]     DATETIME         CONSTRAINT [DF_PasswordResetToken_ExpiresUtc] DEFAULT (dateadd(hour, 24, getutcdate())) NOT NULL,
        [IsUsed]         BIT              CONSTRAINT [DF_PasswordResetToken_IsUsed] DEFAULT ((0)) NOT NULL,
        CONSTRAINT [PK_PasswordResetToken] PRIMARY KEY CLUSTERED ([TokenId] ASC)
    );
    
    PRINT 'Created table: [Tenants].[PasswordResetToken]'
END
ELSE
BEGIN
    PRINT 'Table [Tenants].[PasswordResetToken] already exists'
END
GO

-- =============================================
-- 3. CREATE FOREIGN KEY CONSTRAINTS
-- =============================================

-- Add foreign key constraint to User table (if it doesn't exist)
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[Tenants].[FK_PasswordResetToken_User]') AND parent_object_id = OBJECT_ID(N'[Tenants].[PasswordResetToken]'))
BEGIN
    ALTER TABLE [Tenants].[PasswordResetToken]
    ADD CONSTRAINT [FK_PasswordResetToken_User] 
    FOREIGN KEY ([UserId]) REFERENCES [Tenants].[User] ([UserId]) 
    ON DELETE CASCADE ON UPDATE CASCADE
    
    PRINT 'Created foreign key: FK_PasswordResetToken_User'
END
ELSE
BEGIN
    PRINT 'Foreign key FK_PasswordResetToken_User already exists'
END
GO

-- =============================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Index on Token column for fast token lookups
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[Tenants].[PasswordResetToken]') AND name = N'TokenIndex')
BEGIN
    CREATE NONCLUSTERED INDEX [TokenIndex]
    ON [Tenants].[PasswordResetToken]([Token] ASC);
    
    PRINT 'Created index: TokenIndex on PasswordResetToken.Token'
END
ELSE
BEGIN
    PRINT 'Index TokenIndex already exists'
END
GO

-- Index on UserId column for user-specific queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[Tenants].[PasswordResetToken]') AND name = N'UserIdIndex')
BEGIN
    CREATE NONCLUSTERED INDEX [UserIdIndex]
    ON [Tenants].[PasswordResetToken]([UserId] ASC);
    
    PRINT 'Created index: UserIdIndex on PasswordResetToken.UserId'
END
ELSE
BEGIN
    PRINT 'Index UserIdIndex already exists'
END
GO

-- Index on ExpiresUtc column for cleanup operations
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[Tenants].[PasswordResetToken]') AND name = N'ExpiresUtcIndex')
BEGIN
    CREATE NONCLUSTERED INDEX [ExpiresUtcIndex]
    ON [Tenants].[PasswordResetToken]([ExpiresUtc] ASC);
    
    PRINT 'Created index: ExpiresUtcIndex on PasswordResetToken.ExpiresUtc'
END
ELSE
BEGIN
    PRINT 'Index ExpiresUtcIndex already exists'
END
GO

-- =============================================
-- 5. VERIFY USER TABLE INDEXES FOR REGISTRATION
-- =============================================

-- Ensure unique email index exists for registration validation
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[Tenants].[User]') AND name = N'UniqueEmailIndex')
BEGIN
    -- Check if there's already a similar index on Email
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[Tenants].[User]') AND name = N'UniqueUser')
    BEGIN
        CREATE UNIQUE NONCLUSTERED INDEX [UniqueEmailIndex]
        ON [Tenants].[User]([Email] ASC);
        
        PRINT 'Created index: UniqueEmailIndex on User.Email'
    END
    ELSE
    BEGIN
        PRINT 'Email uniqueness is already enforced by UniqueUser index'
    END
END
ELSE
BEGIN
    PRINT 'Index UniqueEmailIndex already exists'
END
GO

-- =============================================
-- 6. CREATE CLEANUP STORED PROCEDURE
-- =============================================

-- Stored procedure to clean up expired password reset tokens
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Tenants].[CleanupExpiredPasswordResetTokens]') AND type in (N'P', N'PC'))
BEGIN
    DROP PROCEDURE [Tenants].[CleanupExpiredPasswordResetTokens]
    PRINT 'Dropped existing procedure: CleanupExpiredPasswordResetTokens'
END
GO

CREATE PROCEDURE [Tenants].[CleanupExpiredPasswordResetTokens]
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @DeletedCount INT;
    
    -- Delete expired tokens (older than 24 hours or already used)
    DELETE FROM [Tenants].[PasswordResetToken]
    WHERE [ExpiresUtc] < GETUTCDATE() OR [IsUsed] = 1;
    
    SET @DeletedCount = @@ROWCOUNT;
    
    PRINT 'Cleaned up ' + CAST(@DeletedCount AS VARCHAR(10)) + ' expired password reset tokens';
    
    RETURN @DeletedCount;
END
GO

PRINT 'Created procedure: CleanupExpiredPasswordResetTokens'
GO

-- =============================================
-- 7. VERIFY REQUIRED TABLES EXIST
-- =============================================

-- Verify all required tables exist
DECLARE @MissingTables NVARCHAR(MAX) = '';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Tenants].[User]') AND type in (N'U'))
    SET @MissingTables = @MissingTables + '[Tenants].[User], ';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Tenants].[Role]') AND type in (N'U'))
    SET @MissingTables = @MissingTables + '[Tenants].[Role], ';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Tenants].[Tenant]') AND type in (N'U'))
    SET @MissingTables = @MissingTables + '[Tenants].[Tenant], ';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Settings].[UserSetting]') AND type in (N'U'))
    SET @MissingTables = @MissingTables + '[Settings].[UserSetting], ';

IF LEN(@MissingTables) > 0
BEGIN
    SET @MissingTables = LEFT(@MissingTables, LEN(@MissingTables) - 1); -- Remove trailing comma
    PRINT 'WARNING: The following required tables are missing: ' + @MissingTables;
    PRINT 'Please ensure the complete database schema is deployed before using password reset functionality.';
END
ELSE
BEGIN
    PRINT 'All required tables are present.';
END
GO

-- =============================================
-- 8. DEPLOYMENT SUMMARY
-- =============================================

PRINT '============================================='
PRINT 'FluentBlue Database Deployment Complete!'
PRINT '============================================='
PRINT 'Features deployed:'
PRINT '  ✓ Password Reset Token table'
PRINT '  ✓ Foreign key constraints'
PRINT '  ✓ Performance indexes'
PRINT '  ✓ Cleanup stored procedure'
PRINT ''
PRINT 'Next steps:'
PRINT '  1. Configure SMTP settings in appsettings.json'
PRINT '  2. Test password reset functionality'
PRINT '  3. Test user registration functionality'
PRINT '  4. Schedule cleanup procedure to run periodically'
PRINT '============================================='
GO
