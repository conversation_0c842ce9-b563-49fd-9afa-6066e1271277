using Blazored.LocalStorage;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared.Utilities;
using FluentBlue.UI.Devices;
using FluentBlue.UI.Main;
using FluentBlue.UI.Main.Auth;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Hosting;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components.Components.Tooltip;
using Microsoft.JSInterop;
using NodaTime.TimeZones;
using Syncfusion.Blazor;
using System.Diagnostics.Tracing;
using System.Globalization;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Serilog;
using ILogger = Serilog.ILogger;
using System.Reflection;
using Serilog.Core;
using FluentBlue.UI.Main.Services;
using FluentBlue.UI.Web.Services;
using Sentry.Protocol;


namespace FluentBlue.UI.Web
{
    public class Program
    {
        //private static ILogger logger = default!;

        public static async Task Main(string[] args)
        {
            try
            {
                // Get the assembly version
                string assemblyVersion = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";

                // Configure Serilog with Sentry sink
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Information()  // Set minimum level for logs to capture
                    .WriteTo.Console()  // Optional: Write logs to browser console (for debugging purposes)
                    .WriteTo.Sentry(o =>
                    {
                        o.Dsn = "https://<EMAIL>/4509101428572240";  // Replace with your Sentry DSN
                        o.MinimumEventLevel = Serilog.Events.LogEventLevel.Debug;  // Set minimum log level for Sentry
                        o.AttachStacktrace = true;  // Attach stacktrace for every event
                        o.Release = assemblyVersion;  // Set the release version

#if Debug
                        o.TracesSampleRate = 1.0f; // Adjust the sample rate as needed
                        o.Debug = true;
                        o.Environment = "Development";  // Set environment to Development
#elif Release
                        o.TracesSampleRate = 1.0f; // Adjust the sample rate as needed  
                        o.Debug = true;
                        o.Environment = "Production";  // Set environment to Production
#endif
                    })
                    .Enrich.WithProperty("Project", "FluentBlue.UI.Web")  // Add a static custom property
                                                                          //.Enrich.WithProperty("TenantId", "DefaultTenant")  // Add a static custom property
                                                                          //.Enrich.WithProperty("Environment", "Production")  // Add environment info (can be dynamic)
                    .CreateLogger();

                var builder = WebAssemblyHostBuilder.CreateDefault(args);

                // Add Serilog to the logging pipeline
                builder.Logging.AddSerilog();

                builder.RootComponents.Add<App>("#app");
                builder.RootComponents.Add<HeadOutlet>("head::after");

                string appsettingsEnvironmentFile = string.Empty;
                //#if DEBUG
                if (builder.HostEnvironment.IsDevelopment())
                {
                    appsettingsEnvironmentFile = "appsettings.Development.json";
                }
                //#elif RELEASE
                else if (builder.HostEnvironment.IsProduction())
                {
                    appsettingsEnvironmentFile = "appsettings.Production.json";
                }
                //#endif
                builder.Configuration.SetBasePath(System.AppContext.BaseDirectory).AddJsonFile("appsettings.json").AddJsonFile(appsettingsEnvironmentFile);


                builder.Services.AddLogging();
                builder.Services.AddLocalization();
                builder.Services.AddAuthorizationCore();
                builder.Services.AddHybridCache();
                builder.Services.AddBlazoredLocalStorage();
                //builder.Services.AddBlazoredSessionStorageAsSingleton();
                builder.Services.AddFluentUIComponents();
                builder.Services.AddSyncfusionBlazor();
                builder.Services.AddSingleton(typeof(ISyncfusionStringLocalizer), typeof(SyncfusionLocalizer));   // Register the locale service to localize the  SyncfusionBlazor components.
                Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzkyNDE5M0AzMzMwMmUzMDJlMzAzYjMzMzAzYk5qbkk2YXNoZ0pjdllpMU1nU0VVK0hUOG9McWFRKzZiVFU3QktkWi90M2s9;MzkyNDE5NEAzMzMwMmUzMDJlMzAzYjMzMzAzYkpRSjIrWERnSlZqbk12MEU4TSsrQ0dMWmJxK1FwZTBsYVBsenE5ZTZCTEE9");


                #region Depedency Injection
                builder.Services.AddScoped<ITooltipService, TooltipService>();
                builder.Services.AddSingleton<IFormFactor, FormFactor>();
                //For WebApi
                builder.Services.AddSingleton<HttpClient>(sp =>
                {
                    IConfiguration configuration = sp.GetRequiredService<IConfiguration>();
                    string? baseUrl = configuration.GetValue<string>("WebApi:BaseUrl");
                    return new HttpClient
                    {
                        BaseAddress = new Uri(baseUrl!),
                        DefaultRequestVersion = new Version(2, 0), // Use HTTP/2 for better performance
                        Timeout = TimeSpan.Parse(configuration.GetValue<string>("WebApi:Timeout")!),
                        DefaultRequestHeaders =
                        {
                            // Στο request header προσθέτουμε το ApplicationId για να ξέρουμε ποιο application στέλνει το request.
                            { "ApplicationId", FluentBlue.Shared.ApplicationInstanceInfo.ApplicationInstanceId.ToString() }
                        }
                    };
                });

                builder.Services.AddScoped<UserSettingValidator>();

                builder.Services.AddScoped<AuthenticationWebApiClient>();  //Για να χρησιμοποιηθεί από το JwtAuthenticationStateProvider
                builder.Services.AddScoped<UsersWebApiClient>();
                builder.Services.AddScoped<SettingsWebApiClient>();
                builder.Services.AddScoped<EventCategoriesWebApiClient>();
                builder.Services.AddScoped<EventsWebApiClient>();
                builder.Services.AddScoped<ContactCategoriesWebApiClient>();
                builder.Services.AddScoped<UserDevicesWebApiClient>();
                //builder.Services.AddScoped<NotificationService>(); // Register SignalR service
                // Register NotificationService as Singleton with configuration
                builder.Services.AddSingleton<SignalRNotificationService>(sp =>
                {
                    var configuration = sp.GetRequiredService<IConfiguration>();
                    var hubUrl = configuration["SignalR:HubUrl"]; // Read from configuration
                    if (string.IsNullOrEmpty(hubUrl))
                    {
                        // Optional: Log an error or throw if the URL is missing
                        // Log.Error("SignalR Hub URL is not configured in appsettings.");
                        throw new InvalidOperationException("SignalR Hub URL is not configured.");
                    }
                    return new SignalRNotificationService(hubUrl);
                });

                //Για το JWT
                builder.Services.AddScoped<JwtAuthenticationStateProvider>();
                builder.Services.AddScoped<AuthenticationStateProvider, JwtAuthenticationStateProvider>(
                    provider => provider.GetRequiredService<JwtAuthenticationStateProvider>()  //προσθέτουμε αυτή τη γραμμή ώστε σαν object για το JwtAuthenticationStateProvider να χρησιμοποιήσει αυτό που έφτιαξε στην παραπάνω γραμμή.
                );
                builder.Services.AddScoped<ILoginService, JwtAuthenticationStateProvider>(
                    provider => provider.GetRequiredService<JwtAuthenticationStateProvider>()  //προσθέτουμε αυτή τη γραμμή ώστε σαν object για το JwtAuthenticationStateProvider να χρησιμοποιήσει αυτό που έφτιαξε παραπάνω.
                );

                builder.Services.AddScoped<IPerformPhoneCall, PerformPhoneCall>();
                builder.Services.AddScoped<IMapsNavigation, MapsNavigation>();
                builder.Services.AddScoped<ISendEmail, SendEmail>();
                builder.Services.AddScoped<IDevicePreferences, DevicePreferences>();
                builder.Services.AddSingleton<IRestartAppService, RestartAppService>();
                builder.Services.AddSingleton(Log.Logger);
                builder.Services.AddSingleton<IWebUpdateCheckerService, WebUpdateCheckerService>();
                builder.Services.AddSingleton<IAppVersionHelper, AppVersionHelper>();

                //ErrorMessage
                //builder.Services.AddTransient<IDialogService, DialogService>();
                //builder.Services.AddTransient<ErrorNotifier>(sp =>
                //{
                //    return new ErrorNotifier(sp.GetRequiredService<IDialogService>());
                //});
                #endregion


                WebAssemblyHost app = builder.Build();


                #region  Set the TimeZone, Language
                CultureInfo? cultureInfo = null;
                string languageText = string.Empty;
                IJSRuntime jsRuntime = app.Services.GetRequiredService<IJSRuntime>();

                ILocalStorageService localStorage = app.Services.GetRequiredService<ILocalStorageService>();
                //Διαβάζει τις ρυθμίσεις από το LocalStorage.
                languageText = await localStorage.GetItemAsync<string>("Language") ?? string.Empty;

                //Αν βρέθηκαν ρυθμίσεις σχετικά με τη γλώσσα στο LocalStorage, δηλαδή ο ίδιος χρήστης έχει πιθανόν κάνει ξανά login στην εφαρμογή.
                if (languageText != "")
                {
                    //Language language = (Language)Enum.Parse<Language>(languageText);
                    Language language = languageText.Contains("el") ? Language.GreekGeneral : Language.EnglishGeneral;
                    cultureInfo = language.GetCultureInfo();
                }
                else  //Αν δεν βρέθηκαν UserSetting στο LocalStorage, δηλαδή ο χρήστης δεν έχει κάνει login ποτέ στην εφαρμογή.
                {
                    //Gets user language and timezone from the browser
                    string systemLanguage = await jsRuntime.InvokeAsync<string>("eval", "window.defaultCultureInfo.language");
                    
                    Language language = systemLanguage.Contains("el") ? Language.GreekGeneral : Language.EnglishGeneral;
                    cultureInfo = language.GetCultureInfo();

                    await localStorage.SetItemAsync<string>("Language", systemLanguage.ToString());
                }

                //Καταχωρεί στο LocalStorage το TimeZone του συστήματος.
                string systemTimezone = await jsRuntime.InvokeAsync<string>("eval", "window.defaultCultureInfo.timezone");
                await localStorage.SetItemAsync<string>("TimeZone", systemTimezone);

                // Set both default and current cultures
                CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
                CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;
                CultureInfo.CurrentCulture = cultureInfo;
                CultureInfo.CurrentUICulture = cultureInfo;

                #endregion

                await app.RunAsync();
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, ex.Message);
                throw;
            }
        }
    }
}
