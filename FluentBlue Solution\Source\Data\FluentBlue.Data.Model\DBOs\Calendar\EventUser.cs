﻿using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.VisualBasic;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.CompilerServices;

namespace FluentBlue.Data.Model.DBOs.Calendar
{

    [Table("EventUser", Schema = "Calendar")]
    public class EventUser : IObjectState
    {
        private Guid eventUserId;
        private Guid? userId;
        private Guid? eventId;

        public EventUser()
        {
            eventUserId = Guid.CreateVersion7();
            ObjectState = ObjectState.Unchanged;
        }

        [Key]
        public Guid EventUserId
        {
            get
            {
                return eventUserId;
            }
            set
            {
                eventUserId = value;
            }
        }   //TODO: Στη βάση δεδομένων πρέπει το primary key να γίνει από non-clustered index σε clustered για λόγους ταχύτητας (τα GUID ειναι αργά με Clustered index).

        [Required]
        [ForeignKey("User")]
        public Guid? UserId
        {
            get
            {
                return userId;
            }
            set
            {
                userId = value;
            }
        }

        [Required]
        public Guid? EventId
        {
            get
            {
                return eventId;
            }
            set
            {
                eventId = value;
            }
        }

        public User? User { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }


    }
}
