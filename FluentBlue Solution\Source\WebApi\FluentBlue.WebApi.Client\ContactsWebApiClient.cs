﻿using FluentBlue.WebApi.Shared;
using FluentBlue.Shared;
using System.Text;
using Newtonsoft.Json;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using static System.Runtime.InteropServices.JavaScript.JSType;
using FluentBlue.Data.Model.DBOs.Contacts;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
using FluentBlue.Data.Model.DBOs.Tenants;
using System.Net.Http.Headers;
using FluentBlue.Shared.Authorization;


namespace FluentBlue.WebApi.Client
{
    public class ContactsWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<ContactsWebApiClient> logger;
        private CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(10));

        public ContactsWebApiClient(HttpClient httpClient, ILogger<ContactsWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>> GetContacts(Guid tenantId, string filter, Guid[] contactCategoryIds, UInt16 pageIndex, UInt16 pageSize)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "Contacts/Get";

                RequestPagedContactsParameters requestDataParams = new RequestPagedContactsParameters();
                requestDataParams.TenantId = tenantId;
                requestDataParams.Filter = filter.Trim();
                requestDataParams.ContactCategoryIds = contactCategoryIds;
                requestDataParams.PageIndex = pageIndex;
                requestDataParams.PageSize = pageSize;
                requestDataParams.SortColumns = new Dictionary<string, SortOrder>();
                requestDataParams.SortColumns.Add("FirstName", SortOrder.Ascending);
                requestDataParams.SortColumns.Add("LastName", SortOrder.Ascending);
                string getEntitiesParamsJson = JsonConvert.SerializeObject(requestDataParams);  //Μετατρέπουμε τις παραμέτρους για το διάβασμα entities σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContent = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");

                string urlParameters = UrlHelpers.ToQueryString(requestDataParams, "&");
                requestUri = requestUri + "?" + urlParameters;

                //Εκτελούμε το request.
                //this.httpClient.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiIyY2FhYmI1NC1mZDM4LTRhY2MtOTMyYS1kNWM3Yjk0ZmRkMmYiLCJVc2VybmFtZSI6IjMzIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZSI6IlAgTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IlN1cGVyQWRtaW4iLCJUZW5hbnRJZCI6IjNmYTg1ZjY0LTU3MTctNDU2Mi1iM2ZjLTJjOTYzZjY2YWZhNiIsImV4cCI6MTc0NzA0Mjk2Mn0.ZqUnCMKdLJ37E2PBZJJVTaFXpVBa5skxbHhBY18wTjs");
                //HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent, this.cancellationTokenSource.Token);  //Διαβάζουμε το HttpResponse

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<PagedData<List<Data.Model.DTOs.ContactView>>> response = JsonConvert.DeserializeObject<ApiResponse<PagedData<List<Data.Model.DTOs.ContactView>>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return new PagedData<List<Data.Model.DTOs.ContactView>> { Data = null, DataTotalCount = 0 };
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetContacts({@tenantId},{fiter},{@pageIndex}, {@pageSize})", tenantId, filter, pageIndex, pageSize);
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DTOs.ContactLI>> GetContactsList(Guid tenantId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "Contacts/GetList";

                //Εκτελούμε το request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DTOs.ContactLI>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DTOs.ContactLI>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return new List<Data.Model.DTOs.ContactLI>();
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetContactsLI({@tenantId})", tenantId);
                throw;
            }
        }

        public async Task<Contact?> GetContact(Guid contactId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "Contact/Get?ContactId=" + contactId.ToString();  // + "&tenantId=" + tenantId;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<Contact?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Contacts.Contact?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetContact({@contactId})", contactId);
                throw;
            }
        }

        public async Task<Contact?> CreateOrUpdateContact(Contact contact)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Contact/CreateOrUpdate";

                //string ContactJson = Newtonsoft.Json.JsonConvert.SerializeObject(Contact, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το Contact object σε json.
                string ContactJson = System.Text.Json.JsonSerializer.Serialize(contact);  //Μετατρέπουμε το Contact object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(ContactJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<Contact>? response = JsonConvert.DeserializeObject<ApiResponse<Contact>>(responseString);  //Μετατρέπουμε το δικό μας response σε object


                ///////////////
                //int[] arr = new int[100000];
                //Parallel.ForEach(arr, new ParallelOptions { MaxDegreeOfParallelism = 10},
                //async i =>
                //{
                //    // logic
                //    Contact contact2 = new Contact();
                //    contact2.TenantId = Guid.Parse("3FA85F64-5717-4562-B3FC-2C963F66AFA6");
                //    contact2.FirstName = i.ToString();
                //    contact2.LastName = Guid.CreateVersion7().ToString();
                //    contact2.DateCreated= DateTime.Now;
                //    contact2.DateModified= DateTime.Now;
                //    contact2.ObjectState = ObjectState.Added;

                //    string ContactJson = System.Text.Json.JsonSerializer.Serialize(contact2);  //Μετατρέπουμε το Contact object σε json.

                //    //Ετοιμάζουμε το HttpContext με τα json data.
                //    HttpContent httpContext = new StringContent(ContactJson, Encoding.UTF8, "application/json");

                //    //Εκτελούμε το POST request.
                //    HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                //    string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                //    ApiResponse<Contact>? response = JsonConvert.DeserializeObject<ApiResponse<Contact>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                //});
                /////////////////

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }
                else if (response.ResultCode == ApiResponseResultCode.DbConcurrencyException)
                {
                    ApplicationException appException = new ApplicationException(response.ExceptionMessage ?? "");
                    appException.Data.Add("DbConcurrencyException", null);
                    throw appException;
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateContact({@tenant})", contact);
                throw;
            }
        }

        public async Task DeleteContact(Guid contactId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/Contact/Delete?ContactId=" + contactId.ToString();

                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString)!;
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception)
            {
                throw;
            }
        }

        /*
       public async Task<Tenant> CreateTenant(TenantDto tenant)
       {
           string requestUri = httpClient.BaseAddress + apiVersion + "/" + "tenants/create";

           //string tenantJson = Newtonsoft.Json.JsonConvert.SerializeObject(tenant, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το Tenant object σε json.
           string tenantJson = System.Text.Json.JsonSerializer.Serialize(tenant);  //Μετατρέπουμε το Tenant object σε json.

           //Ετοιμάζουμε το HttpContext με τα json data.
           HttpContent httpContext = new StringContent(tenantJson, Encoding.UTF8, "application/json");

           //Εκτελούμε το POST request.
           HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
           string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
           ApiResponse<Tenant> response = JsonConvert.DeserializeObject<ApiResponse<Tenant>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

           if (response.ResultCode == ApiResponseResultCode.Ok)
           {
               return response.Data;
           }
           else if (response.ResultCode == ApiResponseResultCode.Exception)
           {
               throw new ApplicationException(response.Exception.Message);
               //ExceptionHandler.RecordException(response.Exception);
           }

           return null;
       }

       public async Task<TenantDto> UpdateTenant(TenantDto tenant)
       {
           string requestUri = httpClient.BaseAddress + apiVersion + "/" + "tenants/update";

           //string tenantJson = Newtonsoft.Json.JsonConvert.SerializeObject(tenant, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το Tenant object σε json.
           string tenantJson = System.Text.Json.JsonSerializer.Serialize(tenant);  //Μετατρέπουμε το Tenant object σε json.

           //Ετοιμάζουμε το HttpContext με τα json data.
           HttpContent httpContext = new StringContent(tenantJson, Encoding.UTF8, "application/json");

           //Εκτελούμε το POST request.
           HttpResponseMessage httpResponse = await this.httpClient.PutAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
           string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
           ApiResponse<TenantDto> response = JsonConvert.DeserializeObject<ApiResponse<TenantDto>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

           if (response.ResultCode == ApiResponseResultCode.Ok)
           {
               return response.Data;
           }
           else if (response.ResultCode == ApiResponseResultCode.Exception)
           {
               throw new ApplicationException(response.Exception.Message);
               //ExceptionHandler.RecordException(response.Exception);
           }

           return null;
       }

       public async Task DeleteTenant(Guid tenantId)
       {
           try
           {
               string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "tenants" + "/" + tenantId.ToString() + "/" + "?pwd=333";

               HttpResponseMessage httpResponse = await this.httpClient.DeleteAsync(requestUri);
               string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
               ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString);
               if (response.ResultCode == ApiResponseResultCode.Ok)
               {
                   return;
               }
               else if (response.ResultCode == ApiResponseResultCode.Exception)
               {
                   throw response.Exception;
                   //ExceptionHandler.RecordException(response.Exception);
               }

               return;
           }
           catch (Exception exp)
           {

               throw;
           }

       }

       public async Task<Int32> GetTransformedFilesCount(string tenantId)
       {
           string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "tenants" + "/" + tenantId.ToString() + "/FilesCount";  // + "&tenantId=" + tenantId;
           string responseString = await this.httpClient.GetStringAsync(requestUri);

           ApiResponseS<Int32>? response = JsonConvert.DeserializeObject<ApiResponseS<Int32>>(responseString);
           if (response != null)
           {
               if (response.Data != null && response.ResultCode == ApiResponseResultCode.Ok)
               {
                   return response.Data.Value;
               }
               else if (response.ResultCode == ApiResponseResultCode.Exception)
               {
                   throw response.Exception!;
                   //ExceptionHandler.RecordException(response.Exception);
               }
           }
           else
           {
               throw new Exception("Null response");
           }

           return 0;
       }

       public async Task<UserToken> Login(string username, string password)
       {
           string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "Tenants/Login";

           requestUri += "?Username=" + username + "&Password=" + password;


           string responseString = await this.httpClient.GetStringAsync(requestUri);
           ApiResponse<UserToken> response = JsonConvert.DeserializeObject<ApiResponse<UserToken>>(responseString);
           if (response.ResultCode == ApiResponseResultCode.Ok)
           {
               return response.Data;
           }
           else if (response.ResultCode == ApiResponseResultCode.LoginFailedInvalidUser)
           {
               //return null;
               throw new ApplicationException("LoginFailedInvalidUser");
           }
           else if (response.ResultCode == ApiResponseResultCode.LoginFailedSubscriptionExpired)
           {
               throw new ApplicationException("LoginFailedSubscriptionExpired");
           }
           else if (response.ResultCode == ApiResponseResultCode.Exception)
           {
               throw response.Exception;
               //ExceptionHandler.RecordException(response.Exception);
           }

           return null;
       }

       public async Task<UserToken> RenewToken()
       {
           string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "RenewToken";

           string responseString = await this.httpClient.GetStringAsync(requestUri);
           ApiResponse<UserToken> response = JsonConvert.DeserializeObject<ApiResponse<UserToken>>(responseString);
           if (response.ResultCode == ApiResponseResultCode.Ok)
           {
               return response.Data;
           }
           else if (response.ResultCode == ApiResponseResultCode.Exception)
           {
               throw response.Exception;
               //ExceptionHandler.RecordException(response.Exception);
           }

           return null;

       }
       */
    }
}
