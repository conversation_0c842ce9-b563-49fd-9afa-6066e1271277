﻿using FluentBlue.Application.Business;
using FluentBlue.Shared.Authorization;
using FluentBlue.WebApi.Shared.Response;
using FluentBlue.WebApi.Shared.Request;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class AuthenticationController : ControllerBase
    {
        private ILogger<AuthenticationController> logger;
        private IUsersBusiness usersBusiness;
        private IConfiguration configuration;

        public AuthenticationController(IUsersBusiness usersBusiness, ILogger<AuthenticationController> logger, IConfiguration configuration)
        {
            this.usersBusiness = usersBusiness;
            this.logger = logger;  //TODO: να δω αν θα χρησιμοποιείσω το ILogger γενικά στην εφαρμογή.
            this.configuration = configuration;
        }

        [HttpGet("Login")]
        public async Task<ApiResponse<LoginResponse>> Login(string username, string password)
        {
            try
            {
                //Query
                
                Data.Model.DBOs.Tenants.User? user = await this.usersBusiness.GetUser(username, password);

                //Αν ο User υπάρχει
                if (user != null)
                {
                    UserToken token = BuildToken(user.UserId.ToString(), username, user.FullName, user.Role.Name.ToString(), user.TenantId.ToString());

                    //TODO: Λείπει κώδικας αν η συνδρομή του Tenant έχει λήξει

                    //Response
                    LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.Ok, Token = token };
                    return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
                }
                else  //Αν ο User δεν υπάρχει
                {
                    //Response
                    LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.InvalidUsernamePassword, Token = new UserToken() };
                    return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
                }
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                //ExceptionHandler.RecordException(ex);
                return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet("RenewToken")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public ActionResult<UserToken> Renew()
        {
            try
            {
                UserToken userToken = BuildToken("9AD0538F-EAEA-4C4B-8FE7-B69CEDDCABD3", HttpContext.User.Identity.Name, "John", "Admin", "FD335871-9253-4364-9BEB-5D1238F1C4E5");

                return userToken;
            }
            catch (Exception ex)
            {
                //ExceptionHandler.RecordException(ex);
                throw;
            }
        }

        private UserToken BuildToken(string userId, string username, string fullName, string roleName, string tenantId)
        {
            var claims = new List<Claim>()
            {
                new Claim("UserId", userId),
                new Claim("Username", username),
                new Claim(ClaimTypes.Name, fullName),
                new Claim(ClaimTypes.Role, roleName),
                new Claim("TenantId", tenantId)
            };

            //var identityUser = await _userManager.FindByEmailAsync(userinfo.Email);
            //var claimsDB = await _userManager.GetClaimsAsync(identityUser);
            //claims.AddRange(claimsDB);

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["jwt:key"]!));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var expiration = DateTime.UtcNow.AddYears(1);

            JwtSecurityToken token = new JwtSecurityToken(
               issuer: null,
               audience: null,
               claims: claims,
               expires: expiration,
               signingCredentials: creds);

            return new UserToken()
            {
                Token = new JwtSecurityTokenHandler().WriteToken(token),
                Expiration = expiration
            };
        }

        [HttpPost("ForgotPassword")]
        public async Task<ApiResponse<ForgotPasswordResponse>> ForgotPassword([FromBody] ForgotPasswordRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.Email))
                {
                    return new ApiResponse<ForgotPasswordResponse>()
                    {
                        ResultCode = ApiResponseResultCode.Exception,
                        ExceptionMessageForUser = "Email address is required."
                    };
                }

                // Create password reset token and send email
                await usersBusiness.CreatePasswordResetTokenAsync(request.Email);

                var response = new ForgotPasswordResponse
                {
                    Success = true,
                    Message = "If an account with that email exists, we've sent you a password reset link."
                };

                return new ApiResponse<ForgotPasswordResponse>()
                {
                    ResultCode = ApiResponseResultCode.Ok,
                    ResponseContent = response
                };
            }
            catch (ApplicationException)
            {
                // Don't reveal if user exists or not for security reasons
                var response = new ForgotPasswordResponse
                {
                    Success = true,
                    Message = "If an account with that email exists, we've sent you a password reset link."
                };

                return new ApiResponse<ForgotPasswordResponse>()
                {
                    ResultCode = ApiResponseResultCode.Ok,
                    ResponseContent = response
                };
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing forgot password request");
                return new ApiResponse<ForgotPasswordResponse>()
                {
                    ResultCode = ApiResponseResultCode.Exception,
                    ExceptionMessage = ex.Message
                };
            }
        }

        [HttpPost("ResetPassword")]
        public async Task<ApiResponse<ResetPasswordResponse>> ResetPassword([FromBody] ResetPasswordRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.Token) || string.IsNullOrEmpty(request.NewPassword))
                {
                    return new ApiResponse<ResetPasswordResponse>()
                    {
                        ResultCode = ApiResponseResultCode.Exception,
                        ExceptionMessageForUser = "Token and new password are required."
                    };
                }

                bool success = await usersBusiness.ResetPasswordAsync(request.Token, request.NewPassword);

                if (success)
                {
                    var response = new ResetPasswordResponse
                    {
                        Success = true,
                        Message = "Password reset successfully."
                    };

                    return new ApiResponse<ResetPasswordResponse>()
                    {
                        ResultCode = ApiResponseResultCode.Ok,
                        ResponseContent = response
                    };
                }
                else
                {
                    return new ApiResponse<ResetPasswordResponse>()
                    {
                        ResultCode = ApiResponseResultCode.Exception,
                        ExceptionMessageForUser = "Invalid or expired reset token."
                    };
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing password reset request");
                return new ApiResponse<ResetPasswordResponse>()
                {
                    ResultCode = ApiResponseResultCode.Exception,
                    ExceptionMessage = ex.Message
                };
            }
        }

        [HttpPost("Register")]
        public async Task<ApiResponse<Data.Model.DBOs.Tenants.User>> Register([FromBody] RegisterUserRequest request)
        {
            try
            {
                if (request == null)
                {
                    return new ApiResponse<Data.Model.DBOs.Tenants.User>()
                    {
                        ResultCode = ApiResponseResultCode.Exception,
                        ExceptionMessageForUser = "Registration data is required."
                    };
                }

                if (!ModelState.IsValid)
                {
                    var errors = string.Join(", ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage));

                    return new ApiResponse<Data.Model.DBOs.Tenants.User>()
                    {
                        ResultCode = ApiResponseResultCode.Exception,
                        ExceptionMessageForUser = errors
                    };
                }

                var newUser = await usersBusiness.RegisterUserAsync(request);

                return new ApiResponse<Data.Model.DBOs.Tenants.User>()
                {
                    ResultCode = ApiResponseResultCode.Ok,
                    ResponseContent = newUser
                };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Data.Model.DBOs.Tenants.User>()
                {
                    ResultCode = ApiResponseResultCode.Exception,
                    ExceptionMessageForUser = ex.Message
                };
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing user registration request");
                return new ApiResponse<Data.Model.DBOs.Tenants.User>()
                {
                    ResultCode = ApiResponseResultCode.Exception,
                    ExceptionMessage = ex.Message
                };
            }
        }

        [HttpGet("ValidateResetToken")]
        public async Task<ApiResponse<TokenValidationResponse>> ValidateResetToken([FromQuery] string token)
        {
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    var invalidResponse = new TokenValidationResponse
                    {
                        IsValid = false,
                        Message = "Token is required."
                    };

                    return new ApiResponse<TokenValidationResponse>()
                    {
                        ResultCode = ApiResponseResultCode.Ok,
                        ResponseContent = invalidResponse
                    };
                }

                bool isValid = await usersBusiness.ValidatePasswordResetTokenAsync(token);

                var response = new TokenValidationResponse
                {
                    IsValid = isValid,
                    Message = isValid ? "Token is valid." : "Token is invalid or expired."
                };

                return new ApiResponse<TokenValidationResponse>()
                {
                    ResultCode = ApiResponseResultCode.Ok,
                    ResponseContent = response
                };
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error validating reset token");

                var errorResponse = new TokenValidationResponse
                {
                    IsValid = false,
                    Message = "Error validating token."
                };

                return new ApiResponse<TokenValidationResponse>()
                {
                    ResultCode = ApiResponseResultCode.Exception,
                    ResponseContent = errorResponse,
                    ExceptionMessage = ex.Message
                };
            }
        }

    }
}
