﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{
    public partial class Routes
    {
        private bool IsPublicRoute(Type pageType)
        {
            // Define which pages should be accessible without authentication
            var publicPages = new[]
            {
            typeof(FluentBlue.UI.Main.Pages.Login),
            typeof(FluentBlue.UI.Main.Pages.ForgotPassword),
            typeof(FluentBlue.UI.Main.Pages.Register),
            typeof(FluentBlue.UI.Main.Pages.ResetPassword)
        };

            return publicPages.Contains(pageType);
        }
    }
}
