﻿using AutoMapper;
using Azure.Core;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics.Internal;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace FluentBlue.Application.Business
{
    public class EventsBusiness : IEventsBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public EventsBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<EventsBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.Event>> GetEvents(Application.Business.Request.RequestEventsParameters parameters)
        {
            try
            {
                DateTime startDate = new DateTime(parameters.StartDateTicksUtc);
                DateTime endDate = new DateTime(parameters.EndDateTicksUtc);

                //Validation
                int resultsCount;
                parameters.Filter = (parameters.Filter ?? "").Trim();

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Calendar.Event> query = this.dbContext.Events.Include(x => x.Contact).Include(x => x.EventUsers).Include(x => x.EventReminders).AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == parameters.TenantId);
                if (parameters.Filter != "")
                {
                    query = query.Where(x => x.Subject.Contains(parameters.Filter) || x.Description.Contains(parameters.Filter));
                }
                if (parameters.UserIds != null && parameters.UserIds.Count() > 0)
                {
                    query = query.Where(x => x.EventUsers.Any(eu => parameters.UserIds.Contains(eu.UserId!.Value)));
                }
                query = query.Where(x => (x.StartTimeUtc.Date >= startDate && x.StartTimeUtc.Date <= endDate) || (x.EndTimeUtc.Date >= startDate && x.EndTimeUtc.Date <= endDate) || (x.StartTimeUtc.Date < startDate && x.EndTimeUtc.Date > endDate));
                //Διαβάζει το σύνολο των records.
                resultsCount = query.Count();

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Calendar.Event> events = await query.AsNoTracking().ToListAsync();
                //List<FluentBlue.Data.Model.DTOs.EventView> contactsDTOs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Events.Event>, List<FluentBlue.Data.Model.DTOs.EventView>>(events);

                //Response
                return events;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Parameters=" + parameters.ToString() });
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.Event>> GetAllEventsOfContact(Guid contactId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Calendar.Event> query = this.dbContext.Events.Include(x => x.EventUsers).AsNoTracking().AsQueryable();
                query = query.Where(c => c.ContactId == contactId);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Calendar.Event> events = await query.AsNoTracking().ToListAsync();

                //Response
                return events;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Calendar.Event?> GetEvent(Guid eventId)
        {
            try
            {
                //Query
                IQueryable<Event> query = dbContext.Events.Include(x => x.EventUsers).Include(x => x.EventReminders).AsQueryable();
                query = query.Where(c => c.EventId.ToString() == eventId.ToString());

                return await query.AsNoTracking().FirstOrDefaultAsync<Event>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "EventId=" + eventId.ToString() });
                throw;
            }
        }

        public async Task CreateOrUpdateNoRecurrentEvent(Event evnt)
        {
            try
            {
                //Validation
                if (evnt == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                InsertUpdateEventValidator validator = new InsertUpdateEventValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(evnt);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage;  // + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Query
                this.dbContext.Attach(evnt);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (DbUpdateConcurrencyException ex)
            {
                throw;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { evnt.EventId });
                throw;
            }
        }

        public async Task CreateOrUpdateEvent(Event evnt, RecurrentEventHandlingType? recurrentEventUpdateType, List<DateTime>? recurrenceDates, string? userTimeZone)
        {
            try
            {
                //Validation
                if (evnt == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                InsertUpdateEventValidator validator = new InsertUpdateEventValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(evnt);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage; // + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                #region Query
                #region New Event
                if (evnt.ObjectState == Data.Model.ObjectState.Added)
                {
                    //If there is no recurrence.
                    if (evnt.CustomRecurrence == false)
                    {
                        await this.CreateOrUpdateNoRecurrentEvent(evnt);
                    }
                    else  //If there is recurrence.
                    {
                        List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                        //Set the TimeZoneId of the new recurrent events.
                        foreach (Event recurrenceEvent in recurrenceEvents)
                        {
                            recurrenceEvent.UserTimeZoneId = userTimeZone!;
                        }

                        await this.SaveEventsBatch(recurrenceEvents);
                    }
                }
                #endregion New Event

                #region Update Event
                else if (evnt.ObjectState == Data.Model.ObjectState.Modified)
                {
                    Event? initialEvent = await this.GetEvent(evnt.EventId);

                    #region Validations for update.
                    //If the event we are going to update it does not exists.
                    if (initialEvent == null)
                    {
                        throw new ApplicationException(Resources.GlobalResource.EventNotFoundMessage);
                    }

                    //If initial event is recurrent but the updated event is not recurrent.
                    if (initialEvent != null && initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == false)
                    {
                        throw new ApplicationException(Resources.GlobalResource.InvalidDataMessage);
                    }

                    //If initial event is recurrent and the recurrentEventUpdateType is not provided.
                    if (initialEvent != null && initialEvent.CustomRecurrence == true && recurrentEventUpdateType == null)
                    {
                        throw new ApplicationException(Resources.GlobalResource.InvalidDataMessage);
                    }
                    #endregion Validations for update.


                    //If the initial Event and the updated Event does not have recurrence.
                    if (initialEvent!.CustomRecurrence == false && evnt.CustomRecurrence == false)
                    {
                        //await this.CreateOrUpdateNoRecurrentEvent(evnt);  
                        //This is the most simple case.
                        this.dbContext.Attach(evnt);
                        await this.dbContext.SaveChangesAsync();
                    }
                    //If the initial Event does not have recurrence but the updated Event has a recurrence.
                    else if (initialEvent.CustomRecurrence == false && evnt.CustomRecurrence == true)
                    {
                        List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                        //We delete initial Event since we are going to create the recurrent events.
                        evnt.ObjectState = ObjectState.Deleted;
                        recurrenceEvents.Add(evnt);  //Adds current Event in the batch list of events, so as to be deleted.
                        await this.SaveEventsBatch(recurrenceEvents);  //Creates the new recurrent events and deletes the original one.
                    }
                    //if the initial Event has recurrency and the updated Event also has recurrency.
                    else if (initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == true)
                    {
                        //If user wants to update only the current recurrence.
                        if (recurrentEventUpdateType == RecurrentEventHandlingType.Current)
                        {
                            //If StartDate or EndDate are changed (then we clear CustomRecurrence fields since the event is not following the series).
                            if (initialEvent.StartTimeUtc != evnt.StartTimeUtc || initialEvent.EndTimeUtc != evnt.EndTimeUtc)
                            {
                                //We remove the recurrence from current event, because it has different StartDate or EndDate.
                                evnt.CustomRecurrence = false;
                                evnt.CustomRecurrenceId = null;
                                evnt.CustomRecurrenceRule = "";
                            }

                            this.dbContext.Attach(evnt);
                            await this.dbContext.SaveChangesAsync();
                        }
                        else if (recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                        {
                            List<Event> eventsToSave = new List<Event>();  //Batch events to be saved.
                            //Finds the following events of recurrence.
                            List<Event> followingEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();
                            //Finds the previous events of current Event.
                            List<Event> previousEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc < evnt.StartTimeUtc).AsNoTracking().ToList();

                            //If the current Event is included in the previous Events (because e.g. the new StartDate was changed), then we remove it from previous Events.
                            if (previousEvents.Where(x => x.EventId == evnt.EventId).Any())
                            {
                                previousEvents.Remove(previousEvents.Where(x => x.EventId == evnt.EventId).FirstOrDefault());
                            }

                            //If the current Event is included in the following Events (because e.g. the new StartDate was changed), then we remove it from following Events.
                            if (followingEvents.Where(x => x.EventId == evnt.EventId).Any())
                            {
                                followingEvents.Remove(followingEvents.Where(x => x.EventId == evnt.EventId).FirstOrDefault());
                            }

                            //At this point of code we have the current event (evnt), the previousEvents and the followingEvents.

                            //We change the RecurrenceRule in previousEvents, so as to include the correct number of occurrences.
                            foreach (Event previousEvent in previousEvents)
                            {
                                //Remove from CustomRecurrenceRule of each previous event the text "COUNT=x" if exists.
                                if (previousEvent.CustomRecurrenceRule.Contains("COUNT"))
                                {
                                    int countIndex = previousEvent.CustomRecurrenceRule.IndexOf("COUNT");
                                    int semicolonIndex = previousEvent.CustomRecurrenceRule.IndexOf(";", countIndex);
                                    if (semicolonIndex == -1)  //If "COUNT=x" is the last part of the rule.
                                    {
                                        previousEvent.CustomRecurrenceRule = previousEvent.CustomRecurrenceRule.Substring(0, countIndex - 1); //-1 to remove also the preceding ";"
                                    }
                                    else
                                    {
                                        previousEvent.CustomRecurrenceRule = previousEvent.CustomRecurrenceRule.Substring(0, countIndex) + previousEvent.CustomRecurrenceRule.Substring(semicolonIndex + 1);
                                    }
                                }
                                else if (previousEvent.CustomRecurrenceRule.Contains("UNTIL"))
                                {
                                    int untilIndex = previousEvent.CustomRecurrenceRule.IndexOf("UNTIL");
                                    int semicolonIndex = previousEvent.CustomRecurrenceRule.IndexOf(";", untilIndex);
                                    if (semicolonIndex == -1)  //If "UNTIL=yyyyMMddT000000Z" is the last part of the rule.
                                    {
                                        previousEvent.CustomRecurrenceRule = previousEvent.CustomRecurrenceRule.Substring(0, untilIndex - 1); //-1 to remove also the preceding ";"
                                    }
                                    else
                                    {
                                        previousEvent.CustomRecurrenceRule = previousEvent.CustomRecurrenceRule.Substring(0, untilIndex) + previousEvent.CustomRecurrenceRule.Substring(semicolonIndex + 1);
                                    }
                                }

                                //Adds the new "COUNT=x" in CustomRecurrenceRule of each previous event (based on the new ammount of previousEvents).
                                previousEvent.CustomRecurrenceRule += (previousEvent.CustomRecurrenceRule.EndsWith(";") ? "COUNT=" + previousEvents.Count.ToString() : ";COUNT=" + previousEvents.Count.ToString()) + ";";
                            }

                            //Updates the previous events of recurrence.
                            foreach (Event tempEvent in previousEvents)
                            {
                                tempEvent.ObjectState = ObjectState.Modified;
                            }
                            eventsToSave.AddRange(previousEvents);  //Adds the previous events to the batch list of events to be saved.
                            

                            #region Generates the new recurrent events.
                            List<Event> newRecurrenceEvents = new List<Event>();
                            //If user has modified the recurrency rule.
                            if (evnt.CustomRecurrenceRule != initialEvent.CustomRecurrenceRule)
                            {
                                //EXPLANATION: We create the following events (including current) based on the new RecurrenceRule, because it is changed.

                                //Deletes the "old" following events of recurrence, since they will be created from scratch.
                                foreach (Event tempEvent in followingEvents)
                                {
                                    tempEvent.ObjectState = ObjectState.Deleted;
                                }
                                eventsToSave.AddRange(followingEvents);  //Adds the previous events to the batch list of events to be saved.

                                //Generates the recurrent events, based on new RecurrenceRule.
                                newRecurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, evnt.CustomRecurrenceId!.Value);

                                //Generates a new CustomRecurrenceId for the new following events.
                                Guid newRecurrenceId = Guid.CreateVersion7();
                                foreach(Event tempEvent in newRecurrenceEvents)
                                {
                                    tempEvent.CustomRecurrenceId = newRecurrenceId;
                                }

                                //Merges the new following events with the rest, so as to be saved all together.
                                eventsToSave.AddRange(newRecurrenceEvents);

                                //Deletes the current event since it has been recreated right above.
                                evnt.ObjectState = ObjectState.Deleted;
                                eventsToSave.Add(evnt);
                            }
                            else  //If user leave the RecurrenceRule to be the same.
                            {
                                //EXPLANATION: For the current and following events we have to keep the same RecurrenceRule, but we have to change the "COUNT=" or "UNTIL=" part of it, so as to include the correct number of occurrences.
                                newRecurrenceEvents = followingEvents;  //We have the same following events, but we will change the CustomRecurrenceRule, CustomRecurrenceId and other fields.
                                string newCustomRecurrenceRule = evnt.CustomRecurrenceRule;
                                if (newCustomRecurrenceRule.Contains("COUNT"))
                                {
                                    int countIndex = newCustomRecurrenceRule.IndexOf("COUNT");
                                    int semicolonIndex = newCustomRecurrenceRule.IndexOf(";", countIndex);
                                    if (semicolonIndex == -1)  //If "COUNT=x" is the last part of the rule.
                                    {
                                        newCustomRecurrenceRule = newCustomRecurrenceRule.Substring(0, countIndex - 1); //-1 to remove also the preceding ";"
                                    }
                                    else
                                    {
                                        newCustomRecurrenceRule = newCustomRecurrenceRule.Substring(0, countIndex) + newCustomRecurrenceRule.Substring(semicolonIndex + 1);
                                    }
                                }
                                else if (newCustomRecurrenceRule.Contains("UNTIL"))
                                {
                                    int untilIndex = newCustomRecurrenceRule.IndexOf("UNTIL");
                                    int semicolonIndex = newCustomRecurrenceRule.IndexOf(";", untilIndex);
                                    if (semicolonIndex == -1)  //If "UNTIL=yyyyMMddT000000Z" is the last part of the rule.
                                    {
                                        newCustomRecurrenceRule = newCustomRecurrenceRule.Substring(0, untilIndex - 1); //-1 to remove also the preceding ";"
                                    }
                                    else
                                    {
                                        newCustomRecurrenceRule = newCustomRecurrenceRule.Substring(0, untilIndex) + newCustomRecurrenceRule.Substring(semicolonIndex + 1);
                                    }
                                }

                                ////Removes from the end of (new) recurrenceEvents a number of events equal to the number of previous events the current.
                                //newRecurrenceEvents = newRecurrenceEvents.Take(followingEvents.Count - previousEvents.Count).ToList();

                                //Generates a new CustomRecurrenceId for the current and following events.
                                Guid newCustomRecurrenceId = Guid.CreateVersion7();
                                //Adds the new "COUNT=x" in CustomRecurrenceRule based on the new amount of followingEvents + 1 (the current event).
                                newCustomRecurrenceRule += (newCustomRecurrenceRule.EndsWith(";") ? "COUNT=" + (followingEvents.Count + 1).ToString() : ";COUNT=" + (followingEvents.Count + 1).ToString()) + ";";

                                //Pass the new CustomRecurrenceRule and CustomRecurrenceId to the current and following events.
                                foreach (Event recurrenceEvent in newRecurrenceEvents)
                                {
                                    recurrenceEvent.CustomRecurrenceRule = newCustomRecurrenceRule;
                                    recurrenceEvent.CustomRecurrenceId = newCustomRecurrenceId;
                                    recurrenceEvent.ObjectState = ObjectState.Modified;  //We set as Modified because we are actually editing the existing following events.
                                    //Copies changes to data to following recurrent events.
                                    recurrenceEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                                    recurrenceEvent.AllDay = evnt.AllDay;
                                    recurrenceEvent.EventCategoryId = evnt.EventCategoryId;
                                    recurrenceEvent.Block = evnt.Block;
                                    recurrenceEvent.ContactId = evnt.ContactId;
                                    recurrenceEvent.DateModifiedUtc = DateTime.UtcNow;
                                    recurrenceEvent.Description = evnt.Description;
                                    recurrenceEvent.StartTimeUtc = new DateTime(recurrenceEvent.StartTimeUtc.Year, recurrenceEvent.StartTimeUtc.Month, recurrenceEvent.StartTimeUtc.Day, evnt.StartTimeUtc.Hour, evnt.StartTimeUtc.Minute, recurrenceEvent.StartTimeUtc.Second);
                                    recurrenceEvent.EndTimeUtc = new DateTime(recurrenceEvent.EndTimeUtc.Year, recurrenceEvent.EndTimeUtc.Month, recurrenceEvent.EndTimeUtc.Day, evnt.EndTimeUtc.Hour, evnt.EndTimeUtc.Minute, recurrenceEvent.EndTimeUtc.Second);
                                    recurrenceEvent.EventStateId = evnt.EventStateId;
                                    recurrenceEvent.Location = evnt.Location;
                                    recurrenceEvent.Subject = evnt.Subject;
                                    //Updates the EventUsers
                                    foreach (EventUser eventUser in evnt.EventUsers)
                                    {
                                        if (eventUser.ObjectState == ObjectState.Added)
                                        {
                                            EventUser recurEventUser = recurrenceEvent.AddNewEventUser(eventUser.UserId);
                                        }
                                        else if (eventUser.ObjectState == ObjectState.Deleted)
                                        {
                                            EventUser? tempEventUser = recurrenceEvent.EventUsers.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                                            if (tempEventUser != null)
                                            {
                                                tempEventUser.ObjectState = ObjectState.Deleted;
                                            }
                                        }
                                    }
                                }

                                //Merges the new following events with the rest, so as to be saved all together.
                                eventsToSave.AddRange(newRecurrenceEvents);

                                //Finally, we set as Modified the current event and added it to the modifiedEvents so as to be saved all together.
                                evnt.CustomRecurrenceRule = newCustomRecurrenceRule;
                                evnt.CustomRecurrenceId = newCustomRecurrenceId;
                                evnt.ObjectState = ObjectState.Modified;
                                eventsToSave.Add(evnt);

                                ////If RecurrenceRule contains "COUNT", then we should remove the number of events which are before the current event.
                                //if (evnt.CustomRecurrenceRule.Contains("COUNT"))
                                //{
                                //    //Removes from the end of (new) recurrenceEvents a number of events equal to the number of previous events the current.
                                //    newRecurrenceEvents = newRecurrenceEvents.Take(newRecurrenceEvents.Count - previousEvents.Count).ToList();
                                //}
                            }


                            #endregion



                            //Set the TimeZoneId of the all recurrent events, because if it is not set validation error will happen during save.
                            foreach (Event eventToSave in eventsToSave)
                            {
                                eventToSave.UserTimeZoneId = userTimeZone!;
                            }

                            await this.SaveEventsBatch(eventsToSave);






                            ////If the initial Event had recurrence and the updated Event has exact the same recurrency (recurrency is not changed).
                            //else if (initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == true && initialEvent.CustomRecurrenceRule == evnt.CustomRecurrenceRule)
                            //{
                            //    //Depending of the user respond about the type of update.

                            //    //If user wants to update only the current recurrence.
                            //    if (recurrentEventUpdateType == RecurrentEventHandlingType.Current)
                            //    {
                            //        //await this.CreateOrUpdateNoRecurrentEvent(evnt);

                            //        this.dbContext.Attach(evnt);
                            //        await this.dbContext.SaveChangesAsync();
                            //    }
                            //    else if (recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                            //    {
                            //        //Finds the following events of recurrence.
                            //        List<Event> followingEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();

                            //        //Finds the previous events of current Event.
                            //        List<Event> previousEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc < evnt.StartTimeUtc).AsNoTracking().ToList();
                            //        //If the current Event is included in the previous Events (because e.g. the new StartDate was changed), then we remove it from previous Events.
                            //        if (previousEvents.Where(x => x.EventId == evnt.EventId).Any())
                            //        {
                            //            previousEvents.Remove(previousEvents.Where(x => x.EventId == evnt.EventId).FirstOrDefault());
                            //        }

                            //        ////Copies changes to data to following recurrent events.
                            //        //foreach (Event followingEvent in followingEvents)
                            //        //{
                            //        //    followingEvent.ObjectState = ObjectState.Modified;
                            //        //    followingEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                            //        //    followingEvent.AllDay = evnt.AllDay;
                            //        //    followingEvent.EventCategoryId = evnt.EventCategoryId;
                            //        //    followingEvent.Block = evnt.Block;
                            //        //    followingEvent.ContactId = evnt.ContactId;
                            //        //    followingEvent.DateModifiedUtc = DateTime.UtcNow;
                            //        //    followingEvent.Description = evnt.Description;
                            //        //    followingEvent.StartTimeUtc = new DateTime(followingEvent.StartTimeUtc.Year, followingEvent.StartTimeUtc.Month, followingEvent.StartTimeUtc.Day, evnt.StartTimeUtc.Hour, evnt.StartTimeUtc.Minute, followingEvent.StartTimeUtc.Second);
                            //        //    followingEvent.EndTimeUtc = new DateTime(followingEvent.EndTimeUtc.Year, followingEvent.EndTimeUtc.Month, followingEvent.EndTimeUtc.Day, evnt.EndTimeUtc.Hour, evnt.EndTimeUtc.Minute, followingEvent.EndTimeUtc.Second);
                            //        //    followingEvent.EventStateId = evnt.EventStateId;
                            //        //    followingEvent.Location = evnt.Location;
                            //        //    followingEvent.Subject = evnt.Subject;
                            //        //    foreach (EventUser eventUser in evnt.EventUsers)  //Updates the EventUsers
                            //        //    {
                            //        //        if (eventUser.ObjectState == ObjectState.Added)
                            //        //        {
                            //        //            EventUser recurEventUser = followingEvent.AddNewEventUser(eventUser.UserId);
                            //        //        }
                            //        //        else if (eventUser.ObjectState == ObjectState.Deleted)
                            //        //        {
                            //        //            EventUser? tempEventUser = followingEvent.EventUsers.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                            //        //            if (tempEventUser != null)
                            //        //            {
                            //        //                tempEventUser.ObjectState = ObjectState.Deleted;
                            //        //            }
                            //        //        }
                            //        //    }
                            //        //}


                            //        //Deletes the "old" following events of recurrence.
                            //        foreach (Event followingEvent in followingEvents)
                            //        {
                            //            followingEvent.ObjectState = ObjectState.Deleted;
                            //        }

                            //        //Generates the new recurrent events.
                            //        List<Event> newRecurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, evnt.CustomRecurrenceId!.Value);
                            //        //If RecurrenceRule contains "COUNT", then we should remove the number of events which are before the current event.
                            //        if (evnt.CustomRecurrenceRule.Contains("COUNT"))
                            //        {
                            //            //Removes from the end of (new) recurrenceEvents a number of events equal to the number of previous events the current.
                            //            newRecurrenceEvents = newRecurrenceEvents.Take(newRecurrenceEvents.Count - previousEvents.Count).ToList();
                            //        }

                            //        //Merges the deleted following events with the new created ones, so as to be saved all together.
                            //        followingEvents.AddRange(newRecurrenceEvents);

                            //        //Finally, we delete the current event since we have selected to edit current and following events, so it has been recreated.
                            //        if (followingEvents.Where(x => x.EventId == evnt.EventId).Any() == false) //If current Event does not exists in the list of followingEvents (because e.g. the new StartDate was changed), then we add it for deletion.
                            //        {
                            //            evnt.ObjectState = ObjectState.Deleted;
                            //            followingEvents.Add(evnt);
                            //        }

                            //        //Set the TimeZoneId of the all recurrent events, because if it is not set validation error will happen during save.
                            //        foreach (Event followingEvent in followingEvents)
                            //        {
                            //            followingEvent.UserTimeZoneId = userTimeZone!;
                            //        }

                            //        await this.SaveEventsBatch(followingEvents);
                            //    }
                            //    else if (recurrentEventUpdateType == RecurrentEventHandlingType.All)
                            //    {
                            //        //Finds all events of recurrence.
                            //        List<Event> recurrentEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).AsNoTracking().ToList();

                            //        //Copies changes to data to following recurrent events.
                            //        foreach (Event recurrentEvent in recurrentEvents)
                            //        {
                            //            recurrentEvent.ObjectState = ObjectState.Modified;
                            //            recurrentEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                            //            recurrentEvent.AllDay = evnt.AllDay;
                            //            recurrentEvent.EventCategoryId = evnt.EventCategoryId;
                            //            recurrentEvent.Block = evnt.Block;
                            //            recurrentEvent.ContactId = evnt.ContactId;
                            //            recurrentEvent.DateModifiedUtc = DateTime.UtcNow;
                            //            recurrentEvent.Description = evnt.Description;
                            //            recurrentEvent.StartTimeUtc = new DateTime(recurrentEvent.StartTimeUtc.Year, recurrentEvent.StartTimeUtc.Month, recurrentEvent.StartTimeUtc.Day, evnt.StartTimeUtc.Hour, evnt.StartTimeUtc.Minute, recurrentEvent.StartTimeUtc.Second);
                            //            recurrentEvent.EndTimeUtc = new DateTime(recurrentEvent.EndTimeUtc.Year, recurrentEvent.EndTimeUtc.Month, recurrentEvent.EndTimeUtc.Day, evnt.EndTimeUtc.Hour, evnt.EndTimeUtc.Minute, recurrentEvent.EndTimeUtc.Second);
                            //            recurrentEvent.EventStateId = evnt.EventStateId;
                            //            recurrentEvent.Location = evnt.Location;
                            //            recurrentEvent.Subject = evnt.Subject;
                            //            foreach (EventUser eventUser in evnt.EventUsers)  //Updates the EventUsers
                            //            {
                            //                if (eventUser.ObjectState == ObjectState.Added)
                            //                {
                            //                    EventUser recurEventUser = recurrentEvent.AddNewEventUser(eventUser.UserId);
                            //                }
                            //                else if (eventUser.ObjectState == ObjectState.Deleted)
                            //                {
                            //                    EventUser? tempEventUser = recurrentEvent.EventUsers.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                            //                    if (tempEventUser != null)
                            //                    {
                            //                        tempEventUser.ObjectState = ObjectState.Deleted;
                            //                    }
                            //                }
                            //            }
                            //        }

                            //        await this.SaveEventsBatch(recurrentEvents);
                            //    }
                            //}
                            ////If the initial Event and the updated Event had recurrence and the recurrency is changed.
                            //else if (initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == true && initialEvent.CustomRecurrenceRule != evnt.CustomRecurrenceRule)
                            //{
                            //    //Depending of the user respond about the type of update.

                            //    //If user want to update only the current recurrence.
                            //    if (recurrentEventUpdateType == RecurrentEventHandlingType.Current)
                            //    {
                            //        await this.CreateOrUpdateNoRecurrentEvent(evnt);
                            //    }
                            //    else if (recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                            //    {
                            //        //Explanation: Since recurrence is changed, current and following Events should be deleted and new ones (from now on) should be created (with different recurrence).

                            //        #region  Finds the Events that should be deleted.
                            //        //Finds the following events of recurrence.
                            //        List<Event> deleteEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();

                            //        //Copies changes to data to following recurrent events.
                            //        foreach (Event deleteEvent in deleteEvents)
                            //        {
                            //            deleteEvent.ObjectState = ObjectState.Deleted;
                            //            deleteEvent.UserTimeZoneId = evnt.UserTimeZoneId;  //Also set the UserTimeZoneId (not to create new loop).
                            //        }

                            //        evnt.ObjectState = ObjectState.Deleted;
                            //        deleteEvents.Add(evnt);  //Adds the current event for deletion.
                            //        #endregion

                            //        #region  Created the new "current" and "following" recurrent Events.
                            //        List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());
                            //        List<Event> newCurrentAndFollowingEvents = recurrenceEvents.Where(x => x.StartTimeUtc == DateTime.Today.ToUniversalTime()).ToList();  //Gets only the new recurrent events from today and following days.
                            //        #endregion

                            //        //Saves all Events (deleted or created).
                            //        List<Event> eventsForCreateUpdate = new List<Event>();
                            //        eventsForCreateUpdate.AddRange(deleteEvents);
                            //        eventsForCreateUpdate.AddRange(newCurrentAndFollowingEvents);

                            //        await this.SaveEventsBatch(eventsForCreateUpdate);
                            //    }
                            //    else if (recurrentEventUpdateType == RecurrentEventHandlingType.All)
                            //    {
                            //        //Explanation: Since recurrence is changed, all Events should be deleted and new ones should be created (with different recurrence).

                            //        #region  Finds the Events that should be deleted.
                            //        //Finds the following events of recurrence.
                            //        List<Event> deleteEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).ToList();

                            //        //Copies changes to data to following recurrent events.
                            //        foreach (Event deleteEvent in deleteEvents)
                            //        {
                            //            deleteEvent.ObjectState = ObjectState.Deleted;
                            //            deleteEvent.UserTimeZoneId = evnt.UserTimeZoneId;  //Also set the UserTimeZoneId (not to create new loop).
                            //        }
                            //        #endregion

                            //        //Created the new "current" and "following" recurrent Events.
                            //        List<Event> newRecurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                            //        //Saves all Events (deleted or created).
                            //        List<Event> eventsForCreateUpdate = new List<Event>();
                            //        eventsForCreateUpdate.AddRange(deleteEvents);
                            //        eventsForCreateUpdate.AddRange(newRecurrenceEvents);

                            //        await this.SaveEventsBatch(eventsForCreateUpdate);
                            //    }
                            //}

                        }
                        else if (recurrentEventUpdateType == RecurrentEventHandlingType.All)
                        {
                            //Explanation: Since recurrence is changed, all Events should be deleted and new ones should be created (with different recurrence).

                            #region  Finds the Events that should be deleted.
                            //Finds the following events of recurrence.
                            List<Event> deleteEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).ToList();

                            //Copies changes to data to following recurrent events.
                            foreach (Event deleteEvent in deleteEvents)
                            {
                                deleteEvent.ObjectState = ObjectState.Deleted;
                                deleteEvent.UserTimeZoneId = evnt.UserTimeZoneId;  //Also set the UserTimeZoneId (not to create new loop).
                            }
                            #endregion

                            //Created the new "current" and "following" recurrent Events.
                            List<Event> newRecurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                            //Saves all Events (deleted or created).
                            List<Event> eventsForCreateUpdate = new List<Event>();
                            eventsForCreateUpdate.AddRange(deleteEvents);
                            eventsForCreateUpdate.AddRange(newRecurrenceEvents);

                            await this.SaveEventsBatch(eventsForCreateUpdate);
                        }
                        #endregion
                        #endregion

                        //Response
                    }
                }
            }
            catch (DbUpdateConcurrencyException ex)
            {
                throw;
            }
            catch (ApplicationException ex)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { evnt.EventId });
                throw;
            }
        }

        public async Task CreateOrUpdateEventBACKUP(Event evnt, RecurrentEventHandlingType? recurrentEventUpdateType, List<DateTime>? recurrenceDates, string? userTimeZone)
        {
            try
            {
                //Validation
                if (evnt == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                InsertUpdateEventValidator validator = new InsertUpdateEventValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(evnt);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage;  // + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                #region  Query
                //If we create a new Event
                if (evnt.ObjectState == Data.Model.ObjectState.Added)
                {
                    //If there is no recurrence.
                    if (evnt.CustomRecurrence == false)
                    {
                        await this.CreateOrUpdateNoRecurrentEvent(evnt);
                    }
                    else  //If there is recurrence.
                    {
                        List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                        //Set the TimeZoneId of the new recurrent events.
                        foreach (Event recurrenceEvent in recurrenceEvents)
                        {
                            recurrenceEvent.UserTimeZoneId = userTimeZone!;
                        }

                        await this.SaveEventsBatch(recurrenceEvents);
                    }
                }

                //If we update the Event.
                else if (evnt.ObjectState == Data.Model.ObjectState.Modified)
                {
                    Event? initialEvent = await this.GetEvent(evnt.EventId);

                    //If the event we are going to update it does not exists.
                    if (initialEvent == null)
                    {
                        throw new ApplicationException(Resources.GlobalResource.EventNotFoundMessage);
                    }
                    else  //If the Event we are going to update exists in database.
                    {
                        //If the initial Event and the updated Event does not have recurrence.
                        if (initialEvent.CustomRecurrence == false && evnt.CustomRecurrence == false)
                        {
                            await this.CreateOrUpdateNoRecurrentEvent(evnt);  //This is the most simple case.
                        }

                        //If the initial Event does not have recurrence but the new Event has a recurrence.
                        else if (initialEvent.CustomRecurrence == false && evnt.CustomRecurrence == true)
                        {
                            List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                            //We delete initial Event since we are going to create the recurrent events.
                            evnt.ObjectState = ObjectState.Deleted;
                            recurrenceEvents.Add(evnt);  //Adds current Event in the batch list of events, so as to be deleted.
                            await this.SaveEventsBatch(recurrenceEvents);  //Creates the new recurrent events and deletes the original one.
                        }

                        //If the initial Event had recurrence and the updated Event has exact the same recurrency (recurrency is not changed).
                        else if (initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == true && initialEvent.CustomRecurrenceRule == evnt.CustomRecurrenceRule)
                        {
                            //Depending of the user respond about the type of update.

                            //If user wants to update only the current recurrence.
                            if (recurrentEventUpdateType == RecurrentEventHandlingType.Current)
                            {
                                //await this.CreateOrUpdateNoRecurrentEvent(evnt);

                                this.dbContext.Attach(evnt);
                                await this.dbContext.SaveChangesAsync();
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                            {
                                //Finds the following events of recurrence.
                                List<Event> followingEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();

                                //Finds the previous events of current Event.
                                List<Event> previousEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc < evnt.StartTimeUtc).AsNoTracking().ToList();
                                //If the current Event is included in the previous Events (because e.g. the new StartDate was changed), then we remove it from previous Events.
                                if (previousEvents.Where(x => x.EventId == evnt.EventId).Any())
                                {
                                    previousEvents.Remove(previousEvents.Where(x => x.EventId == evnt.EventId).FirstOrDefault());
                                }

                                ////Copies changes to data to following recurrent events.
                                //foreach (Event followingEvent in followingEvents)
                                //{
                                //    followingEvent.ObjectState = ObjectState.Modified;
                                //    followingEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                                //    followingEvent.AllDay = evnt.AllDay;
                                //    followingEvent.EventCategoryId = evnt.EventCategoryId;
                                //    followingEvent.Block = evnt.Block;
                                //    followingEvent.ContactId = evnt.ContactId;
                                //    followingEvent.DateModifiedUtc = DateTime.UtcNow;
                                //    followingEvent.Description = evnt.Description;
                                //    followingEvent.StartTimeUtc = new DateTime(followingEvent.StartTimeUtc.Year, followingEvent.StartTimeUtc.Month, followingEvent.StartTimeUtc.Day, evnt.StartTimeUtc.Hour, evnt.StartTimeUtc.Minute, followingEvent.StartTimeUtc.Second);
                                //    followingEvent.EndTimeUtc = new DateTime(followingEvent.EndTimeUtc.Year, followingEvent.EndTimeUtc.Month, followingEvent.EndTimeUtc.Day, evnt.EndTimeUtc.Hour, evnt.EndTimeUtc.Minute, followingEvent.EndTimeUtc.Second);
                                //    followingEvent.EventStateId = evnt.EventStateId;
                                //    followingEvent.Location = evnt.Location;
                                //    followingEvent.Subject = evnt.Subject;
                                //    foreach (EventUser eventUser in evnt.EventUsers)  //Updates the EventUsers
                                //    {
                                //        if (eventUser.ObjectState == ObjectState.Added)
                                //        {
                                //            EventUser recurEventUser = followingEvent.AddNewEventUser(eventUser.UserId);
                                //        }
                                //        else if (eventUser.ObjectState == ObjectState.Deleted)
                                //        {
                                //            EventUser? tempEventUser = followingEvent.EventUsers.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                                //            if (tempEventUser != null)
                                //            {
                                //                tempEventUser.ObjectState = ObjectState.Deleted;
                                //            }
                                //        }
                                //    }
                                //}


                                //Deletes the "old" following events of recurrence.
                                foreach (Event followingEvent in followingEvents)
                                {
                                    followingEvent.ObjectState = ObjectState.Deleted;
                                }

                                //Generates the new recurrent events.
                                List<Event> newRecurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, evnt.CustomRecurrenceId!.Value);
                                //If RecurrenceRule contains "COUNT", then we should remove the number of events which are before the current event.
                                if (evnt.CustomRecurrenceRule.Contains("COUNT"))
                                {
                                    //Removes from the end of (new) recurrenceEvents a number of events equal to the number of previous events the current.
                                    newRecurrenceEvents = newRecurrenceEvents.Take(newRecurrenceEvents.Count - previousEvents.Count).ToList();
                                }

                                //Merges the deleted following events with the new created ones, so as to be saved all together.
                                followingEvents.AddRange(newRecurrenceEvents);

                                //Finally, we delete the current event since we have selected to edit current and following events, so it has been recreated.
                                if (followingEvents.Where(x => x.EventId == evnt.EventId).Any() == false) //If current Event does not exists in the list of followingEvents (because e.g. the new StartDate was changed), then we add it for deletion.
                                {
                                    evnt.ObjectState = ObjectState.Deleted;
                                    followingEvents.Add(evnt);
                                }

                                //Set the TimeZoneId of the all recurrent events, because if it is not set validation error will happen during save.
                                foreach (Event followingEvent in followingEvents)
                                {
                                    followingEvent.UserTimeZoneId = userTimeZone!;
                                }

                                await this.SaveEventsBatch(followingEvents);
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.All)
                            {
                                //Finds all events of recurrence.
                                List<Event> recurrentEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).AsNoTracking().ToList();

                                //Copies changes to data to following recurrent events.
                                foreach (Event recurrentEvent in recurrentEvents)
                                {
                                    recurrentEvent.ObjectState = ObjectState.Modified;
                                    recurrentEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                                    recurrentEvent.AllDay = evnt.AllDay;
                                    recurrentEvent.EventCategoryId = evnt.EventCategoryId;
                                    recurrentEvent.Block = evnt.Block;
                                    recurrentEvent.ContactId = evnt.ContactId;
                                    recurrentEvent.DateModifiedUtc = DateTime.UtcNow;
                                    recurrentEvent.Description = evnt.Description;
                                    recurrentEvent.StartTimeUtc = new DateTime(recurrentEvent.StartTimeUtc.Year, recurrentEvent.StartTimeUtc.Month, recurrentEvent.StartTimeUtc.Day, evnt.StartTimeUtc.Hour, evnt.StartTimeUtc.Minute, recurrentEvent.StartTimeUtc.Second);
                                    recurrentEvent.EndTimeUtc = new DateTime(recurrentEvent.EndTimeUtc.Year, recurrentEvent.EndTimeUtc.Month, recurrentEvent.EndTimeUtc.Day, evnt.EndTimeUtc.Hour, evnt.EndTimeUtc.Minute, recurrentEvent.EndTimeUtc.Second);
                                    recurrentEvent.EventStateId = evnt.EventStateId;
                                    recurrentEvent.Location = evnt.Location;
                                    recurrentEvent.Subject = evnt.Subject;
                                    foreach (EventUser eventUser in evnt.EventUsers)  //Updates the EventUsers
                                    {
                                        if (eventUser.ObjectState == ObjectState.Added)
                                        {
                                            EventUser recurEventUser = recurrentEvent.AddNewEventUser(eventUser.UserId);
                                        }
                                        else if (eventUser.ObjectState == ObjectState.Deleted)
                                        {
                                            EventUser? tempEventUser = recurrentEvent.EventUsers.Where(x => x.UserId == eventUser.UserId).FirstOrDefault();
                                            if (tempEventUser != null)
                                            {
                                                tempEventUser.ObjectState = ObjectState.Deleted;
                                            }
                                        }
                                    }
                                }

                                await this.SaveEventsBatch(recurrentEvents);
                            }
                        }
                        //If the initial Event and the updated Event had recurrence and the recurrency is changed.
                        else if (initialEvent.CustomRecurrence == true && evnt.CustomRecurrence == true && initialEvent.CustomRecurrenceRule != evnt.CustomRecurrenceRule)
                        {
                            //Depending of the user respond about the type of update.

                            //If user want to update only the current recurrence.
                            if (recurrentEventUpdateType == RecurrentEventHandlingType.Current)
                            {
                                await this.CreateOrUpdateNoRecurrentEvent(evnt);
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                            {
                                //Explanation: Since recurrence is changed, current and following Events should be deleted and new ones (from now on) should be created (with different recurrence).

                                #region  Finds the Events that should be deleted.
                                //Finds the following events of recurrence.
                                List<Event> deleteEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).AsNoTracking().ToList();

                                //Copies changes to data to following recurrent events.
                                foreach (Event deleteEvent in deleteEvents)
                                {
                                    deleteEvent.ObjectState = ObjectState.Deleted;
                                    deleteEvent.UserTimeZoneId = evnt.UserTimeZoneId;  //Also set the UserTimeZoneId (not to create new loop).
                                }

                                evnt.ObjectState = ObjectState.Deleted;
                                deleteEvents.Add(evnt);  //Adds the current event for deletion.
                                #endregion

                                #region  Created the new "current" and "following" recurrent Events.
                                List<Event> recurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());
                                List<Event> newCurrentAndFollowingEvents = recurrenceEvents.Where(x => x.StartTimeUtc == DateTime.Today.ToUniversalTime()).ToList();  //Gets only the new recurrent events from today and following days.
                                #endregion

                                //Saves all Events (deleted or created).
                                List<Event> eventsForCreateUpdate = new List<Event>();
                                eventsForCreateUpdate.AddRange(deleteEvents);
                                eventsForCreateUpdate.AddRange(newCurrentAndFollowingEvents);

                                await this.SaveEventsBatch(eventsForCreateUpdate);
                            }
                            else if (recurrentEventUpdateType == RecurrentEventHandlingType.All)
                            {
                                //Explanation: Since recurrence is changed, all Events should be deleted and new ones should be created (with different recurrence).

                                #region  Finds the Events that should be deleted.
                                //Finds the following events of recurrence.
                                List<Event> deleteEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).ToList();

                                //Copies changes to data to following recurrent events.
                                foreach (Event deleteEvent in deleteEvents)
                                {
                                    deleteEvent.ObjectState = ObjectState.Deleted;
                                    deleteEvent.UserTimeZoneId = evnt.UserTimeZoneId;  //Also set the UserTimeZoneId (not to create new loop).
                                }
                                #endregion

                                //Created the new "current" and "following" recurrent Events.
                                List<Event> newRecurrenceEvents = this.GenerateRecurrentEvents(evnt, recurrenceDates!, evnt.CustomRecurrenceRule, Guid.CreateVersion7());

                                //Saves all Events (deleted or created).
                                List<Event> eventsForCreateUpdate = new List<Event>();
                                eventsForCreateUpdate.AddRange(deleteEvents);
                                eventsForCreateUpdate.AddRange(newRecurrenceEvents);

                                await this.SaveEventsBatch(eventsForCreateUpdate);
                            }
                        }
                    }
                }

                #endregion

                //Response
            }
            catch (DbUpdateConcurrencyException ex)
            {
                throw;
            }
            catch (ApplicationException ex)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { evnt.EventId });
                throw;
            }
        }


        /// <summary>
        /// This function creates, updates and deletes Events.
        /// </summary>
        /// <param name="events"></param>
        /// <returns></returns>
        public async Task SaveEventsBatch(List<Event> events)
        {
            try
            {
                if (events == null || events.Count == 0)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                InsertUpdateEventValidator insertUpdateEventValidator = new InsertUpdateEventValidator();
                DeleteEventValidator deleteEventValidator = new DeleteEventValidator();
                List<string> validationErrors = new List<string>();


                foreach (var evnt in events)
                {
                    FluentValidation.Results.ValidationResult result = new FluentValidation.Results.ValidationResult();
                    if (evnt.ObjectState == ObjectState.Added || evnt.ObjectState == ObjectState.Modified)
                    {
                        result = insertUpdateEventValidator.Validate(evnt);
                    }
                    else if (evnt.ObjectState == ObjectState.Deleted)
                    {
                        result = deleteEventValidator.Validate(evnt);
                    }
                    
                    if (!result.IsValid)
                    {
                        foreach (var failure in result.Errors)
                        {
                            validationErrors.Add(failure.ErrorMessage);
                        }
                    }
                    else
                    {
                        this.dbContext.Attach(evnt);
                    }
                }

                if (validationErrors.Count > 0)
                {
                    throw new ApplicationException(string.Join(". ", validationErrors));
                }

                await this.dbContext.SaveChangesAsync();
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message);
                throw;
            }
        }

        public async Task DeleteEvent(Guid eventId, RecurrentEventHandlingType? recurrentEventUpdateType)
        {
            Event? evnt = await this.dbContext.Events.Where(x => x.EventId == eventId).FirstAsync();
            if (evnt != null)
            {
                //Αν δεν έχει δοθεί τιμή στο παράμετρο recurrentEventUpdateType, τότε θεωρείται ότι θέλουμε να διαγράψουμε το συγκεκριμένο Event.
                //Ή αν έχει δοθεί τιμή Current, τότε θα διαγραφεί μόνο το συγκεκριμένο Event.
                if (recurrentEventUpdateType == null || (recurrentEventUpdateType != null && recurrentEventUpdateType == RecurrentEventHandlingType.Current))
                {
                    DeleteEventValidator validator = new DeleteEventValidator();
                    List<string> validationErrors = new List<string>();

                    FluentValidation.Results.ValidationResult result = validator.Validate(evnt);
                    if (!result.IsValid)
                    {
                        foreach (var failure in result.Errors)
                        {
                            validationErrors.Add(failure.ErrorMessage);
                        }
                    }

                    evnt.ObjectState = ObjectState.Deleted;
                    this.dbContext.Attach(evnt);
                    this.dbContext.SaveChanges();
                }
                else if (recurrentEventUpdateType != null && recurrentEventUpdateType == RecurrentEventHandlingType.CurrentAndFollowing)
                {

                    //Finds the following events of recurrence.
                    List<Event> followingEvents = this.dbContext.Events.Include(x => x.EventUsers).Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId && x.StartTimeUtc > evnt.StartTimeUtc).ToList();

                    //Copies changes to data to following recurrent events.
                    foreach (Event followingEvent in followingEvents)
                    {
                        followingEvent.ObjectState = ObjectState.Deleted;
                    }
                    evnt.ObjectState = ObjectState.Deleted;
                    followingEvents.Add(evnt);  //Adds also the current event so as to be deleted with the following ones.

                    await this.SaveEventsBatch(followingEvents);
                }
                else if (recurrentEventUpdateType != null && recurrentEventUpdateType == RecurrentEventHandlingType.All)
                {
                    //Finds the following events of recurrence.
                    List<Event> deleteEvents = this.dbContext.Events.Where(x => x.CustomRecurrenceId == evnt.CustomRecurrenceId).ToList();

                    //Copies changes to data to following recurrent events.
                    foreach (Event deleteEvent in deleteEvents)
                    {
                        deleteEvent.ObjectState = ObjectState.Deleted;
                    }

                    await this.SaveEventsBatch(deleteEvents);
                }
            }
            else
            {
                throw new ApplicationException(Resources.GlobalResource.InvalidDataMessage);
            }
        }

        public async Task<Dictionary<User, int>> GetTotalUpcomingEventsForTomorrowByUser(Guid tenantId)
        {
            try
            {
                DateTime tomorrow = DateTime.UtcNow.AddDays(1).Date;

                var users = await dbContext.Users
                    .Where(u => u.AvailableInEventUsers && u.TenantId == tenantId)
                    .AsNoTracking().ToListAsync();

                var events = await dbContext.Events
                    .Include(e => e.EventUsers)
                    .Where(e => e.StartTimeUtc.Date == tomorrow && e.TenantId == tenantId)
                    .AsNoTracking().ToListAsync();

                var groupedEvents = events
                    .SelectMany(e => e.EventUsers.Select(u => new { UserId = u.UserId, Event = e }))
                    .GroupBy(x => x.UserId)
                    .ToDictionary(g => users.First(u => u.UserId == g.Key), g => g.Select(x => x.Event).Count());

                return groupedEvents;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

        public Dictionary<DateTime, int> GetTotalEventsPerDayForCurrentMonth(string tenantId)
        {
            var currentMonth = DateTime.UtcNow.Month;
            var currentYear = DateTime.UtcNow.Year;

            var eventsPerDay = dbContext.Events
                .Where(e => e.StartTimeUtc.Month == currentMonth && e.StartTimeUtc.Year == currentYear)
                .GroupBy(e => e.StartTimeUtc.Date)
                .ToDictionary(g => g.Key, g => g.Count());

            return eventsPerDay;
        }

        public List<Event> GenerateRecurrentEvents(Event evnt, List<DateTime> recurrenceDates, string recurrenceRule, Guid recurrenceIdGuid)
        {
            var recurrentEvents = new List<Event>();

            foreach (DateTime recurrenceDate in recurrenceDates)
            {
                //Event recurrentEvent = evnt.Clone();
                Event recurrentEvent = new Event();
                recurrentEvent.EventId = Guid.CreateVersion7();
                recurrentEvent.TenantId = evnt.TenantId;
                foreach (EventUser eventUser in evnt.EventUsers)
                {
                    //Explanation: Here the EventUsers list of each cloned Event, is exactly the same as the EventUsers of the initial Event. So, we should set new Guids and ObjectState to Added.
                    EventUser recurEventUser = recurrentEvent.AddNewEventUser(eventUser.UserId);
                }
                recurrentEvent.ObjectState = ObjectState.Added;
                recurrentEvent.AllDay = evnt.AllDay;
                recurrentEvent.Block = evnt.Block;
                recurrentEvent.ContactId = evnt.ContactId;
                recurrentEvent.UserTimeZoneId = evnt.UserTimeZoneId;
                recurrentEvent.Subject = evnt.Subject;
                recurrentEvent.Description = evnt.Description;
                recurrentEvent.EventCategoryId = evnt.EventCategoryId;
                recurrentEvent.Location = evnt.Location;
                recurrentEvent.DateCreatedUtc = DateTime.UtcNow;
                recurrentEvent.DateModifiedUtc = DateTime.UtcNow;
                recurrentEvent.StartTimeLocal = new DateTime(recurrenceDate.Year, recurrenceDate.Month, recurrenceDate.Day, evnt!.StartTimeLocal!.Value.Hour, evnt.StartTimeLocal!.Value.Minute, evnt!.StartTimeLocal!.Value.Second);
                recurrentEvent.EndTimeLocal = new DateTime(recurrenceDate.Year, recurrenceDate.Month, recurrenceDate.Day, evnt.EndTimeLocal!.Value.Hour, evnt.EndTimeLocal!.Value.Minute, evnt.EndTimeLocal!.Value.Second);
                recurrentEvent.StartTimeZone = evnt.StartTimeZone;
                recurrentEvent.EndTimeZone = evnt.EndTimeZone;
                recurrentEvent.CustomRecurrence = true;
                recurrentEvent.CustomRecurrenceId = recurrenceIdGuid;
                recurrentEvent.CustomRecurrenceRule = recurrenceRule;
                recurrentEvents.Add(recurrentEvent);
            }

            return recurrentEvents;
        }

        public async Task<int> GetFutureEventsCount(Guid tenantId)
        {
            try
            {
                // Query
                IQueryable<Event> query = this.dbContext.Events.AsNoTracking().AsQueryable();
                query = query.Where(c => c.StartTimeUtc > DateTime.UtcNow && c.TenantId == tenantId);

                // Return total count
                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }


        public async Task<int> GetTotalEventsForDate(Guid tenantId, DateTime currentDateLocal, string timeZoneId)
        {
            try
            {
                // Convert local date to UTC
                DateTime startOfDayUtc = TimeZoneInfo.ConvertTimeToUtc(currentDateLocal.Date, TimeZoneInfo.FindSystemTimeZoneById(timeZoneId));
                DateTime endOfDayUtc = startOfDayUtc.AddDays(1).AddTicks(-1);

                // Query
                IQueryable<Event> query = this.dbContext.Events.AsNoTracking().AsQueryable();
                query = query.Where(e => e.TenantId == tenantId && e.StartTimeUtc >= startOfDayUtc && e.StartTimeUtc <= endOfDayUtc);

                // Return total count
                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString(), "CurrentDateLocal=" + currentDateLocal.ToString() });
                throw;
            }
        }

        public async Task<List<Event>> GetEventsForExport(List<Guid> eventIds)
        {
            try
            {
                //Validation
                if (eventIds == null)
                {
                    throw new ArgumentNullException(nameof(eventIds), Resources.GlobalResource.InvalidDataMessage);
                }

                //Query
                IQueryable<Event> query = this.dbContext.Events
                    .Include(e => e.Contact)
                    .Include(e => e.EventCategory)
                    .Include(e => e.EventState)
                    .Include(e => e.EventUsers).ThenInclude(x => x.User)
                    .AsNoTracking()
                    .AsQueryable();

                query = query.Where(e => eventIds.Contains(e.EventId));

                //Execute query and return results
                List<Event> events = await query.ToListAsync();

                return events;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "EventIds=" + string.Join(",", eventIds) });
                throw;
            }
        }
    }
}
