﻿@using Blazored.FluentValidation
@using Blazored.LocalStorage
@using FluentBlue.Data.Model
@using FluentBlue.Data.Model.DBOs.Settings
@using FluentBlue.Shared.Utilities
@using FluentBlue.UI.Main.Auth
@using Microsoft.AspNetCore.Components.Forms
@using FluentBlue.UI.Main.Components
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.JSInterop
@using NodaTime.TimeZones
@using System.Globalization
@using Syncfusion.Blazor
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons
@using Microsoft.Extensions.Caching.Hybrid

@implements IDialogContentComponent<Data.Model.DBOs.Settings.UserSetting>

@inject HttpClient httpClient
@inject IDialogService dialogService
@inject ILogger<SettingsDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.SettingsWebApiClient> userSettingsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.RolesWebApiClient> rolesWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.EventCategoriesWebApiClient> eventCategoriesWebApiClientLogger
@inject IJSRuntime JS
@inject HybridCache cache
@inject NavigationManager navManager
@inject ILocalStorageService localStorage
@inject IFormFactor formFactor
@inject IRestartAppService restartAppService
@inject IDevicePreferences devicePreferences
@inject IAppVersionHelper appVersionHelper

<FluentDialogHeader Class="hidden"></FluentDialogHeader>

<FluentDialogBody class="overflow-x-hidden overflow-y-auto h-full max-h-full">
    <FluentStack Orientation="Orientation.Vertical" VerticalAlignment="VerticalAlignment.Top" Class="h-full max-h-full">
        <EditForm id="editForm" Model="@Content" class="w-full h-full max-h-full">
            <ChildContent Context="context2">
                <FluentValidationSummary></FluentValidationSummary>
                <FluentValidationValidator @ref="fluentValidationValidator" />

                <FluentStack Orientation="settingsOrientation" Class="w-full">
                    @if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                    {
                        <FluentStack Orientation="Orientation.Vertical">
                            <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-2">@Resources.SettingsDialogResource.Title</FluentLabel>
                            <FluentMenuButton @ref=menuBtn Text="@this.menuBtnText" class="w-full" OnMenuChanged="MenuBtnOnMenuChanged">
                                <FluentMenuItem Id="General">@Resources.SettingsDialogResource.General</FluentMenuItem>
                                <FluentMenuItem Id="Contacts">@Resources.SettingsDialogResource.Contacts</FluentMenuItem>
                                <FluentMenuItem Id="Calendar">@Resources.SettingsDialogResource.Calendar</FluentMenuItem>
                                <FluentMenuItem Id="UsersRoles">@Resources.SettingsDialogResource.UsersRoles</FluentMenuItem>
                            </FluentMenuButton>
                        </FluentStack>
                    }
                    else
                    {
                        <FluentTabs Orientation="Orientation.Vertical" @ref="settingsTabs" Size="TabSize.Small" Class="w-64 py-0 ps-0" OnTabChange="SettingsTabsOnTabChange">
                            <div slot="start">
                                <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4 md:mt-3 lg:mt-3 xl:mt-2">Settings</FluentLabel>
                            </div>

                            <FluentTab Label="@Resources.SettingsDialogResource.General" Id="General" Icon="@(new Icons.Regular.Size20.Settings())">
                            </FluentTab>
                            <FluentTab Label="@Resources.SettingsDialogResource.Contacts" Id="Contacts" Icon="@(new Icons.Regular.Size20.People())">
                            </FluentTab>
                            <FluentTab Label="@Resources.SettingsDialogResource.Calendar" Id="Calendar" Icon="@(new Icons.Regular.Size20.People())">
                            </FluentTab>
                            <FluentTab Label="@Resources.SettingsDialogResource.UsersRoles" Id="UsersRoles" Visible="@((this.userRole?.CanAdministerRoles ?? false) || (this.userRole?.CanAdministerUsers ?? false))" Icon="@(new Icons.Regular.Size20.ShieldKeyhole())">
                            </FluentTab>
                        </FluentTabs>
                    }

                    <div class="w-full overflow-y-auto max-h-full">
                        @if (this.activeTab == "General")
                        {
                            <div class="hidden md:block lg:block xl:block">
                                <FluentLabel Typo="Typography.H4" Class="xs:hidden sm:hidden">@Resources.SettingsDialogResource.General</FluentLabel>
                            </div>
                            <div class="h-4" />
                            <FluentLabel Typo="Typography.H5">@Resources.SettingsDialogResource.LanguageTimeTitle</FluentLabel>
                            <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                            <div class="h-3" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.Language" Items="@(Enum.GetValues<Language>().Select(i => (Language)i))" TOption="Language" @bind-SelectedOption="@this.Content!.Language" Class="max-w-96">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@context.GetDisplayText()</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.Language)" />
                            @* <div class="h-4" />
                            <FluentCombobox Items="@CultureInfo.GetCultures(CultureTypes.SpecificCultures)" @bind-Value="@this.Content!.Culture" ImmediateDelay="300" Immediate="true" Label="@Resources.SettingsDialogResource.CultureInfo" Autocomplete="ComboboxAutocomplete.Both" Class="max-w-96" Height="230px" TOption="CultureInfo" OptionText="@(i => i.Name)" OptionValue="@(x => x.Name)">
                                <OptionTemplate>
                                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Class="w-full">
                                        <FluentLabel Class="w-1/2">@(context.NativeName + " (" + context.Name + ")")</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentCombobox>
                            <FluentValidationMessage For="@(() => this.Content!.Culture)" /> *@
                            <div class="h-4" />
                            <FluentCombobox Items="@timeZones" @bind-Value="@this.Content!.TimeZone" ImmediateDelay="300" Immediate="true" Label="@Resources.SettingsDialogResource.TimeZone" Autocomplete="ComboboxAutocomplete.Both" Class="max-w-96" Height="230px" TOption="string">
                                @* <FluentOption Value="Europe/Andorra">Europe/Andorra</FluentOption>
                                <FluentOption Value="Asia/Dubai">Asia/Dubai</FluentOption>
                                <FluentOption Value="Asia/Kabul">Asia/Kabul</FluentOption>
                                <FluentOption Value="America/Antigua">America/Antigua</FluentOption>
                                <FluentOption Value="America/Anguilla">America/Anguilla</FluentOption>
                                <FluentOption Value="Europe/Tirane">Europe/Tirane</FluentOption>
                                <FluentOption Value="Asia/Yerevan">Asia/Yerevan</FluentOption>
                                <FluentOption Value="Africa/Luanda">Africa/Luanda</FluentOption>
                                <FluentOption Value="Antarctica/McMurdo">Antarctica/McMurdo</FluentOption>
                                <FluentOption Value="Antarctica/Casey">Antarctica/Casey</FluentOption>
                                <FluentOption Value="Antarctica/Davis">Antarctica/Davis</FluentOption>
                                <FluentOption Value="Antarctica/DumontDUrville">Antarctica/DumontDUrville</FluentOption>
                                <FluentOption Value="Antarctica/Mawson">Antarctica/Mawson</FluentOption>
                                <FluentOption Value="Antarctica/Palmer">Antarctica/Palmer</FluentOption>
                                <FluentOption Value="Antarctica/Rothera">Antarctica/Rothera</FluentOption>
                                <FluentOption Value="Antarctica/Syowa">Antarctica/Syowa</FluentOption>
                                <FluentOption Value="Antarctica/Troll">Antarctica/Troll</FluentOption>
                                <FluentOption Value="Antarctica/Vostok">Antarctica/Vostok</FluentOption>
                                <FluentOption Value="America/Argentina/Buenos_Aires">America/Argentina/Buenos_Aires</FluentOption>
                                <FluentOption Value="America/Argentina/Cordoba">America/Argentina/Cordoba</FluentOption>
                                <FluentOption Value="America/Argentina/Salta">America/Argentina/Salta</FluentOption>
                                <FluentOption Value="America/Argentina/Jujuy">America/Argentina/Jujuy</FluentOption>
                                <FluentOption Value="America/Argentina/Tucuman">America/Argentina/Tucuman</FluentOption>
                                <FluentOption Value="America/Argentina/Catamarca">America/Argentina/Catamarca</FluentOption>
                                <FluentOption Value="America/Argentina/La_Rioja">America/Argentina/La_Rioja</FluentOption>
                                <FluentOption Value="America/Argentina/San_Juan">America/Argentina/San_Juan</FluentOption>
                                <FluentOption Value="America/Argentina/Mendoza">America/Argentina/Mendoza</FluentOption>
                                <FluentOption Value="America/Argentina/San_Luis">America/Argentina/San_Luis</FluentOption>
                                <FluentOption Value="America/Argentina/Rio_Gallegos">America/Argentina/Rio_Gallegos</FluentOption>
                                <FluentOption Value="America/Argentina/Ushuaia">America/Argentina/Ushuaia</FluentOption>
                                <FluentOption Value="Pacific/Pago_Pago">Pacific/Pago_Pago</FluentOption>
                                <FluentOption Value="Europe/Vienna">Europe/Vienna</FluentOption>
                                <FluentOption Value="Australia/Lord_Howe">Australia/Lord_Howe</FluentOption>
                                <FluentOption Value="Antarctica/Macquarie">Antarctica/Macquarie</FluentOption>
                                <FluentOption Value="Australia/Hobart">Australia/Hobart</FluentOption>
                                <FluentOption Value="Australia/Melbourne">Australia/Melbourne</FluentOption>
                                <FluentOption Value="Australia/Sydney">Australia/Sydney</FluentOption>
                                <FluentOption Value="Australia/Broken_Hill">Australia/Broken_Hill</FluentOption>
                                <FluentOption Value="Australia/Brisbane">Australia/Brisbane</FluentOption>
                                <FluentOption Value="Australia/Lindeman">Australia/Lindeman</FluentOption>
                                <FluentOption Value="Australia/Adelaide">Australia/Adelaide</FluentOption>
                                <FluentOption Value="Australia/Darwin">Australia/Darwin</FluentOption>
                                <FluentOption Value="Australia/Perth">Australia/Perth</FluentOption>
                                <FluentOption Value="Australia/Eucla">Australia/Eucla</FluentOption>
                                <FluentOption Value="America/Aruba">America/Aruba</FluentOption>
                                <FluentOption Value="Europe/Mariehamn">Europe/Mariehamn</FluentOption>
                                <FluentOption Value="Asia/Baku">Asia/Baku</FluentOption>
                                <FluentOption Value="Europe/Sarajevo">Europe/Sarajevo</FluentOption>
                                <FluentOption Value="America/Barbados">America/Barbados</FluentOption>
                                <FluentOption Value="Asia/Dhaka">Asia/Dhaka</FluentOption>
                                <FluentOption Value="Europe/Brussels">Europe/Brussels</FluentOption>
                                <FluentOption Value="Africa/Ouagadougou">Africa/Ouagadougou</FluentOption>
                                <FluentOption Value="Europe/Sofia">Europe/Sofia</FluentOption>
                                <FluentOption Value="Asia/Bahrain">Asia/Bahrain</FluentOption>
                                <FluentOption Value="Africa/Bujumbura">Africa/Bujumbura</FluentOption>
                                <FluentOption Value="Africa/Porto-Novo">Africa/Porto-Novo</FluentOption>
                                <FluentOption Value="America/St_Barthelemy">America/St_Barthelemy</FluentOption>
                                <FluentOption Value="Atlantic/Bermuda">Atlantic/Bermuda</FluentOption>
                                <FluentOption Value="Asia/Brunei">Asia/Brunei</FluentOption>
                                <FluentOption Value="America/La_Paz">America/La_Paz</FluentOption>
                                <FluentOption Value="America/Kralendijk">America/Kralendijk</FluentOption>
                                <FluentOption Value="America/Noronha">America/Noronha</FluentOption>
                                <FluentOption Value="America/Belem">America/Belem</FluentOption>
                                <FluentOption Value="America/Fortaleza">America/Fortaleza</FluentOption>
                                <FluentOption Value="America/Recife">America/Recife</FluentOption>
                                <FluentOption Value="America/Araguaina">America/Araguaina</FluentOption>
                                <FluentOption Value="America/Maceio">America/Maceio</FluentOption>
                                <FluentOption Value="America/Bahia">America/Bahia</FluentOption>
                                <FluentOption Value="America/Sao_Paulo">America/Sao_Paulo</FluentOption>
                                <FluentOption Value="America/Campo_Grande">America/Campo_Grande</FluentOption>
                                <FluentOption Value="America/Cuiaba">America/Cuiaba</FluentOption>
                                <FluentOption Value="America/Santarem">America/Santarem</FluentOption>
                                <FluentOption Value="America/Porto_Velho">America/Porto_Velho</FluentOption>
                                <FluentOption Value="America/Boa_Vista">America/Boa_Vista</FluentOption>
                                <FluentOption Value="America/Manaus">America/Manaus</FluentOption>
                                <FluentOption Value="America/Eirunepe">America/Eirunepe</FluentOption>
                                <FluentOption Value="America/Rio_Branco">America/Rio_Branco</FluentOption>
                                <FluentOption Value="America/Nassau">America/Nassau</FluentOption>
                                <FluentOption Value="Asia/Thimphu">Asia/Thimphu</FluentOption>
                                <FluentOption Value="Africa/Gaborone">Africa/Gaborone</FluentOption>
                                <FluentOption Value="Europe/Minsk">Europe/Minsk</FluentOption>
                                <FluentOption Value="America/Belize">America/Belize</FluentOption>
                                <FluentOption Value="America/St_Johns">America/St_Johns</FluentOption>
                                <FluentOption Value="America/Halifax">America/Halifax</FluentOption>
                                <FluentOption Value="America/Glace_Bay">America/Glace_Bay</FluentOption>
                                <FluentOption Value="America/Moncton">America/Moncton</FluentOption>
                                <FluentOption Value="America/Goose_Bay">America/Goose_Bay</FluentOption>
                                <FluentOption Value="America/Blanc-Sablon">America/Blanc-Sablon</FluentOption>
                                <FluentOption Value="America/Toronto">America/Toronto</FluentOption>
                                <FluentOption Value="America/Iqaluit">America/Iqaluit</FluentOption>
                                <FluentOption Value="America/Atikokan">America/Atikokan</FluentOption>
                                <FluentOption Value="America/Winnipeg">America/Winnipeg</FluentOption>
                                <FluentOption Value="America/Resolute">America/Resolute</FluentOption>
                                <FluentOption Value="America/Rankin_Inlet">America/Rankin_Inlet</FluentOption>
                                <FluentOption Value="America/Regina">America/Regina</FluentOption>
                                <FluentOption Value="America/Swift_Current">America/Swift_Current</FluentOption>
                                <FluentOption Value="America/Edmonton">America/Edmonton</FluentOption>
                                <FluentOption Value="America/Cambridge_Bay">America/Cambridge_Bay</FluentOption>
                                <FluentOption Value="America/Inuvik">America/Inuvik</FluentOption>
                                <FluentOption Value="America/Creston">America/Creston</FluentOption>
                                <FluentOption Value="America/Dawson_Creek">America/Dawson_Creek</FluentOption>
                                <FluentOption Value="America/Fort_Nelson">America/Fort_Nelson</FluentOption>
                                <FluentOption Value="America/Whitehorse">America/Whitehorse</FluentOption>
                                <FluentOption Value="America/Dawson">America/Dawson</FluentOption>
                                <FluentOption Value="America/Vancouver">America/Vancouver</FluentOption>
                                <FluentOption Value="Indian/Cocos">Indian/Cocos</FluentOption>
                                <FluentOption Value="Africa/Kinshasa">Africa/Kinshasa</FluentOption>
                                <FluentOption Value="Africa/Lubumbashi">Africa/Lubumbashi</FluentOption>
                                <FluentOption Value="Africa/Bangui">Africa/Bangui</FluentOption>
                                <FluentOption Value="Africa/Brazzaville">Africa/Brazzaville</FluentOption>
                                <FluentOption Value="Europe/Zurich">Europe/Zurich</FluentOption>
                                <FluentOption Value="Africa/Abidjan">Africa/Abidjan</FluentOption>
                                <FluentOption Value="Pacific/Rarotonga">Pacific/Rarotonga</FluentOption>
                                <FluentOption Value="America/Santiago">America/Santiago</FluentOption>
                                <FluentOption Value="America/Punta_Arenas">America/Punta_Arenas</FluentOption>
                                <FluentOption Value="Pacific/Easter">Pacific/Easter</FluentOption>
                                <FluentOption Value="Africa/Douala">Africa/Douala</FluentOption>
                                <FluentOption Value="Asia/Shanghai">Asia/Shanghai</FluentOption>
                                <FluentOption Value="Asia/Urumqi">Asia/Urumqi</FluentOption>
                                <FluentOption Value="America/Bogota">America/Bogota</FluentOption>
                                <FluentOption Value="America/Costa_Rica">America/Costa_Rica</FluentOption>
                                <FluentOption Value="America/Havana">America/Havana</FluentOption>
                                <FluentOption Value="Atlantic/Cape_Verde">Atlantic/Cape_Verde</FluentOption>
                                <FluentOption Value="America/Curacao">America/Curacao</FluentOption>
                                <FluentOption Value="Indian/Christmas">Indian/Christmas</FluentOption>
                                <FluentOption Value="Asia/Nicosia">Asia/Nicosia</FluentOption>
                                <FluentOption Value="Asia/Famagusta">Asia/Famagusta</FluentOption>
                                <FluentOption Value="Europe/Prague">Europe/Prague</FluentOption>
                                <FluentOption Value="Europe/Berlin">Europe/Berlin</FluentOption>
                                <FluentOption Value="Europe/Busingen">Europe/Busingen</FluentOption>
                                <FluentOption Value="Africa/Djibouti">Africa/Djibouti</FluentOption>
                                <FluentOption Value="Europe/Copenhagen">Europe/Copenhagen</FluentOption>
                                <FluentOption Value="America/Dominica">America/Dominica</FluentOption>
                                <FluentOption Value="America/Santo_Domingo">America/Santo_Domingo</FluentOption>
                                <FluentOption Value="Africa/Algiers">Africa/Algiers</FluentOption>
                                <FluentOption Value="America/Guayaquil">America/Guayaquil</FluentOption>
                                <FluentOption Value="Pacific/Galapagos">Pacific/Galapagos</FluentOption>
                                <FluentOption Value="Europe/Tallinn">Europe/Tallinn</FluentOption>
                                <FluentOption Value="Africa/Cairo">Africa/Cairo</FluentOption>
                                <FluentOption Value="Africa/El_Aaiun">Africa/El_Aaiun</FluentOption>
                                <FluentOption Value="Africa/Asmara">Africa/Asmara</FluentOption>
                                <FluentOption Value="Europe/Madrid">Europe/Madrid</FluentOption>
                                <FluentOption Value="Africa/Ceuta">Africa/Ceuta</FluentOption>
                                <FluentOption Value="Atlantic/Canary">Atlantic/Canary</FluentOption>
                                <FluentOption Value="Africa/Addis_Ababa">Africa/Addis_Ababa</FluentOption>
                                <FluentOption Value="Europe/Helsinki">Europe/Helsinki</FluentOption>
                                <FluentOption Value="Pacific/Fiji">Pacific/Fiji</FluentOption>
                                <FluentOption Value="Atlantic/Stanley">Atlantic/Stanley</FluentOption>
                                <FluentOption Value="Pacific/Chuuk">Pacific/Chuuk</FluentOption>
                                <FluentOption Value="Pacific/Pohnpei">Pacific/Pohnpei</FluentOption>
                                <FluentOption Value="Pacific/Kosrae">Pacific/Kosrae</FluentOption>
                                <FluentOption Value="Atlantic/Faroe">Atlantic/Faroe</FluentOption>
                                <FluentOption Value="Europe/Paris">Europe/Paris</FluentOption>
                                <FluentOption Value="Africa/Libreville">Africa/Libreville</FluentOption>
                                <FluentOption Value="Europe/London">Europe/London</FluentOption>
                                <FluentOption Value="America/Grenada">America/Grenada</FluentOption>
                                <FluentOption Value="Asia/Tbilisi">Asia/Tbilisi</FluentOption>
                                <FluentOption Value="America/Cayenne">America/Cayenne</FluentOption>
                                <FluentOption Value="Europe/Guernsey">Europe/Guernsey</FluentOption>
                                <FluentOption Value="Africa/Accra">Africa/Accra</FluentOption>
                                <FluentOption Value="Europe/Gibraltar">Europe/Gibraltar</FluentOption>
                                <FluentOption Value="America/Nuuk">America/Nuuk</FluentOption>
                                <FluentOption Value="America/Danmarkshavn">America/Danmarkshavn</FluentOption>
                                <FluentOption Value="America/Scoresbysund">America/Scoresbysund</FluentOption>
                                <FluentOption Value="America/Thule">America/Thule</FluentOption>
                                <FluentOption Value="Africa/Banjul">Africa/Banjul</FluentOption>
                                <FluentOption Value="Africa/Conakry">Africa/Conakry</FluentOption>
                                <FluentOption Value="America/Guadeloupe">America/Guadeloupe</FluentOption>
                                <FluentOption Value="Africa/Malabo">Africa/Malabo</FluentOption>
                                <FluentOption Value="Europe/Athens">Europe/Athens</FluentOption>
                                <FluentOption Value="Atlantic/South_Georgia">Atlantic/South_Georgia</FluentOption>
                                <FluentOption Value="America/Guatemala">America/Guatemala</FluentOption>
                                <FluentOption Value="Pacific/Guam">Pacific/Guam</FluentOption>
                                <FluentOption Value="Africa/Bissau">Africa/Bissau</FluentOption>
                                <FluentOption Value="America/Guyana">America/Guyana</FluentOption>
                                <FluentOption Value="Asia/Hong_Kong">Asia/Hong_Kong</FluentOption>
                                <FluentOption Value="America/Tegucigalpa">America/Tegucigalpa</FluentOption>
                                <FluentOption Value="Europe/Zagreb">Europe/Zagreb</FluentOption>
                                <FluentOption Value="America/Port-au-Prince">America/Port-au-Prince</FluentOption>
                                <FluentOption Value="Europe/Budapest">Europe/Budapest</FluentOption>
                                <FluentOption Value="Asia/Jakarta">Asia/Jakarta</FluentOption>
                                <FluentOption Value="Asia/Pontianak">Asia/Pontianak</FluentOption>
                                <FluentOption Value="Asia/Makassar">Asia/Makassar</FluentOption>
                                <FluentOption Value="Asia/Jayapura">Asia/Jayapura</FluentOption>
                                <FluentOption Value="Europe/Dublin">Europe/Dublin</FluentOption>
                                <FluentOption Value="Asia/Jerusalem">Asia/Jerusalem</FluentOption>
                                <FluentOption Value="Europe/Isle_of_Man">Europe/Isle_of_Man</FluentOption>
                                <FluentOption Value="Asia/Kolkata">Asia/Kolkata</FluentOption>
                                <FluentOption Value="Indian/Chagos">Indian/Chagos</FluentOption>
                                <FluentOption Value="Asia/Baghdad">Asia/Baghdad</FluentOption>
                                <FluentOption Value="Asia/Tehran">Asia/Tehran</FluentOption>
                                <FluentOption Value="Atlantic/Reykjavik">Atlantic/Reykjavik</FluentOption>
                                <FluentOption Value="Europe/Rome">Europe/Rome</FluentOption>
                                <FluentOption Value="Europe/Jersey">Europe/Jersey</FluentOption>
                                <FluentOption Value="America/Jamaica">America/Jamaica</FluentOption>
                                <FluentOption Value="Asia/Amman">Asia/Amman</FluentOption>
                                <FluentOption Value="Asia/Tokyo">Asia/Tokyo</FluentOption>
                                <FluentOption Value="Africa/Nairobi">Africa/Nairobi</FluentOption>
                                <FluentOption Value="Asia/Bishkek">Asia/Bishkek</FluentOption>
                                <FluentOption Value="Asia/Phnom_Penh">Asia/Phnom_Penh</FluentOption>
                                <FluentOption Value="Pacific/Tarawa">Pacific/Tarawa</FluentOption>
                                <FluentOption Value="Pacific/Kanton">Pacific/Kanton</FluentOption>
                                <FluentOption Value="Pacific/Kiritimati">Pacific/Kiritimati</FluentOption>
                                <FluentOption Value="Indian/Comoro">Indian/Comoro</FluentOption>
                                <FluentOption Value="America/St_Kitts">America/St_Kitts</FluentOption>
                                <FluentOption Value="Asia/Pyongyang">Asia/Pyongyang</FluentOption>
                                <FluentOption Value="Asia/Seoul">Asia/Seoul</FluentOption>
                                <FluentOption Value="Asia/Kuwait">Asia/Kuwait</FluentOption>
                                <FluentOption Value="America/Cayman">America/Cayman</FluentOption>
                                <FluentOption Value="Asia/Almaty">Asia/Almaty</FluentOption>
                                <FluentOption Value="Asia/Qyzylorda">Asia/Qyzylorda</FluentOption>
                                <FluentOption Value="Asia/Qostanay">Asia/Qostanay</FluentOption>
                                <FluentOption Value="Asia/Aqtobe">Asia/Aqtobe</FluentOption>
                                <FluentOption Value="Asia/Aqtau">Asia/Aqtau</FluentOption>
                                <FluentOption Value="Asia/Atyrau">Asia/Atyrau</FluentOption>
                                <FluentOption Value="Asia/Oral">Asia/Oral</FluentOption>
                                <FluentOption Value="Asia/Vientiane">Asia/Vientiane</FluentOption>
                                <FluentOption Value="Asia/Beirut">Asia/Beirut</FluentOption>
                                <FluentOption Value="America/St_Lucia">America/St_Lucia</FluentOption>
                                <FluentOption Value="Europe/Vaduz">Europe/Vaduz</FluentOption>
                                <FluentOption Value="Asia/Colombo">Asia/Colombo</FluentOption>
                                <FluentOption Value="Africa/Monrovia">Africa/Monrovia</FluentOption>
                                <FluentOption Value="Africa/Maseru">Africa/Maseru</FluentOption>
                                <FluentOption Value="Europe/Vilnius">Europe/Vilnius</FluentOption>
                                <FluentOption Value="Europe/Luxembourg">Europe/Luxembourg</FluentOption>
                                <FluentOption Value="Europe/Riga">Europe/Riga</FluentOption>
                                <FluentOption Value="Africa/Tripoli">Africa/Tripoli</FluentOption>
                                <FluentOption Value="Africa/Casablanca">Africa/Casablanca</FluentOption>
                                <FluentOption Value="Europe/Monaco">Europe/Monaco</FluentOption>
                                <FluentOption Value="Europe/Chisinau">Europe/Chisinau</FluentOption>
                                <FluentOption Value="Europe/Podgorica">Europe/Podgorica</FluentOption>
                                <FluentOption Value="America/Marigot">America/Marigot</FluentOption>
                                <FluentOption Value="Indian/Antananarivo">Indian/Antananarivo</FluentOption>
                                <FluentOption Value="Pacific/Majuro">Pacific/Majuro</FluentOption>
                                <FluentOption Value="Pacific/Kwajalein">Pacific/Kwajalein</FluentOption>
                                <FluentOption Value="Europe/Skopje">Europe/Skopje</FluentOption>
                                <FluentOption Value="Africa/Bamako">Africa/Bamako</FluentOption>
                                <FluentOption Value="Asia/Yangon">Asia/Yangon</FluentOption>
                                <FluentOption Value="Asia/Ulaanbaatar">Asia/Ulaanbaatar</FluentOption>
                                <FluentOption Value="Asia/Hovd">Asia/Hovd</FluentOption>
                                <FluentOption Value="Asia/Macau">Asia/Macau</FluentOption>
                                <FluentOption Value="Pacific/Saipan">Pacific/Saipan</FluentOption>
                                <FluentOption Value="America/Martinique">America/Martinique</FluentOption>
                                <FluentOption Value="Africa/Nouakchott">Africa/Nouakchott</FluentOption>
                                <FluentOption Value="America/Montserrat">America/Montserrat</FluentOption>
                                <FluentOption Value="Europe/Malta">Europe/Malta</FluentOption>
                                <FluentOption Value="Indian/Mauritius">Indian/Mauritius</FluentOption>
                                <FluentOption Value="Indian/Maldives">Indian/Maldives</FluentOption>
                                <FluentOption Value="Africa/Blantyre">Africa/Blantyre</FluentOption>
                                <FluentOption Value="America/Mexico_City">America/Mexico_City</FluentOption>
                                <FluentOption Value="America/Cancun">America/Cancun</FluentOption>
                                <FluentOption Value="America/Merida">America/Merida</FluentOption>
                                <FluentOption Value="America/Monterrey">America/Monterrey</FluentOption>
                                <FluentOption Value="America/Matamoros">America/Matamoros</FluentOption>
                                <FluentOption Value="America/Chihuahua">America/Chihuahua</FluentOption>
                                <FluentOption Value="America/Ciudad_Juarez">America/Ciudad_Juarez</FluentOption>
                                <FluentOption Value="America/Ojinaga">America/Ojinaga</FluentOption>
                                <FluentOption Value="America/Mazatlan">America/Mazatlan</FluentOption>
                                <FluentOption Value="America/Bahia_Banderas">America/Bahia_Banderas</FluentOption>
                                <FluentOption Value="America/Hermosillo">America/Hermosillo</FluentOption>
                                <FluentOption Value="America/Tijuana">America/Tijuana</FluentOption>
                                <FluentOption Value="Asia/Kuala_Lumpur">Asia/Kuala_Lumpur</FluentOption>
                                <FluentOption Value="Asia/Kuching">Asia/Kuching</FluentOption>
                                <FluentOption Value="Africa/Maputo">Africa/Maputo</FluentOption>
                                <FluentOption Value="Africa/Windhoek">Africa/Windhoek</FluentOption>
                                <FluentOption Value="Pacific/Noumea">Pacific/Noumea</FluentOption>
                                <FluentOption Value="Africa/Niamey">Africa/Niamey</FluentOption>
                                <FluentOption Value="Pacific/Norfolk">Pacific/Norfolk</FluentOption>
                                <FluentOption Value="Africa/Lagos">Africa/Lagos</FluentOption>
                                <FluentOption Value="America/Managua">America/Managua</FluentOption>
                                <FluentOption Value="Europe/Amsterdam">Europe/Amsterdam</FluentOption>
                                <FluentOption Value="Europe/Oslo">Europe/Oslo</FluentOption>
                                <FluentOption Value="Asia/Kathmandu">Asia/Kathmandu</FluentOption>
                                <FluentOption Value="Pacific/Nauru">Pacific/Nauru</FluentOption>
                                <FluentOption Value="Pacific/Niue">Pacific/Niue</FluentOption>
                                <FluentOption Value="Pacific/Auckland">Pacific/Auckland</FluentOption>
                                <FluentOption Value="Pacific/Chatham">Pacific/Chatham</FluentOption>
                                <FluentOption Value="Asia/Muscat">Asia/Muscat</FluentOption>
                                <FluentOption Value="America/Panama">America/Panama</FluentOption>
                                <FluentOption Value="America/Lima">America/Lima</FluentOption>
                                <FluentOption Value="Pacific/Tahiti">Pacific/Tahiti</FluentOption>
                                <FluentOption Value="Pacific/Marquesas">Pacific/Marquesas</FluentOption>
                                <FluentOption Value="Pacific/Gambier">Pacific/Gambier</FluentOption>
                                <FluentOption Value="Pacific/Port_Moresby">Pacific/Port_Moresby</FluentOption>
                                <FluentOption Value="Pacific/Bougainville">Pacific/Bougainville</FluentOption>
                                <FluentOption Value="Asia/Manila">Asia/Manila</FluentOption>
                                <FluentOption Value="Asia/Karachi">Asia/Karachi</FluentOption>
                                <FluentOption Value="Europe/Warsaw">Europe/Warsaw</FluentOption>
                                <FluentOption Value="America/Miquelon">America/Miquelon</FluentOption>
                                <FluentOption Value="Pacific/Pitcairn">Pacific/Pitcairn</FluentOption>
                                <FluentOption Value="America/Puerto_Rico">America/Puerto_Rico</FluentOption>
                                <FluentOption Value="Asia/Gaza">Asia/Gaza</FluentOption>
                                <FluentOption Value="Asia/Hebron">Asia/Hebron</FluentOption>
                                <FluentOption Value="Europe/Lisbon">Europe/Lisbon</FluentOption>
                                <FluentOption Value="Atlantic/Madeira">Atlantic/Madeira</FluentOption>
                                <FluentOption Value="Atlantic/Azores">Atlantic/Azores</FluentOption>
                                <FluentOption Value="Pacific/Palau">Pacific/Palau</FluentOption>
                                <FluentOption Value="America/Asuncion">America/Asuncion</FluentOption>
                                <FluentOption Value="Asia/Qatar">Asia/Qatar</FluentOption>
                                <FluentOption Value="Indian/Reunion">Indian/Reunion</FluentOption>
                                <FluentOption Value="Europe/Bucharest">Europe/Bucharest</FluentOption>
                                <FluentOption Value="Europe/Belgrade">Europe/Belgrade</FluentOption>
                                <FluentOption Value="Europe/Kaliningrad">Europe/Kaliningrad</FluentOption>
                                <FluentOption Value="Europe/Moscow">Europe/Moscow</FluentOption>
                                <FluentOption Value="Europe/Simferopol">Europe/Simferopol</FluentOption>
                                <FluentOption Value="Europe/Kirov">Europe/Kirov</FluentOption>
                                <FluentOption Value="Europe/Volgograd">Europe/Volgograd</FluentOption>
                                <FluentOption Value="Europe/Astrakhan">Europe/Astrakhan</FluentOption>
                                <FluentOption Value="Europe/Saratov">Europe/Saratov</FluentOption>
                                <FluentOption Value="Europe/Ulyanovsk">Europe/Ulyanovsk</FluentOption>
                                <FluentOption Value="Europe/Samara">Europe/Samara</FluentOption>
                                <FluentOption Value="Asia/Yekaterinburg">Asia/Yekaterinburg</FluentOption>
                                <FluentOption Value="Asia/Omsk">Asia/Omsk</FluentOption>
                                <FluentOption Value="Asia/Novosibirsk">Asia/Novosibirsk</FluentOption>
                                <FluentOption Value="Asia/Barnaul">Asia/Barnaul</FluentOption>
                                <FluentOption Value="Asia/Tomsk">Asia/Tomsk</FluentOption>
                                <FluentOption Value="Asia/Novokuznetsk">Asia/Novokuznetsk</FluentOption>
                                <FluentOption Value="Asia/Krasnoyarsk">Asia/Krasnoyarsk</FluentOption>
                                <FluentOption Value="Asia/Irkutsk">Asia/Irkutsk</FluentOption>
                                <FluentOption Value="Asia/Chita">Asia/Chita</FluentOption>
                                <FluentOption Value="Asia/Yakutsk">Asia/Yakutsk</FluentOption>
                                <FluentOption Value="Asia/Khandyga">Asia/Khandyga</FluentOption>
                                <FluentOption Value="Asia/Vladivostok">Asia/Vladivostok</FluentOption>
                                <FluentOption Value="Asia/Ust-Nera">Asia/Ust-Nera</FluentOption>
                                <FluentOption Value="Asia/Magadan">Asia/Magadan</FluentOption>
                                <FluentOption Value="Asia/Sakhalin">Asia/Sakhalin</FluentOption>
                                <FluentOption Value="Asia/Srednekolymsk">Asia/Srednekolymsk</FluentOption>
                                <FluentOption Value="Asia/Kamchatka">Asia/Kamchatka</FluentOption>
                                <FluentOption Value="Asia/Anadyr">Asia/Anadyr</FluentOption>
                                <FluentOption Value="Africa/Kigali">Africa/Kigali</FluentOption>
                                <FluentOption Value="Asia/Riyadh">Asia/Riyadh</FluentOption>
                                <FluentOption Value="Pacific/Guadalcanal">Pacific/Guadalcanal</FluentOption>
                                <FluentOption Value="Indian/Mahe">Indian/Mahe</FluentOption>
                                <FluentOption Value="Africa/Khartoum">Africa/Khartoum</FluentOption>
                                <FluentOption Value="Europe/Stockholm">Europe/Stockholm</FluentOption>
                                <FluentOption Value="Asia/Singapore">Asia/Singapore</FluentOption>
                                <FluentOption Value="Atlantic/St_Helena">Atlantic/St_Helena</FluentOption>
                                <FluentOption Value="Europe/Ljubljana">Europe/Ljubljana</FluentOption>
                                <FluentOption Value="Arctic/Longyearbyen">Arctic/Longyearbyen</FluentOption>
                                <FluentOption Value="Europe/Bratislava">Europe/Bratislava</FluentOption>
                                <FluentOption Value="Africa/Freetown">Africa/Freetown</FluentOption>
                                <FluentOption Value="Europe/San_Marino">Europe/San_Marino</FluentOption>
                                <FluentOption Value="Africa/Dakar">Africa/Dakar</FluentOption>
                                <FluentOption Value="Africa/Mogadishu">Africa/Mogadishu</FluentOption>
                                <FluentOption Value="America/Paramaribo">America/Paramaribo</FluentOption>
                                <FluentOption Value="Africa/Juba">Africa/Juba</FluentOption>
                                <FluentOption Value="Africa/Sao_Tome">Africa/Sao_Tome</FluentOption>
                                <FluentOption Value="America/El_Salvador">America/El_Salvador</FluentOption>
                                <FluentOption Value="America/Lower_Princes">America/Lower_Princes</FluentOption>
                                <FluentOption Value="Asia/Damascus">Asia/Damascus</FluentOption>
                                <FluentOption Value="Africa/Mbabane">Africa/Mbabane</FluentOption>
                                <FluentOption Value="America/Grand_Turk">America/Grand_Turk</FluentOption>
                                <FluentOption Value="Africa/Ndjamena">Africa/Ndjamena</FluentOption>
                                <FluentOption Value="Indian/Kerguelen">Indian/Kerguelen</FluentOption>
                                <FluentOption Value="Africa/Lome">Africa/Lome</FluentOption>
                                <FluentOption Value="Asia/Bangkok">Asia/Bangkok</FluentOption>
                                <FluentOption Value="Asia/Dushanbe">Asia/Dushanbe</FluentOption>
                                <FluentOption Value="Pacific/Fakaofo">Pacific/Fakaofo</FluentOption>
                                <FluentOption Value="Asia/Dili">Asia/Dili</FluentOption>
                                <FluentOption Value="Asia/Ashgabat">Asia/Ashgabat</FluentOption>
                                <FluentOption Value="Africa/Tunis">Africa/Tunis</FluentOption>
                                <FluentOption Value="Pacific/Tongatapu">Pacific/Tongatapu</FluentOption>
                                <FluentOption Value="Europe/Istanbul">Europe/Istanbul</FluentOption>
                                <FluentOption Value="America/Port_of_Spain">America/Port_of_Spain</FluentOption>
                                <FluentOption Value="Pacific/Funafuti">Pacific/Funafuti</FluentOption>
                                <FluentOption Value="Asia/Taipei">Asia/Taipei</FluentOption>
                                <FluentOption Value="Africa/Dar_es_Salaam">Africa/Dar_es_Salaam</FluentOption>
                                <FluentOption Value="Europe/Kyiv">Europe/Kyiv</FluentOption>
                                <FluentOption Value="Africa/Kampala">Africa/Kampala</FluentOption>
                                <FluentOption Value="Pacific/Midway">Pacific/Midway</FluentOption>
                                <FluentOption Value="Pacific/Wake">Pacific/Wake</FluentOption>
                                <FluentOption Value="America/New_York">America/New_York</FluentOption>
                                <FluentOption Value="America/Detroit">America/Detroit</FluentOption>
                                <FluentOption Value="America/Kentucky/Louisville">America/Kentucky/Louisville</FluentOption>
                                <FluentOption Value="America/Kentucky/Monticello">America/Kentucky/Monticello</FluentOption>
                                <FluentOption Value="America/Indiana/Indianapolis">America/Indiana/Indianapolis</FluentOption>
                                <FluentOption Value="America/Indiana/Vincennes">America/Indiana/Vincennes</FluentOption>
                                <FluentOption Value="America/Indiana/Winamac">America/Indiana/Winamac</FluentOption>
                                <FluentOption Value="America/Indiana/Marengo">America/Indiana/Marengo</FluentOption>
                                <FluentOption Value="America/Indiana/Petersburg">America/Indiana/Petersburg</FluentOption>
                                <FluentOption Value="America/Indiana/Vevay">America/Indiana/Vevay</FluentOption>
                                <FluentOption Value="America/Chicago">America/Chicago</FluentOption>
                                <FluentOption Value="America/Indiana/Tell_City">America/Indiana/Tell_City</FluentOption>
                                <FluentOption Value="America/Indiana/Knox">America/Indiana/Knox</FluentOption>
                                <FluentOption Value="America/Menominee">America/Menominee</FluentOption>
                                <FluentOption Value="America/North_Dakota/Center">America/North_Dakota/Center</FluentOption>
                                <FluentOption Value="America/North_Dakota/New_Salem">America/North_Dakota/New_Salem</FluentOption>
                                <FluentOption Value="America/North_Dakota/Beulah">America/North_Dakota/Beulah</FluentOption>
                                <FluentOption Value="America/Denver">America/Denver</FluentOption>
                                <FluentOption Value="America/Boise">America/Boise</FluentOption>
                                <FluentOption Value="America/Phoenix">America/Phoenix</FluentOption>
                                <FluentOption Value="America/Los_Angeles">America/Los_Angeles</FluentOption>
                                <FluentOption Value="America/Anchorage">America/Anchorage</FluentOption>
                                <FluentOption Value="America/Juneau">America/Juneau</FluentOption>
                                <FluentOption Value="America/Sitka">America/Sitka</FluentOption>
                                <FluentOption Value="America/Metlakatla">America/Metlakatla</FluentOption>
                                <FluentOption Value="America/Yakutat">America/Yakutat</FluentOption>
                                <FluentOption Value="America/Nome">America/Nome</FluentOption>
                                <FluentOption Value="America/Adak">America/Adak</FluentOption>
                                <FluentOption Value="Pacific/Honolulu">Pacific/Honolulu</FluentOption>
                                <FluentOption Value="America/Montevideo">America/Montevideo</FluentOption>
                                <FluentOption Value="Asia/Samarkand">Asia/Samarkand</FluentOption>
                                <FluentOption Value="Asia/Tashkent">Asia/Tashkent</FluentOption>
                                <FluentOption Value="Europe/Vatican">Europe/Vatican</FluentOption>
                                <FluentOption Value="America/St_Vincent">America/St_Vincent</FluentOption>
                                <FluentOption Value="America/Caracas">America/Caracas</FluentOption>
                                <FluentOption Value="America/Tortola">America/Tortola</FluentOption>
                                <FluentOption Value="America/St_Thomas">America/St_Thomas</FluentOption>
                                <FluentOption Value="Asia/Ho_Chi_Minh">Asia/Ho_Chi_Minh</FluentOption>
                                <FluentOption Value="Pacific/Efate">Pacific/Efate</FluentOption>
                                <FluentOption Value="Pacific/Wallis">Pacific/Wallis</FluentOption>
                                <FluentOption Value="Pacific/Apia">Pacific/Apia</FluentOption>
                                <FluentOption Value="Asia/Aden">Asia/Aden</FluentOption>
                                <FluentOption Value="Indian/Mayotte">Indian/Mayotte</FluentOption>
                                <FluentOption Value="Africa/Johannesburg">Africa/Johannesburg</FluentOption>
                                <FluentOption Value="Africa/Lusaka">Africa/Lusaka</FluentOption>
                                <FluentOption Value="Africa/Harare">Africa/Harare</FluentOption> *@
                            </FluentCombobox>
                            <FluentValidationMessage For="@(() => this.Content!.TimeZone)" />
                            <div class="h-4" />
                            <FluentSelect @ref="@dateFormatSelect" @bind-Value="@this.Content!.DateFormat" Immediate="true" Label="@Resources.SettingsDialogResource.DateFormat" Class="max-w-96" Height="230px" TOption="string">
                                <FluentOption Value="dd/MM/yyyy">@("23/03/" + DateTime.Now.Year.ToString())</FluentOption>
                                <FluentOption Value="MM/dd/yyyy">@("03/23/" + DateTime.Now.Year.ToString())</FluentOption>
                                <FluentOption Value="d/M/yyyy">@("9/3/" + DateTime.Now.Year.ToString())</FluentOption>
                                <FluentOption Value="M/d/yyyy">@("3/9/" + DateTime.Now.Year.ToString())</FluentOption>
                                <FluentOption Value="yyyy-MM-dd">@(DateTime.Now.Year.ToString() + "-03-23")</FluentOption>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.DateFormat)" />
                            <div class="h-4" />

                            <FluentSelect @ref="@timeFormatSelect" @bind-Value="@this.Content!.TimeFormat" Immediate="true" Label="@Resources.SettingsDialogResource.TimeFormat" Class="max-w-96" Height="230px" TOption="string">
                                <FluentOption Value="HH:mm">01:30 - 23:59</FluentOption>
                                <FluentOption Value="h:mm tt">@("1:30 " + this.Content!.Language.GetCultureInfo().DateTimeFormat.AMDesignator + " - 11:59 " + this.Content!.Language.GetCultureInfo().DateTimeFormat.PMDesignator)</FluentOption>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.DateFormat)" />

                            <div class="h-8" />

                            <FluentLabel Typo="Typography.H5"> @Resources.SettingsDialogResource.AppearanceTitle </FluentLabel>
                            <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                            <div class="h-3" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.Theme" Class="max-w-96" @ref="colorModeSelect" Items="@(Enum.GetValues<ThemeColorMode>())" TOption="ThemeColorMode" SelectedOption="@this.currentThemeColorMode" SelectedOptionChanged="@OnThemeModeChanged" />
                            <div class="h-4" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.Color" Class="max-w-96" Height="200px" Items="@(Enum.GetValues<ThemeColor>().Select(i => (ThemeColor?)i))" TOption="ThemeColor?" SelectedOption="@this.currentThemeColor" SelectedOptionChanged="@OnThemeColorChanged">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentIcon Value="@(new Icons.Filled.Size20.RectangleLandscape())" Color="Color.Custom" CustomColor="@context!.GetEnumDescription()" />
                                        <FluentLabel>@context</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.ThemeColor)" />
                            <div class="h-8" />
                            <div>
                                @(GlobalResource.Version + ": " + appVersionHelper.CurrentVersion)
                            </div>
                        }
                        else if (this.activeTab == "Contacts")
                        {
                            <div class="hidden md:block lg:block xl:block">
                                <FluentLabel Typo="Typography.H4" Class="xs:hidden sm:hidden">@Resources.SettingsDialogResource.Contacts</FluentLabel>
                            </div>
                            <div class="h-4" />
                            <FluentLabel Typo="Typography.H5">@Resources.SettingsDialogResource.LanguageTimeTitle</FluentLabel>
                            <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                            <div class="h-3" />
                            @* <FluentSelect @bind-Value="@this.Content.FullNameDisplay" TOption="Enum">
                                <FluentOption Value=@FluentBlue.Data.Model.FullNameDisplay.FirstLast.>
                                    @FluentBlue.Data.Model.Resources.Contacts.ContactResource.FirstLast
                                </FluentOption>
                                <FluentOption Value=@FluentBlue.Data.Model.FullNameDisplay.LastFirst>
                                    @FluentBlue.Data.Model.Resources.Contacts.ContactResource.LastFirst
                                </FluentOption>
                            </FluentSelect> *@
                            <FluentSelect Label="@FluentBlue.Data.Model.Resources.Settings.GeneralSettingResource.FullNameDisplay" Items="@(Enum.GetValues<FullNameDisplay>())" TOption="FullNameDisplay" @bind-SelectedOption="@this.Content.FullNameDisplay" Class="max-w-96">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@(context == FullNameDisplay.FirstLast ? FluentBlue.Data.Model.Resources.Contacts.ContactResource.FirstLast : FluentBlue.Data.Model.Resources.Contacts.ContactResource.LastFirst)</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>

                            <div class="h-3" />
                            <FluentSwitch @bind-Value=this.Content!.ShowTinInContactLists Label="@Resources.SettingsDialogResource.ShowTinInContactLists"></FluentSwitch>
                            <div class="h-4" />
                            <FluentSwitch @bind-Value=this.Content!.ShowSsnInContactLists Label="@Resources.SettingsDialogResource.ShowSsnInContactLists"></FluentSwitch>
                            <div class="h-8" />
                            @if (this.userRole.CanEditApplicationSettings)
                            {
                                <FluentLabel Typo="Typography.H5">@Resources.SettingsDialogResource.ContactCategoriesTitle</FluentLabel>
                                <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                                <div class="h-3" />
                                <FluentButton OnClick="OpenContactCategoriesDialog">@Resources.SettingsDialogResource.ContactCategoriesBtn_Text</FluentButton>
                                <div class="h-8" />
                            }

                        }
                        else if (this.activeTab == "Calendar")
                        {
                            <div class="hidden md:block lg:block xl:block">
                                <FluentLabel Typo="Typography.H4">@Resources.SettingsDialogResource.Calendar</FluentLabel>
                            </div>
                            <div class="h-4" />
                            <FluentLabel Typo="Typography.H5">@Resources.SettingsDialogResource.AppearanceTitle</FluentLabel>
                            <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                            <div class="h-3" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.FirstDayOfWeek" Items="@(Enum.GetValues<DayOfWeek>())" TOption="DayOfWeek" SelectedOption="@this.firstDayOfWeek" SelectedOptionChanged="@OnFirstDayOfWeekChanged" Width="150px">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@context</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.FirstDayOfWeek)" />
                            <div class="h-4" />
                            <FluentLabel Class="mb-1">@Resources.SettingsDialogResource.CalendarWorkDays</FluentLabel>
                            <FluentStack HorizontalAlignment="HorizontalAlignment.Left" Wrap="true">
                                <FluentCheckbox @bind-Value="mondayChecked" Label="@Resources.SettingsDialogResource.Monday.Substring(0,3)" />
                                <FluentCheckbox @bind-Value="tuesdayChecked" Label="@Resources.SettingsDialogResource.Tuesday.Substring(0,3)" />
                                <FluentCheckbox @bind-Value="wednesdayChecked" Label="@Resources.SettingsDialogResource.Wednesday.Substring(0,3)" />
                                <FluentCheckbox @bind-Value="thursdayChecked" Label="@Resources.SettingsDialogResource.Thursday.Substring(0,3)" />
                                <FluentCheckbox @bind-Value="fridayChecked" Label="@Resources.SettingsDialogResource.Friday.Substring(0,3)" />
                                <FluentCheckbox @bind-Value="saturdayChecked" Label="@Resources.SettingsDialogResource.Saturday.Substring(0,3)" />
                                <FluentCheckbox @bind-Value="sundayChecked" Label="@Resources.SettingsDialogResource.Sunday.Substring(0,3)" />
                            </FluentStack>
                            <div class="h-4" />
                            <FluentLabel Class="mb-1">@Resources.SettingsDialogResource.CalendarWorkHours</FluentLabel>
                            <FluentStack HorizontalAlignment="HorizontalAlignment.Left" VerticalAlignment="VerticalAlignment.Center">
                                <FluentTimePicker Format="@(this.Content.TimeFormat ?? "")" @bind-Value="this.Content!.CalendarWorkStartDateTime" Label="@Resources.SettingsDialogResource.StartHour" />
                                <FluentValidationMessage For="@(() => Content!.CalendarWorkStartDateTime)" />
                                <FluentTimePicker Format="@(this.Content.TimeFormat ?? "")" @bind-Value="this.Content!.CalendarWorkEndDateTime" Label="@Resources.SettingsDialogResource.EndHour" />
                                <FluentValidationMessage For="@(() => Content!.CalendarWorkEndDateTime)" />
                            </FluentStack>
                            <div class="h-4" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.CalendarTimeScaleInterval" Items="@(Enum.GetValues<TimeScaleInterval>())" TOption="TimeScaleInterval" SelectedOption="@this.Content!.CalendarTimeScaleInterval" SelectedOptionChanged="@OnCalendarTimeScaleIntervalChanged" Width="150px">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@context.GetDisplayText()</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.CalendarTimeScaleInterval)" />
                            <div class="h-4" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.CalendarDefaultView" Items="@(Enum.GetValues<CalendarView>())" TOption="CalendarView" SelectedOption="@this.Content!.CalendarDefaultView" SelectedOptionChanged="@OnCalendarDefaultViewChanged" Width="150px">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@context.GetDisplayText()</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.CalendarDefaultView)" />
                            <div class="h-4" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.CalendarDefaultViewOnMobiles" Items="@(Enum.GetValues<CalendarView>())" TOption="CalendarView" SelectedOption="@this.Content!.CalendarDefaultViewOnMobiles" SelectedOptionChanged="@OnCalendarDefaultViewOnMobilesChanged" Width="150px">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@context.GetDisplayText()</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.CalendarDefaultViewOnMobiles)" />
                            <div class="h-4" />
                            <FluentSelect id="eventTextDisplaySelect" Label="@Resources.SettingsDialogResource.EventTextDisplay" Items="@(Enum.GetValues<EventTextDisplay>())" TOption="EventTextDisplay" SelectedOption="@this.Content!.EventTextDisplay" SelectedOptionChanged="@OnEventTextDisplayChanged" Width="250px">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@context.GetDisplayText()</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.EventTextSeparator)" />
                            <div class="h-4" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.EventTextSeparator" Items="@(Enum.GetValues<EventTextSeparator>())" TOption="EventTextSeparator" SelectedOption="@this.Content!.EventTextSeparator" SelectedOptionChanged="@OnEventTextSeparatorChanged" Width="250px">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@(context.GetDisplayText())</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.EventTextDisplay)" />
                            <div class="h-3" />
                            <FluentLabel>@(Resources.SettingsDialogResource.EventTextDisplaySampleTitle + " " + this.eventTextDisplaySample)</FluentLabel>
                            <div class="h-8" />

                            <FluentLabel Typo="Typography.H5">@Resources.SettingsDialogResource.BehaviourTitle</FluentLabel>
                            <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                            <div class="h-3" />
                            <FluentSelect Label="@Resources.SettingsDialogResource.CalendarScrollToTime" Items="@(Enum.GetValues<CalendarScrollToTime>())" TOption="CalendarScrollToTime" SelectedOption="@this.Content!.CalendarScrollToTime" SelectedOptionChanged="@OnCalendarScrollToTimeChanged" Width="150px" Height="200px">
                                <OptionTemplate>
                                    <FluentStack>
                                        <FluentLabel>@context.GetDisplayText()</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>
                            </FluentSelect>
                            <FluentValidationMessage For="@(() => this.Content!.CalendarScrollToTime)" />
                            <div class="h-8" />

                            @if (this.userRole.CanEditApplicationSettings)
                            {
                                <FluentLabel Typo="Typography.H5">@Resources.SettingsDialogResource.EventCategoriesTitle</FluentLabel>
                                <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                                <div class="h-3" />
                                <FluentButton OnClick="OpenEventCategoriesDialog">@Resources.SettingsDialogResource.EventCategoriesBtn_Text</FluentButton>
                                <div class="h-8" />

                                <FluentLabel Typo="Typography.H5">@Resources.SettingsDialogResource.EventStatesTitle</FluentLabel>
                                <FluentDivider Class="w-full mt-1" Role="DividerRole.Presentation"></FluentDivider>
                                <div class="h-3" />
                                <FluentButton OnClick="OpenEventStatesDialog">@Resources.SettingsDialogResource.EventStatesBtn_Text</FluentButton>
                                <div class="h-8sql" />
                            }
                        }
                        else if (this.activeTab == "UsersRoles")
                        {
                            <div class="hidden md:block lg:block xl:block">
                                <FluentLabel Typo="Typography.H4" Class="xs:hidden sm:hidden">@Resources.SettingsDialogResource.UsersRoles</FluentLabel>
                            </div>
                            <FluentStack Class="w-full h-full max-h-full">
                                <FluentTabs Orientation="Orientation.Horizontal" Size="TabSize.Small" Class="w-full h-full max-h-full">
                                    <FluentTab Label="@Resources.SettingsDialogResource.Users" Id="usersTab" Visible="@(this.userRole?.CanAdministerUsers ?? false)" Icon="@(new Icons.Regular.Size20.People())">
                                        <div class="px-3 pt-3">
                                            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                                                <FluentButton IconStart="@(new Icons.Regular.Size20.Person())" Appearance="Appearance.Accent" OnClick="NewUserBtnOnClick"><span class="">@Resources.SettingsDialogResource.NewUserBtn_Text</span></FluentButton>
                                                <FluentSearch slot="end" @ref="searchUsersTxtBox" @onkeyup="SearchUsers_OnKeyUp" Class="w-48 xs:w-24 sm:w-24 hover:w-48" ValueChanged="SearchUsers_ValueChanged" AutoComplete="off"></FluentSearch>
                                            </FluentToolbar>
                                        </div>

                                        <div class="xs:hidden sm:hidden max-h-full h-full">
                                            <FluentDataGrid Items="@this.users.AsQueryable()" ResizableColumns=true class="w-full max-h-full h-full" ItemSize="30" TGridItem="Data.Model.DTOs.UserView" OnRowDoubleClick="UsersDataGridOnRowDoubleClick">
                                                @* <TemplateColumn Align="@Align.Center" Class="p-0 h-10">
                                                    <FluentPersona ImageSize="30px" Initials="@context.Initials"></FluentPersona>
                                                </TemplateColumn> *@
                                                <PropertyColumn Property="@(x => x.FullName)" Sortable="true" Tooltip="true" Class="truncate h-10" Width="1fr" Style="vertical-align:middle" />
                                                <PropertyColumn Property="@(x => x.RoleName)" Sortable="true" Tooltip="true" Class="h-10" Width="1fr" />
                                                <PropertyColumn Property="@(x => x.Email)" Sortable="true" Tooltip="true" Class="h-10" Width="1fr" />
                                                <TemplateColumn Align="@Align.Center" Class="p-0 h-10" Width="90px">
                                                    <div class="w-fit">
                                                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnEditUser(context.UserId))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                                                        <FluentButton Appearance="Appearance.Stealth" Disabled=@(context.UserId == AuthenticatedUserData.UserId) @onclick="@(() => OnDeleteUser(context.UserId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton>
                                                    </div>
                                                </TemplateColumn>
                                            </FluentDataGrid>
                                        </div>

                                        <div class="md:hidden lg:hidden xl:hidden">
                                            <FluentDataGrid Items="@this.users.AsQueryable()" ResizableColumns=false Class="w-full" ItemSize="30" TGridItem="Data.Model.DTOs.UserView" OnRowClick="UsersDataGridOnRowClick">
                                                @* <TemplateColumn Align="@Align.Center" Class="p-1 h-14 content-center">
                                                    <FluentPersona ImageSize="30px" Initials="@context.Initials"></FluentPersona>
                                                </TemplateColumn> *@
                                                <TemplateColumn Class="h-14" Width="1fr">
                                                    <FluentLabel><b>@Resources.SettingsDialogResource.UserFullName: </b>@context.FullName</FluentLabel>
                                                    <FluentLabel><b>@Resources.SettingsDialogResource.UserEmail: </b>@context.Email</FluentLabel>
                                                </TemplateColumn>
                                                <TemplateColumn Align="@Align.Center" Class="p-0 h-14" Width="60px">
                                                    <div class="w-fit">
                                                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnEditUser(context.UserId))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                                                        @* <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnDeleteUser(context.UserId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton> *@
                                                    </div>
                                                </TemplateColumn>
                                            </FluentDataGrid>
                                        </div>

                                        <div id="pageBottom" class="px-3 pb-3">
                                            <FluentPaginator State="@usersPagination" CurrentPageIndexChanged="@UsersPagination_CurrentPageIndexChanged" Class="mx-4" />
                                        </div>


                                        <FluentOverlay @bind-Visible=@loadingUsers FullScreen="false" Transparent="true" Dismissable="false" Opacity="0" Alignment="Align.Center" Justification="JustifyContent.Center">
                                            <FluentProgressRing />
                                        </FluentOverlay>


                                    </FluentTab>
                                    <FluentTab Label="@Resources.SettingsDialogResource.Roles" Id="rolesTab" Visible="@(this.userRole?.CanAdministerRoles ?? false)" Icon="@(new Icons.Regular.Size20.ShieldKeyhole())">

                                        <div class="px-3 pt-3">
                                            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                                                <FluentButton IconStart="@(new Icons.Regular.Size20.ShieldKeyhole())" Appearance="Appearance.Accent" OnClick="NewRoleBtnOnClick"><span class="">@Resources.SettingsDialogResource.NewRoleBtn_Text</span></FluentButton>
                                                <FluentSearch slot="end" @ref="searchRolesTxtBox" @onkeyup="SearchRoles_OnKeyUp" Class="w-48 xs:w-24 sm:w-24 hover:w-48" ValueChanged="SearchRoles_ValueChanged" AutoComplete="off"></FluentSearch>
                                            </FluentToolbar>
                                        </div>

                                        <div class="xs:hidden sm:hidden">
                                            <FluentDataGrid @ref="@rolesDataGrid" Items="@this.roles.AsQueryable()" ResizableColumns=true Class="w-full" ItemSize="30" TGridItem="Data.Model.DBOs.Tenants.Role" OnCellClick="RolesDataGridOnCellClick" OnRowDoubleClick="RolesDataGridOnRowDoubleClick">
                                                <PropertyColumn Property="@(x => x.Name)" Sortable="true" IsDefaultSortColumn="true" InitialSortDirection="SortDirection.Ascending" Tooltip="true" Class="truncate h-10" Width="1fr" Style="vertical-align:middle" />
                                                <TemplateColumn Align="@Align.Center" Class="p-0 h-10" Width="90px">
                                                    <div class="w-fit">
                                                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnEditRole(context.RoleId))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                                                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnDeleteRole(context.RoleId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton>
                                                    </div>
                                                </TemplateColumn>
                                            </FluentDataGrid>
                                        </div>

                                        <div class="md:hidden lg:hidden xl:hidden">
                                            <FluentDataGrid Items="@this.roles.AsQueryable()" ResizableColumns=false Class="w-full" ItemSize="30" TGridItem="Data.Model.DBOs.Tenants.Role" OnRowClick="RolesDataGridOnRowClick" OnRowDoubleClick="RolesDataGridOnRowDoubleClick">
                                                <TemplateColumn Class="h-14" Width="1fr">
                                                    <FluentLabel><b>@Resources.SettingsDialogResource.RoleName: </b>@context.Name</FluentLabel>
                                                </TemplateColumn>
                                                <TemplateColumn Align="@Align.Center" Class="p-0 h-14" Width="60px">
                                                    <div class="w-fit">
                                                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnEditRole(context.RoleId))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                                                        @* <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnDeleteRole(context.RoleId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton> *@
                                                    </div>
                                                </TemplateColumn>
                                            </FluentDataGrid>
                                        </div>

                                        <div id="pageBottom" class="px-3 pb-3">
                                            <FluentPaginator State="@rolesPagination" CurrentPageIndexChanged="@RolesPagination_CurrentPageIndexChanged" Class="mx-4" />
                                        </div>

                                        <FluentOverlay @bind-Visible=@loadingRoles FullScreen="false" Transparent="true" Dismissable="false" Opacity="0" Alignment="Align.Center" Justification="JustifyContent.Center">
                                            <FluentProgressRing />
                                        </FluentOverlay>
                                    </FluentTab>
                                </FluentTabs>
                            </FluentStack>
                        }
                    </div>
                </FluentStack>
            </ChildContent>
        </EditForm>


    </FluentStack>
</FluentDialogBody>

<FluentDialogFooter>
    <FluentStack Orientation="Orientation.Vertical" VerticalAlignment="VerticalAlignment.Bottom">
        <FluentDivider Class="w-full" Role="DividerRole.Presentation"></FluentDivider>
        <FluentStack Orientation="Orientation.Horizontal" HorizontalAlignment="HorizontalAlignment.Right" VerticalAlignment="VerticalAlignment.Bottom" Class="self-end">
            <FluentButton Loading="@this.isSaving" IconStart="@(new Icons.Regular.Size16.Save())" Appearance="Appearance.Accent" OnClick="SaveBtnOnClick"><span>@GlobalResource.Save</span></FluentButton>
            <FluentButton IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtnOnClick"><span>@GlobalResource.Cancel</span></FluentButton>
        </FluentStack>
    </FluentStack>
</FluentDialogFooter>
