﻿using Blazored.LocalStorage;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared.Utilities;
using FluentBlue.Ui.Devices;
using FluentBlue.UI.Main;
using FluentBlue.UI.Main.Auth;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using Syncfusion.Blazor;
using System.Globalization;
using System.Net.Http.Headers;
using System.Reflection;
using FluentBlue.Data.Model;
using Microsoft.FluentUI.AspNetCore.Components.Components.Tooltip;
using Serilog;
using Serilog.Events;
using TimeZone = FluentBlue.Shared.Utilities.TimeZone;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices;
using System.Threading;
using System.Collections.Generic;
using NodaTime;
using NodaTime.TimeZones;
using Microsoft.Extensions.Caching.Hybrid;
using FluentBlue.UI.Main.Services;
using FluentBlue.UI.Devices.Services;
using Microsoft.Extensions.Localization;


namespace FluentBlue.UI.Devices
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            // Get the assembly version
            string assemblyVersion = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";

            try
            {
                // Configure Serilog with Sentry Sink
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Information()  // Set the minimum log level for capturing logs
                    .WriteTo.Console()  // Optional: Log to the console (for debugging purposes)
                    .WriteTo.Sentry(o =>
                    {
                        o.Dsn = "https://<EMAIL>/4509101428572240";
                        o.MinimumEventLevel = LogEventLevel.Error;  // Log only errors and above to Sentry
                        o.AttachStacktrace = true;  // Attach stack trace to events
                        o.Debug = true;  // Enable debug mode to see if events are being sent
                        o.SendDefaultPii = true;  // Send personal identifiable information
#if DEBUG
                        o.Environment = "Development";
                        o.TracesSampleRate = 1.0f; // Adjust the sample rate as needed
#else
                        o.Environment = "Production";
                        o.TracesSampleRate = 0.1f; // Adjust the sample rate as needed
#endif
                        o.Release = assemblyVersion;  // Set assembly version
                    })
                    .Enrich.WithProperty("Project", "FluentBlue.UI.Devices")  // Add a static custom property
                                                                              //.Enrich.WithProperty("TenantId", "DefaultTenant")  // Static TenantId
                                                                              //.Enrich.WithProperty("Environment", "Production")  // Static environment info
                                                                              //.Enrich.WithProperty("Device", "Mobile")  // Example of device property
                    .CreateLogger();

                var builder = MauiApp.CreateBuilder();
                builder
                    .UseMauiApp<App>()
                    .ConfigureFonts(fonts =>
                    {
                        fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    });

                // Properly configure logging
                builder.Services.AddLogging(loggingBuilder =>
                {
                    loggingBuilder.ClearProviders();
                    loggingBuilder.AddSerilog(dispose: true);
                });

                #region Setup configuration
                //Το αρχείο appsettings.<Environment>.json από το FluentBlue.UI.Devices ανάλογα με το Environment ή το Debug/Release. 
#if DEBUG
                string appsettingsEnvironmentFile = "FluentBlue.UI.Devices.appsettings.Development.json";
#elif RELEASE
                string appsettingsEnvironmentFile = "FluentBlue.UI.Devices.appsettings.Production.json";
#endif
                var a = Assembly.GetExecutingAssembly();
                using var commonAppsettingsStream = a.GetManifestResourceStream("FluentBlue.UI.Devices.appsettings.json");  //Διαβάζουμε τα appsettings.json από το FluentBlue.UI.Devices με αυτό το τρόπο γιατί στο wwwroot δε δουλεύει.
                using var environmentAppsettingsStream = a.GetManifestResourceStream(appsettingsEnvironmentFile);  //Διαβάζουμε τα appsettings.json ανάλογα με το Environment ή το Debug/Release.
                var config = new ConfigurationBuilder().AddJsonStream(commonAppsettingsStream!).AddJsonStream(environmentAppsettingsStream!).Build();
                builder.Configuration.AddConfiguration(config);
                #endregion

                builder.Services.AddLogging();
                builder.Services.AddLocalization();
                builder.Services.AddAuthorizationCore();
                builder.Services.AddHybridCache();
                builder.Services.AddBlazoredLocalStorage();
                //builder.Services.AddBlazoredSessionStorageAsSingleton();
                builder.Services.AddMauiBlazorWebView();
                builder.Services.AddFluentUIComponents();
                builder.Services.AddSyncfusionBlazor();
                builder.Services.AddTransient(typeof(IStringLocalizer<>), typeof(StringLocalizer<>));
                builder.Services.AddSingleton(typeof(ISyncfusionStringLocalizer), typeof(SyncfusionLocalizer));   // Register the locale service to localize the  SyncfusionBlazor components.
                Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzkyNDE5M0AzMzMwMmUzMDJlMzAzYjMzMzAzYk5qbkk2YXNoZ0pjdllpMU1nU0VVK0hUOG9McWFRKzZiVFU3QktkWi90M2s9;MzkyNDE5NEAzMzMwMmUzMDJlMzAzYjMzMzAzYkpRSjIrWERnSlZqbk12MEU4TSsrQ0dMWmJxK1FwZTBsYVBsenE5ZTZCTEE9");

                #region Depedency Injection
                builder.Services.AddScoped<ITooltipService, TooltipService>();
                builder.Services.AddSingleton<IFormFactor, FormFactor>();
                builder.Services.AddSingleton<IRestartAppService, RestartAppService>();
                builder.Services.AddSingleton<HttpClient>(sp =>
                {
                    IConfiguration configuration = sp.GetRequiredService<IConfiguration>();
                    string? baseUrl = configuration.GetValue<string>("WebApi:BaseUrl");

#if DEBUG
                    IFormFactor formFactor = sp.GetRequiredService<IFormFactor>();
                    //Αν είμαστε σε debug και πραγματική συσκευή τότε χρησιμοποιούμε άλλο localhost:port για το WebApi. Θέλει στον browser Edge στη σελιδα edge://inspect/#devices να 
                    //if (formFactor.GetPlatform() == DevicePlatform.Android.ToString() /*&& formFactor.GetDeviceType() == DeviceType.Physical.ToString()*/)
                    //{
                    //    baseUrl = configuration.GetValue<string>("WebApi:BaseUrlPsysicalDevice");
                    //}
                    if (formFactor.GetPlatform() == DevicePlatform.Android.ToString() && DeviceInfo.DeviceType == DeviceType.Physical)
                    {
                        baseUrl = configuration.GetValue<string>("WebApi:BaseUrlPsysicalDevice");
                    }
                    else if (formFactor.GetPlatform() == DevicePlatform.Android.ToString() && DeviceInfo.DeviceType == DeviceType.Virtual)
                    {
                        baseUrl = configuration.GetValue<string>("WebApi:BaseUrlVirtualDevice");
                    }
                    else if (formFactor.GetPlatform() == DevicePlatform.WinUI.ToString())
                    {
                        baseUrl = configuration.GetValue<string>("WebApi:BaseUrlWinUIDevice");
                    }
#endif

                    HttpClient httpClient = new HttpClient
                    {
                        BaseAddress = new Uri(baseUrl!),
                        DefaultRequestVersion = new Version(2, 0), // Use HTTP/2 for better performance
                        Timeout = TimeSpan.Parse(configuration.GetValue<string>("WebApi:Timeout")!),
                        DefaultRequestHeaders =
                        {
                            // Στο request header προσθέτουμε το ApplicationId για να ξέρουμε ποιο application στέλνει το request.
                            { "ApplicationId", FluentBlue.Shared.ApplicationInstanceInfo.ApplicationInstanceId.ToString() }
                        }
                    };
                    //httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AuthenticatedUserData.)
                    return httpClient;
                });

                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Settings.UserSetting>, UserSettingValidator>();
                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Calendar.Event>, InsertUpdateEventValidator>();
                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Calendar.EventState>, EventStateValidator>();
                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Calendar.EventCategory>, EventCategoryValidator>();
                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Contacts.Contact>, ContactValidator>();
                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Contacts.ContactCategory>, ContactCategoryValidator>();
                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Tenants.User>, UserValidator>();
                builder.Services.AddScoped<FluentValidation.IValidator<FluentBlue.Data.Model.DBOs.Tenants.Role>, RoleValidator>();

                builder.Services.AddScoped<AuthenticationWebApiClient>();  //Για να χρησιμοποιηθεί από το JwtAuthenticationStateProvider
                builder.Services.AddScoped<UsersWebApiClient>();
                builder.Services.AddScoped<SettingsWebApiClient>();
                builder.Services.AddScoped<EventCategoriesWebApiClient>();
                builder.Services.AddScoped<EventsWebApiClient>();
                builder.Services.AddScoped<ContactCategoriesWebApiClient>();
                builder.Services.AddScoped<UserDevicesWebApiClient>();
                //builder.Services.AddScoped<FirebaseService>();

                //builder.Services.AddScoped<NotificationService>(); // Register SignalR service
                // Register NotificationService as Singleton with configuration
                builder.Services.AddSingleton<SignalRNotificationService>(sp =>
                {
                    var configuration = sp.GetRequiredService<IConfiguration>();
                    var hubUrl = configuration["SignalR:HubUrl"]; // Read from configuration
                    if (string.IsNullOrEmpty(hubUrl))
                    {
                        // Optional: Log an error or throw if the URL is missing
                        // Log.Error("SignalR Hub URL is not configured in appsettings.");
                        throw new InvalidOperationException("SignalR Hub URL is not configured.");
                    }
                    return new SignalRNotificationService(hubUrl);
                });

                //Για το JWT
                builder.Services.AddScoped<JwtAuthenticationStateProvider>();
                builder.Services.AddScoped<AuthenticationStateProvider, JwtAuthenticationStateProvider>(
                    provider => provider.GetRequiredService<JwtAuthenticationStateProvider>()  //προσθέτουμε αυτή τη γραμμή ώστε σαν object για το JwtAuthenticationStateProvider να χρησιμοποιήσει αυτό που έφτιαξε στην παραπάνω γραμμή.
                );
                builder.Services.AddScoped<ILoginService, JwtAuthenticationStateProvider>(
                    provider => provider.GetRequiredService<JwtAuthenticationStateProvider>()  //προσθέτουμε αυτή τη γραμμή ώστε σαν object για το JwtAuthenticationStateProvider να χρησιμοποιήσει αυτό που έφτιαξε παραπάνω.
                );

                builder.Services.AddScoped<IPerformPhoneCall, PerformPhoneCall>();
                builder.Services.AddScoped<IMapsNavigation, MapsNavigation>();
                builder.Services.AddScoped<ISendEmail, SendEmail>();
                builder.Services.AddSingleton<IDevicePreferences, DevicePreferences>();
                builder.Services.AddSingleton<IWebUpdateCheckerService, WebUpdateCheckerService>();
                builder.Services.AddSingleton<IAppVersionHelper, AppVersionHelper>();

                #endregion

#if DEBUG
                builder.Services.AddBlazorWebViewDeveloperTools();
                builder.Logging.AddDebug();
#endif

                MauiApp app = builder.Build();

                #region  Ρυθμίζει το language, timezone της εφαρμογής.
                CultureInfo? cultureInfo = null;

                //Διαβάζει τα UserSetting του χρήστη από το LocalStorage που πιθανότατα είναι ο ίδιος χρήστης που συνδέεται και τώρα. Οπότε θα χρησιμοποιήσουμε τα ίδια UserSetting.
                ILocalStorageService localStorage = app.Services.GetRequiredService<ILocalStorageService>();

                string languageText = string.Empty;
                string shortTimeFormat = string.Empty;
                string longTimeFormat = string.Empty;
                string shortDateFormat = string.Empty;

                languageText = Preferences.Get("Language", "");
                Language language = Language.EnglishGeneral;  //Βάζουμε μια default τιμή αλλά θα αλλάξει παρακάτω.

                //Αν βρέθηκαν ρυθμίσεις σχετικά με τη γλώσσα στο LocalStorage, δηλαδή ο ίδιος χρήστης έχει πιθανόν κάνει ξανά login στην εφαρμογή.
                if (languageText != "")
                {
                    language = languageText.Contains("el") ? Language.GreekGeneral : Language.EnglishGeneral;
                    cultureInfo = language.GetCultureInfo();
                }
                else  //Αν δεν βρέθηκαν ρυθμίσεις σχετικά με τη γλώσσα στο LocalStorage, δηλαδή ο χρήστης δεν έχει κάνει login ποτέ στην εφαρμογή.
                {
                    TimeZoneInfo timeZoneInfo1 = TimeZoneInfo.Local;
                    string displayName = timeZoneInfo1.DisplayName;

                    language = displayName.Contains("el") ? Language.GreekGeneral : Language.EnglishGeneral;
                    cultureInfo = language.GetCultureInfo();

                    Preferences.Set("Language", language.ToString());
                }

                //Καταχωρεί στο LocalStorage το TimeZone του συστήματος.
                TimeZoneInfo timeZoneInfo2 = TimeZoneInfo.Local;
                string timezone = timeZoneInfo2.Id;
                Preferences.Set("TimeZone", timezone);
                #endregion

                return app;
            }
            catch (Exception ex)
            {
                // Log the exception
                Log.Fatal(ex, "Application failed to start");

                // Make sure to flush logs before potentially crashing
                Log.CloseAndFlush();

                // Re-throw to let the platform handle the crash
                throw;
            }
        }


    }
    public static class MauiAppBuilderExtensions
    {
        public static MauiAppBuilder ConfigureFirebase(this MauiAppBuilder builder)
        {
            // Add Firebase configuration logic here.
            // For example, you might initialize Firebase services or add Firebase-related dependencies.
            return builder;
        }
    }
}
