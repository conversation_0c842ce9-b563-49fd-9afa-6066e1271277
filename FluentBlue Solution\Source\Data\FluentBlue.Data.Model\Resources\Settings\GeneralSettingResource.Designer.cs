﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources.Settings {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class GeneralSettingResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal GeneralSettingResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.Settings.GeneralSettingResource", typeof(GeneralSettingResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agenda.
        /// </summary>
        public static string CalendarAgendaView {
            get {
                return ResourceManager.GetString("CalendarAgendaView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        public static string CalendarDayView {
            get {
                return ResourceManager.GetString("CalendarDayView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default view.
        /// </summary>
        public static string CalendarDefaultView {
            get {
                return ResourceManager.GetString("CalendarDefaultView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default view on mobiles.
        /// </summary>
        public static string CalendarDefaultViewOnMobiles {
            get {
                return ResourceManager.GetString("CalendarDefaultViewOnMobiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month.
        /// </summary>
        public static string CalendarMonthView {
            get {
                return ResourceManager.GetString("CalendarMonthView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 08:00.
        /// </summary>
        public static string CalendarScrollToEight {
            get {
                return ResourceManager.GetString("CalendarScrollToEight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 05:00.
        /// </summary>
        public static string CalendarScrollToFive {
            get {
                return ResourceManager.GetString("CalendarScrollToFive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 04:00.
        /// </summary>
        public static string CalendarScrollToFour {
            get {
                return ResourceManager.GetString("CalendarScrollToFour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 09:00.
        /// </summary>
        public static string CalendarScrollToNine {
            get {
                return ResourceManager.GetString("CalendarScrollToNine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No scroll.
        /// </summary>
        public static string CalendarScrollToNone {
            get {
                return ResourceManager.GetString("CalendarScrollToNone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 01:00.
        /// </summary>
        public static string CalendarScrollToOne {
            get {
                return ResourceManager.GetString("CalendarScrollToOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 07:00.
        /// </summary>
        public static string CalendarScrollToSeven {
            get {
                return ResourceManager.GetString("CalendarScrollToSeven", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 06:00.
        /// </summary>
        public static string CalendarScrollToSix {
            get {
                return ResourceManager.GetString("CalendarScrollToSix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10:00.
        /// </summary>
        public static string CalendarScrollToTen {
            get {
                return ResourceManager.GetString("CalendarScrollToTen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 03:00.
        /// </summary>
        public static string CalendarScrollToThree {
            get {
                return ResourceManager.GetString("CalendarScrollToThree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scroll to time.
        /// </summary>
        public static string CalendarScrollToTime {
            get {
                return ResourceManager.GetString("CalendarScrollToTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 02:00.
        /// </summary>
        public static string CalendarScrollToTwo {
            get {
                return ResourceManager.GetString("CalendarScrollToTwo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Interval.
        /// </summary>
        public static string CalendarTimeScaleInterval {
            get {
                return ResourceManager.GetString("CalendarTimeScaleInterval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Week.
        /// </summary>
        public static string CalendarWeekView {
            get {
                return ResourceManager.GetString("CalendarWeekView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working days.
        /// </summary>
        public static string CalendarWorkDays {
            get {
                return ResourceManager.GetString("CalendarWorkDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End time.
        /// </summary>
        public static string CalendarWorkEnd {
            get {
                return ResourceManager.GetString("CalendarWorkEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start time.
        /// </summary>
        public static string CalendarWorkStart {
            get {
                return ResourceManager.GetString("CalendarWorkStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work week.
        /// </summary>
        public static string CalendarWorkWeekView {
            get {
                return ResourceManager.GetString("CalendarWorkWeekView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color.
        /// </summary>
        public static string Color {
            get {
                return ResourceManager.GetString("Color", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color mode.
        /// </summary>
        public static string ColorMode {
            get {
                return ResourceManager.GetString("ColorMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regional Settings.
        /// </summary>
        public static string CultureField {
            get {
                return ResourceManager.GetString("CultureField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date format.
        /// </summary>
        public static string DateFormat {
            get {
                return ResourceManager.GetString("DateFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What to display in event&apos;s text.
        /// </summary>
        public static string EventTextDisplay {
            get {
                return ResourceManager.GetString("EventTextDisplay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Separator in event&apos;s text.
        /// </summary>
        public static string EventTextSeparator {
            get {
                return ResourceManager.GetString("EventTextSeparator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 15 minutes.
        /// </summary>
        public static string FifteenMinutes {
            get {
                return ResourceManager.GetString("FifteenMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First day of week.
        /// </summary>
        public static string FirstDayOfWeek {
            get {
                return ResourceManager.GetString("FirstDayOfWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5 minutes.
        /// </summary>
        public static string FiveMinutes {
            get {
                return ResourceManager.GetString("FiveMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show fullname as.
        /// </summary>
        public static string FullNameDisplay {
            get {
                return ResourceManager.GetString("FullNameDisplay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include phones in lists of contacts.
        /// </summary>
        public static string ShowPhonesInContactLists {
            get {
                return ResourceManager.GetString("ShowPhonesInContactLists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include SSN in lists of contacts.
        /// </summary>
        public static string ShowSsnInContactLists {
            get {
                return ResourceManager.GetString("ShowSsnInContactLists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include TIN in lists of contacts.
        /// </summary>
        public static string ShowTinInContactLists {
            get {
                return ResourceManager.GetString("ShowTinInContactLists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 60 minutes.
        /// </summary>
        public static string SixtyMinutes {
            get {
                return ResourceManager.GetString("SixtyMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10 minutes.
        /// </summary>
        public static string TenMinutes {
            get {
                return ResourceManager.GetString("TenMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 30 minutes.
        /// </summary>
        public static string ThirtyMinutes {
            get {
                return ResourceManager.GetString("ThirtyMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time format.
        /// </summary>
        public static string TimeFormat {
            get {
                return ResourceManager.GetString("TimeFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Zone.
        /// </summary>
        public static string TimeZone {
            get {
                return ResourceManager.GetString("TimeZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 20 minutes.
        /// </summary>
        public static string TwentyMinutes {
            get {
                return ResourceManager.GetString("TwentyMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserSettingId.
        /// </summary>
        public static string UserSettingId {
            get {
                return ResourceManager.GetString("UserSettingId", resourceCulture);
            }
        }
    }
}
