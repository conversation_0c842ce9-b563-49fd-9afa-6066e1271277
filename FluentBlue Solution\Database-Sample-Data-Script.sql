-- =============================================
-- FluentBlue Sample Data Setup Script
-- Password Reset and User Registration Features
-- =============================================
-- This script creates sample data for testing:
-- 1. Default tenant and roles
-- 2. Sample users for testing
-- 3. Configuration data
-- =============================================

USE [FluentBlue]
GO

-- =============================================
-- 1. CREATE DEFAULT TENANT (if not exists)
-- =============================================

DECLARE @DefaultTenantId UNIQUEIDENTIFIER = '11111111-1111-1111-1111-111111111111';
DECLARE @DefaultTenantName NVARCHAR(100) = 'Default Tenant';

IF NOT EXISTS (SELECT * FROM [Tenants].[Tenant] WHERE [TenantId] = @DefaultTenantId)
BEGIN
    INSERT INTO [Tenants].[Tenant] ([TenantId], [Name], [Email], [DateCreatedUtc], [DateModifiedUtc])
    VALUES (@DefaultTenantId, @DefaultTenantName, '<EMAIL>', GETUTCDATE(), GETUTCDATE());
    
    PRINT 'Created default tenant: ' + @DefaultTenantName;
END
ELSE
BEGIN
    PRINT 'Default tenant already exists';
END
GO

-- =============================================
-- 2. CREATE DEFAULT ROLES (if not exist)
-- =============================================

DECLARE @AdminRoleId UNIQUEIDENTIFIER = '22222222-2222-2222-2222-222222222222';
DECLARE @UserRoleId UNIQUEIDENTIFIER = '33333333-3333-3333-3333-333333333333';
DECLARE @DefaultTenantId UNIQUEIDENTIFIER = '11111111-1111-1111-1111-111111111111';

-- Admin Role
IF NOT EXISTS (SELECT * FROM [Tenants].[Role] WHERE [RoleId] = @AdminRoleId)
BEGIN
    INSERT INTO [Tenants].[Role] ([RoleId], [Name], [DateCreatedUtc], [DateModifiedUtc])
    VALUES (@AdminRoleId, 'Admin', GETUTCDATE(), GETUTCDATE());
    
    PRINT 'Created Admin role';
END
ELSE
BEGIN
    PRINT 'Admin role already exists';
END

-- User Role
IF NOT EXISTS (SELECT * FROM [Tenants].[Role] WHERE [RoleId] = @UserRoleId)
BEGIN
    INSERT INTO [Tenants].[Role] ([RoleId], [Name], [DateCreatedUtc], [DateModifiedUtc])
    VALUES (@UserRoleId, 'User', GETUTCDATE(), GETUTCDATE());
    
    PRINT 'Created User role';
END
ELSE
BEGIN
    PRINT 'User role already exists';
END
GO

-- =============================================
-- 3. CREATE SAMPLE ADMIN USER (if not exists)
-- =============================================

DECLARE @AdminUserId UNIQUEIDENTIFIER = '44444444-4444-4444-4444-444444444444';
DECLARE @AdminRoleId UNIQUEIDENTIFIER = '22222222-2222-2222-2222-222222222222';
DECLARE @DefaultTenantId UNIQUEIDENTIFIER = '11111111-1111-1111-1111-111111111111';

IF NOT EXISTS (SELECT * FROM [Tenants].[User] WHERE [UserId] = @AdminUserId)
BEGIN
    INSERT INTO [Tenants].[User] (
        [UserId], [RoleId], [TenantId], [FirstName], [LastName], [MiddleName], 
        [Email], [Username], [Password], [Phone], [Mobile], [Image], 
        [AvailableInEventUsers], [DateCreatedUtc], [DateModifiedUtc]
    )
    VALUES (
        @AdminUserId, @AdminRoleId, @DefaultTenantId, 'System', 'Administrator', '',
        '<EMAIL>', 'admin', 'admin123', '', '', '',
        1, GETUTCDATE(), GETUTCDATE()
    );
    
    PRINT 'Created admin user: <EMAIL> (username: admin, password: admin123)';
END
ELSE
BEGIN
    PRINT 'Admin user already exists';
END
GO

-- =============================================
-- 4. CREATE SAMPLE TEST USER (if not exists)
-- =============================================

DECLARE @TestUserId UNIQUEIDENTIFIER = '55555555-5555-5555-5555-555555555555';
DECLARE @UserRoleId UNIQUEIDENTIFIER = '33333333-3333-3333-3333-333333333333';
DECLARE @DefaultTenantId UNIQUEIDENTIFIER = '11111111-1111-1111-1111-111111111111';

IF NOT EXISTS (SELECT * FROM [Tenants].[User] WHERE [UserId] = @TestUserId)
BEGIN
    INSERT INTO [Tenants].[User] (
        [UserId], [RoleId], [TenantId], [FirstName], [LastName], [MiddleName], 
        [Email], [Username], [Password], [Phone], [Mobile], [Image], 
        [AvailableInEventUsers], [DateCreatedUtc], [DateModifiedUtc]
    )
    VALUES (
        @TestUserId, @UserRoleId, @DefaultTenantId, 'Test', 'User', '',
        '<EMAIL>', 'testuser', 'test123', '', '+1234567890', '',
        1, GETUTCDATE(), GETUTCDATE()
    );
    
    PRINT 'Created test user: <EMAIL> (username: testuser, password: test123)';
END
ELSE
BEGIN
    PRINT 'Test user already exists';
END
GO

-- =============================================
-- 5. CREATE USER SETTINGS FOR SAMPLE USERS
-- =============================================

DECLARE @AdminUserId UNIQUEIDENTIFIER = '44444444-4444-4444-4444-444444444444';
DECLARE @TestUserId UNIQUEIDENTIFIER = '55555555-5555-5555-5555-555555555555';

-- Admin user settings
IF NOT EXISTS (SELECT * FROM [Settings].[UserSetting] WHERE [UserId] = @AdminUserId)
BEGIN
    INSERT INTO [Settings].[UserSetting] ([UserId])
    VALUES (@AdminUserId);
    
    PRINT 'Created user settings for admin user';
END
ELSE
BEGIN
    PRINT 'Admin user settings already exist';
END

-- Test user settings
IF NOT EXISTS (SELECT * FROM [Settings].[UserSetting] WHERE [UserId] = @TestUserId)
BEGIN
    INSERT INTO [Settings].[UserSetting] ([UserId])
    VALUES (@TestUserId);
    
    PRINT 'Created user settings for test user';
END
ELSE
BEGIN
    PRINT 'Test user settings already exist';
END
GO

-- =============================================
-- 6. CREATE SAMPLE PASSWORD RESET TOKEN (for testing)
-- =============================================

DECLARE @TestUserId UNIQUEIDENTIFIER = '55555555-5555-5555-5555-555555555555';
DECLARE @SampleToken NVARCHAR(255) = 'sample-test-token-12345-abcdef';

-- Clean up any existing test tokens first
DELETE FROM [Tenants].[PasswordResetToken] 
WHERE [UserId] = @TestUserId AND [Token] = @SampleToken;

-- Create a sample token for testing (expires in 1 hour)
INSERT INTO [Tenants].[PasswordResetToken] (
    [UserId], [Token], [DateCreatedUtc], [ExpiresUtc], [IsUsed]
)
VALUES (
    @TestUserId, @SampleToken, GETUTCDATE(), DATEADD(HOUR, 1, GETUTCDATE()), 0
);

PRINT 'Created sample password reset token for testing';
PRINT 'Test token: ' + @SampleToken;
PRINT 'Test URL: /reset-password?token=' + @SampleToken;
GO

-- =============================================
-- 7. DISPLAY SUMMARY INFORMATION
-- =============================================

PRINT '============================================='
PRINT 'FluentBlue Sample Data Setup Complete!'
PRINT '============================================='
PRINT 'Sample accounts created:'
PRINT ''
PRINT 'ADMIN ACCOUNT:'
PRINT '  Email: <EMAIL>'
PRINT '  Username: admin'
PRINT '  Password: admin123'
PRINT '  Role: Admin'
PRINT ''
PRINT 'TEST ACCOUNT:'
PRINT '  Email: <EMAIL>'
PRINT '  Username: testuser'
PRINT '  Password: test123'
PRINT '  Role: User'
PRINT ''
PRINT 'Testing Information:'
PRINT '  - Use <EMAIL> to test password reset'
PRINT '  - Sample reset token created for immediate testing'
PRINT '  - Try registering new users through the UI'
PRINT ''
PRINT 'Security Notes:'
PRINT '  - Change default passwords in production!'
PRINT '  - Implement password hashing before production use!'
PRINT '  - Configure proper SMTP settings for email functionality'
PRINT '============================================='

-- =============================================
-- 8. VERIFICATION QUERIES
-- =============================================

PRINT ''
PRINT 'Database verification:'
PRINT '----------------------'

SELECT 
    'Tenants' as TableType,
    COUNT(*) as RecordCount
FROM [Tenants].[Tenant]
UNION ALL
SELECT 
    'Roles' as TableType,
    COUNT(*) as RecordCount
FROM [Tenants].[Role]
UNION ALL
SELECT 
    'Users' as TableType,
    COUNT(*) as RecordCount
FROM [Tenants].[User]
UNION ALL
SELECT 
    'Password Reset Tokens' as TableType,
    COUNT(*) as RecordCount
FROM [Tenants].[PasswordResetToken]
UNION ALL
SELECT 
    'User Settings' as TableType,
    COUNT(*) as RecordCount
FROM [Settings].[UserSetting];

GO
