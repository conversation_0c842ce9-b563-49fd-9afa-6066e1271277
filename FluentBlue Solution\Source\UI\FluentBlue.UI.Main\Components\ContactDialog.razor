﻿@inject HttpClient httpClient
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject ILogger<FluentBlue.UI.Main.Components.ContactDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.ContactsWebApiClient> contactsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.EventsWebApiClient> eventsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.ContactCategoriesWebApiClient> contactCategoresWebApiClientLogger
@inject HybridCache cache
@inject IFormFactor formFactor
@inject IJSRuntime JS
@inject IPerformPhoneCall performPhoneCall
@inject IMapsNavigation mapsNavigation
@inject ISendEmail sendEmail
@* @inject ILogger<FluentBlue.WebApi.Client.EventsWebApiClient> eventsWebApiClientLogger *@

@using Blazored.FluentValidation
@using FluentBlue.Data.Model
@using FluentBlue.Data.Model.DBOs.Contacts
@using FluentBlue.Shared.Utilities
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Caching.Hybrid
@using Microsoft.JSInterop
@using Syncfusion.Blazor.RichTextEditor

@implements IDialogContentComponent<FluentBlue.UI.Main.Shared.ContactDialogInput>


<FluentDialogHeader Class="hidden"></FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto">
    <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4">@Resources.ContactDialogResource.Title</FluentLabel>
    <EditForm id="editForm" Model="@this.Content.Contact">
        <ChildContent Context="context2">
            @* <FluentValidationSummary></FluentValidationSummary> *@
            <FluentValidationValidator @ref="fluentValidationValidator" Validator="validator" />

            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                <FluentButton Loading="@this.isSaving" Disabled="@this.isDeleting" IconStart="@(new Icons.Regular.Size16.Save())" Appearance="Appearance.Accent" OnClick="SaveBtnOnClick"><span class="xs:hidden">@Resources.ContactDialogResource.SaveBtn_Text</span></FluentButton>
                @if (this.Content.Restricted == false)
                {
                    <FluentButton Loading="@this.isDeleting" Disabled="@this.isSaving" OnClick="DeleteBtnOnClick"><FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Color="Color.Error" Slot="start" /><span class="xs:hidden sm:hidden">@Resources.ContactDialogResource.DeleteBtn_Text</span></FluentButton>   @* Color="var(--error)" *@
                }
                <FluentButton Disabled="@(this.isSaving || this.isDeleting)" IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CloseBtnOnClick"><span class="xs:hidden sm:hidden">@GlobalResource.Close</span></FluentButton>
            </FluentToolbar>

            <FluentTabs Orientation="Orientation.Horizontal" Size="TabSize.Medium" Class="h-full xs:px-0 sm:px-0" OnTabChange="@OnTabChange">
                <FluentTab Label="@Resources.ContactDialogResource.GeneralTab" Id="generalTab">
                    <FluentDivider Class="w-full mt-0 mb-4" Role="DividerRole.Presentation"></FluentDivider>

                    <FluentGrid Spacing="2">
                        <FluentGridItem xs="12" md="6">
                            <FluentTextField @bind-Value="@Content!.Contact.FirstName" Label="@Resources.ContactDialogResource.FirstName" Maxlength="100" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.Contact.FirstName)" />
                        </FluentGridItem>

                        <FluentGridItem xs="12" md="6">
                            <FluentTextField @bind-Value="@Content!.Contact.LastName" Maxlength="100" Label="@Resources.ContactDialogResource.LastName" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.Contact.LastName)" />
                        </FluentGridItem>

                        <FluentGridItem xs="12" md="6">
                            <FluentTextField @bind-Value="@Content!.Contact.MiddleName" Maxlength="100" Label="@Resources.ContactDialogResource.MiddleName" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.Contact.MiddleName)" />
                        </FluentGridItem>

                        <FluentGridItem xs="12" md="6">
                            <FluentTextField @bind-Value="@Content!.Contact.Occupation" Maxlength="200" Label="@Resources.ContactDialogResource.Occupation" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.Contact.Occupation)" />
                        </FluentGridItem>

                        <FluentGridItem xs="12" md="6">
                            <FluentAutocomplete @ref="@contactCategoriesSelect" Label="@Resources.ContactDialogResource.ContactCategory" Items="@this.contactCategories" SelectedOptions="@selectedContactCategories" TOption="ContactCategory" IconSearch="null" Class="w-full" KeepOpen="false" Appearance="FluentInputAppearance.Outline" MaximumSelectedOptions="5" OptionText="@(x => x.Name)" IconDismiss="null" AutoComplete="off" SelectedOptionsChanged="ContactCategoriesSelectOnSelectedOptionsChanged">
                                @* Template used with each Selected items *@
                                <SelectedOptionTemplate>
                                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Class="w-full">
                                        <FluentPersona ImageSize="1px" Image="_content/FluentBlue.UI.Main/images/Empty.png" Name="@($"{context.Name}")" Style=@("height: 25px; background:" + context.Color + ";") DismissTitle="@Resources.EventDialogResource.Dismiss" OnDismissClick="@(async () => await this.contactCategoriesSelect.RemoveSelectedItemAsync(context))" />
                                    </FluentStack>
                                </SelectedOptionTemplate>

                                @* Template used with each Option items *@
                                <OptionTemplate>
                                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" VerticalAlignment="VerticalAlignment.Center" Class="w-full">
                                        <FluentIcon Icon="Icons.Filled.Size20.Square" hidden="@(context.ContactCategoryId == Guid.Empty)" Slot="start" Color=@Color.Custom CustomColor="@context.Color" />
                                        <FluentLabel Class="w-1/2">@context.Name</FluentLabel>
                                    </FluentStack>
                                </OptionTemplate>

                                @* Template used when the maximum number of selected items (MaximumSelectedOptions) has been reached *@
                                <MaximumSelectedOptionsMessage>
                                    @Resources.ContactDialogResource.MaximumCategoriesSelected
                                </MaximumSelectedOptionsMessage>

                                @* Content display at the top of the Popup area *@
                                @*  <HeaderContent>
                <FluentLabel Color="Color.Accent"
                             Style="padding: 8px; font-size: 11px; border-bottom: 1px solid var(--neutral-fill-stealth-hover);">
                    Suggested contacts
                </FluentLabel>
            </HeaderContent> *@

                                @* Content display at the bottom of the Popup area *@
                                <FooterContent>
                                    @if (!context.Any())
                                    {
                                        <FluentLabel Style="font-size: 11px; text-align: center; width: 200px;">
                                            @Resources.ContactDialogResource.NoCategoriesFound
                                        </FluentLabel>
                                    }
                                </FooterContent>
                            </FluentAutocomplete>
                        </FluentGridItem>
                    </FluentGrid>

                    <FluentGrid Spacing="5" Class="py-5">
                        <FluentGridItem xs="12" sm="12" md="12" lg="6" xl="6">
                            @* Emails *@
                            <FluentStack Orientation="Orientation.Vertical">
                                <div style="display: flex; width:100%">
                                    <FluentLabel Typo="Typography.Header">@Resources.ContactDialogResource.Emails</FluentLabel>
                                    <FluentSpacer />
                                    <FluentButton IconStart="@(new Icons.Regular.Size16.Add())" OnClick="@AddEmailBtnOnClick" Appearance="Appearance.Outline"></FluentButton>
                                </div>
                                @if (this.Content!.Contact.Emails.Count == 0)
                                {
                                    @Resources.ContactDialogResource.NoEmails
                                }
                                else
                                {
                                    @foreach (Data.Model.DBOs.Contacts.ContactEmail email in this.Content!.Contact.Emails.Where(x => x.ObjectState != Data.Model.ObjectState.Deleted).AsEnumerable())
                                    {
                                        <FluentStack Orientation="Orientation.Horizontal">
                                            <div style="width:100%;">
                                                <FluentTextField Appearance="FluentInputAppearance.Filled" @bind-Value="@email.EmailAddress" Style="width:100%;" AutoComplete="off" />
                                                <FluentValidationMessage For="@(() => email.EmailAddress)" />
                                            </div>
                                            <div style="width:150px;">
                                                <FluentSelect Appearance="Appearance.Filled" Value="@(email.Type?.ToString() ?? string.Empty)" ValueChanged="@(v => email.Type = string.IsNullOrEmpty(v) ? null : Enum.Parse<EmailType>(v))" TOption="string" Items="@(Enum.GetNames<EmailType>())" Width="150px"></FluentSelect>
                                            </div>
                                            <FluentButton id="@("addressMenu" + email.ContactEmailId.ToString())" OnClick="@(() => ToggleMenu(email.ContactEmailId))" IconStart="@(new Icons.Regular.Size16.MoreHorizontal())" Appearance="Appearance.Outline"></FluentButton>
                                            <FluentMenu Anchor="@("addressMenu" + email.ContactEmailId)" Open="@(menuStates.ContainsKey(email.ContactEmailId) && menuStates[email.ContactEmailId])" Anchored="true" VerticalThreshold="170">
                                                <FluentMenuItem OnClick="@((e) => SendEmailBtnOnClick(email.ContactEmailId))">
                                                    <FluentIcon Value="@(new Icons.Regular.Size16.Mail())" Slot="start" />@Resources.ContactDialogResource.SendEmail
                                                </FluentMenuItem>
                                                <FluentMenuItem OnClick="@((e) => RemoveEmailBtnOnClick(email.ContactEmailId))">
                                                    <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Slot="start" />@Main.GlobalResource.Delete
                                                </FluentMenuItem>
                                            </FluentMenu>
                                            @* <FluentButton OnClick="@(() => RemoveEmailBtn_OnClick(email.ContactEmailId))" IconStart="@(new Icons.Regular.Size16.Delete())" Appearance="Appearance.Outline"></FluentButton> *@
                                        </FluentStack>
                                    }
                                }
                            </FluentStack>
                        </FluentGridItem>
                        <FluentGridItem xs="12" sm="12" md="12" lg="6" xl="6">
                            @* Phones *@
                            <FluentStack Orientation="Orientation.Vertical">
                                <div style="display: flex; width:100%">
                                    <FluentLabel Typo="Typography.Header">@Resources.ContactDialogResource.Phones</FluentLabel>
                                    <FluentSpacer />
                                    <FluentButton IconStart="@(new Icons.Regular.Size16.Add())" OnClick="@AddPhoneBtnOnClick" Appearance="Appearance.Outline"></FluentButton>
                                </div>
                                @if (this.Content!.Contact.Phones.Count == 0)
                                {
                                    @Resources.ContactDialogResource.NoPhones
                                }
                                else
                                {
                                    @foreach (Data.Model.DBOs.Contacts.ContactPhone phone in this.Content!.Contact.Phones.Where(x => x.ObjectState != Data.Model.ObjectState.Deleted).AsEnumerable())
                                    {
                                        <FluentStack Orientation="Orientation.Horizontal">
                                            <div style="width:100%;">
                                                <FluentTextField Appearance="FluentInputAppearance.Filled" @bind-Value="@phone.PhoneNumber" Style="width:100%;" AutoComplete="off" />
                                                <FluentValidationMessage For="@(() => phone.PhoneNumber)" />
                                            </div>
                                            <div style="width:150px;">
                                                @* <FluentSelect Appearance="Appearance.Filled" @bind-Value="@phone.TypeStr" TOption="Option<string>" OptionText="@(i => i.Text.ToString())" OptionValue="@(i => i.Value.ToString())" Items="@phoneTypeOptions" Width="150px"></FluentSelect> *@
                                                <FluentSelect Appearance="Appearance.Filled" Value="@(phone.Type?.ToString() ?? string.Empty)" ValueChanged="@(v => phone.Type = string.IsNullOrEmpty(v) ? null : Enum.Parse<PhoneType>(v))" TOption="string" Items="@(Enum.GetNames<PhoneType>())" Width="150px"></FluentSelect>
                                            </div>
                                            <FluentButton id="@("phoneMenu" + phone.ContactPhoneId.ToString())" OnClick="@(() => ToggleMenu(phone.ContactPhoneId))" IconStart="@(new Icons.Regular.Size16.MoreHorizontal())" Appearance="Appearance.Outline"></FluentButton>
                                            <FluentMenu Anchor="@("phoneMenu" + phone.ContactPhoneId)" Open="@(menuStates.ContainsKey(phone.ContactPhoneId) && menuStates[phone.ContactPhoneId])" Anchored="true" VerticalThreshold="170">
                                                @if (formFactor.GetDeviceIdiom() == "Phone")
                                                {
                                                    <FluentMenuItem OnClick="@((e) => CallPhoneBtnOnClick(phone.ContactPhoneId))">
                                                        <FluentIcon Value="@(new Icons.Regular.Size16.Call())" Slot="start" />@Resources.ContactDialogResource.Call
                                                    </FluentMenuItem>
                                                }
                                                <FluentMenuItem OnClick="@((e) => RemovePhoneBtnOnClick(phone.ContactPhoneId))">
                                                    <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Slot="start" />@Main.GlobalResource.Delete
                                                </FluentMenuItem>
                                            </FluentMenu>
                                            @* <FluentButton OnClick="@(() => RemovePhoneBtn_OnClick(phone.ContactPhoneId))" IconStart="@(new Icons.Regular.Size16.Delete())" Appearance="Appearance.Outline"></FluentButton> *@
                                        </FluentStack>
                                    }
                                }
                            </FluentStack>
                            <FluentValidationMessage For="@(() => Content.Contact.Phones)" />
                        </FluentGridItem>
                        <FluentGridItem xs="12" sm="12" md="12" lg="12" xl="12">
                            @* Addresses *@
                            <FluentStack Orientation="Orientation.Vertical">
                                <div style="display: flex; width:100%">
                                    <FluentLabel Typo="Typography.Header">@Resources.ContactDialogResource.Addresses</FluentLabel>
                                    <FluentSpacer />
                                    <FluentButton IconStart="@(new Icons.Regular.Size16.Add())" OnClick="@AddAddressBtnOnClick" Appearance="Appearance.Outline"></FluentButton>
                                </div>
                                @if (this.Content!.Contact.Addresses.Count == 0)
                                {
                                    @Resources.ContactDialogResource.NoAddresses
                                }
                                else
                                {
                                    @foreach (Data.Model.DBOs.Contacts.ContactAddress address in this.Content!.Contact.Addresses.Where(x => x.ObjectState != Data.Model.ObjectState.Deleted).AsEnumerable())
                                    {
                                        <FluentStack Orientation="Orientation.Horizontal">
                                            <div style="width:100%;">
                                                <FluentTextField Appearance="FluentInputAppearance.Filled" @bind-Value="@address.Address" Style="width:100%;" Placeholder="@Resources.ContactDialogResource.Address" AutoComplete="off" />
                                                <FluentValidationMessage For="@(() => address.Address)" />
                                            </div>
                                            <div style="width:150px;">
                                                <FluentSelect Appearance="Appearance.Filled" Value="@(address.Type?.ToString() ?? string.Empty)" ValueChanged="@(v => address.Type = string.IsNullOrEmpty(v) ? null : Enum.Parse<AddressType>(v))" TOption="string" Items="@(Enum.GetNames<AddressType>())" Width="150px"></FluentSelect>
                                            </div>
                                            <FluentButton id="@("addressMenu" + address.ContactAddressId.ToString())" OnClick="@(() => ToggleMenu(address.ContactAddressId))" IconStart="@(new Icons.Regular.Size16.MoreHorizontal())" Appearance="Appearance.Outline"></FluentButton>
                                            <FluentMenu Anchor="@("addressMenu" + address.ContactAddressId)" Open="@(menuStates.ContainsKey(address.ContactAddressId) && menuStates[address.ContactAddressId])" Anchored="true" VerticalThreshold="170">
                                                <FluentMenuItem OnClick="@((e) => NavigateToAddressBtnOnClick(address.ContactAddressId))">
                                                    <FluentIcon Value="@(new Icons.Regular.Size16.LocationArrow())" Slot="start" />@Resources.ContactDialogResource.NavigateToAddress
                                                </FluentMenuItem>
                                                <FluentMenuItem OnClick="@((e) => RemoveAddressBtnOnClick(address.ContactAddressId))">
                                                    <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Slot="start" />@Main.GlobalResource.Delete
                                                </FluentMenuItem>
                                            </FluentMenu>
                                        </FluentStack>
                                    }
                                }
                            </FluentStack>
                        </FluentGridItem>
                    </FluentGrid>

                    <FluentGrid Spacing="2">
                        <FluentGridItem xs="12" md="4">
                            <FluentTextField @bind-Value="@Content!.Contact.TIN" Label="@Resources.ContactDialogResource.TIN" Maxlength="15" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.Contact.TIN)" />
                        </FluentGridItem>

                        <FluentGridItem xs="12" md="4">
                            <FluentTextField @bind-Value="@Content!.Contact.SSN" Maxlength="15" Label="@Resources.ContactDialogResource.SSN" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.Contact.SSN)" />
                        </FluentGridItem>

                        @*   <FluentGridItem xs="12">
                        </FluentGridItem> *@
                    </FluentGrid>
                    <SfRichTextEditor @bind-Value="@this.Content!.Contact.Notes" EnableResize="false" CssClass="h-96 mt-2">
                        <RichTextEditorToolbarSettings Type="ToolbarType.Expand" Items="@toolbarItems" />
                    </SfRichTextEditor>
                </FluentTab>

                <FluentTab Label="@Resources.ContactDialogResource.EventsTab" Id="eventsTab">
                    <FluentDivider Class="w-full mt-0 mb-4" Role="DividerRole.Presentation"></FluentDivider>

                    @* Αν είμαστε σε μικρές οθόνες *@
                    @if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "md")
                    {
                        <FluentDataGrid @ref="eventsDataGrid" Items="@this.events.AsQueryable()" ResizableColumns=true TGridItem="Data.Model.DBOs.Calendar.Event" OnRowClick="EventsDataGridOnRowClick">
                            <TemplateColumn Width="1fr">
                                <FluentStack Orientation="Orientation.Vertical">
                                    <FluentLabel Class="truncate">@context.Subject</FluentLabel>
                                    <FluentStack Orientation="Orientation.Vertical">
                                        <FluentLabel><b>@Resources.EventDialogResource.Start: </b>@context.StartTimeLocal?.ToString("g")</FluentLabel>
                                        <FluentLabel><b>@Resources.EventDialogResource.End: </b>@context.EndTimeLocal?.ToString("g")</FluentLabel>
                                    </FluentStack>
                                </FluentStack>
                            </TemplateColumn>
                            <TemplateColumn Align="@Align.Center" Class="p-0" Width="60px">
                                <div class="w-fit">
                                    <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => ShowEvent(context))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                                    @* <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnDeleteEvent(context.EventId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton> *@
                                </div>
                            </TemplateColumn>
                        </FluentDataGrid>
                    }
                    else
                    @* Σε μεγάλες οθόνες *@
                    {
                        <FluentDataGrid @ref="eventsDataGrid" Items="@this.events.AsQueryable()" ResizableColumns=true TGridItem="Data.Model.DBOs.Calendar.Event" OnRowDoubleClick="EventsDataGridOnRowDoubleClick">
                            <PropertyColumn Property="@(x => x.Subject)" Tooltip="true" Class="truncate" Width="1fr" />
                            <PropertyColumn Property="@(x => x.StartTimeLocal)" Format="g" Sortable="true" Width="200px" SortBy="startTimeGridSort" IsDefaultSortColumn="true" InitialSortDirection="SortDirection.Descending" Tooltip="true" />
                            <PropertyColumn Property="@(x => x.EndTimeLocal)" Format="g" Tooltip="true" Width="200px" />
                            <PropertyColumn Property="@(x => x.PlainDescription)" Tooltip="true" Class="truncate" Width="2fr" />
                            @if (this.Content!.Restricted == false)
                            {
                                <TemplateColumn Align="@Align.Center" Class="p-0" Width="90px">
                                    <div class="w-fit">
                                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => ShowEvent(context))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnDeleteEvent(context.EventId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton>
                                    </div>
                                </TemplateColumn>
                            }
                        </FluentDataGrid>
                    }
                </FluentTab>
            </FluentTabs>
        </ChildContent>
    </EditForm>
</FluentDialogBody>