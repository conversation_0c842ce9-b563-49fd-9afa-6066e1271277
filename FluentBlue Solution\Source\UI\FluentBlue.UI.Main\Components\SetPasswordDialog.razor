﻿@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Components.Forms
@implements IDialogContentComponent

@inject ILogger<FluentBlue.UI.Main.Components.SetPasswordDialog> logger
@inject IDialogService dialogService

<FluentDialogBody>
    <EditForm Model="@this.Content">
        <FluentValidationMessage For="@(() => Content.Password)" />
        <FluentTextField Label=@Resources.UserDialogResource.Password TextFieldType="TextFieldType.Password" ValueChanged="PasswordOnValueChanged" Required="true" AutoComplete="off" />
        <FluentTextField @bind-Value="@Content.VerifyPassword" TextFieldType="TextFieldType.Password" Label=@Resources.UserDialogResource.VerifyPassword  Required="true" AutoComplete="off" />

        <FluentLabel Color="@Color.Error">@((MarkupString)this.validationMessages)</FluentLabel>
    </EditForm>
</FluentDialogBody>

<FluentDialogFooter>
    <FluentButton Appearance="Appearance.Accent"  OnClick="@Save">
        @GlobalResource.Save
    </FluentButton>
    <FluentButton Appearance="Appearance.Neutral" OnClick="@Cancel">
        @GlobalResource.Cancel
    </FluentButton>
</FluentDialogFooter>
