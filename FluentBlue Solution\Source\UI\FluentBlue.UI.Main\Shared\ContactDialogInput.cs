﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.WebApi.Shared.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Shared
{
    public class ContactDialogInput
    {
        public required Contact Contact { get; set; }
        public bool Restricted { get; set; } = false;  // If true, then some options/actions are not available in the dialog  
    }
}
