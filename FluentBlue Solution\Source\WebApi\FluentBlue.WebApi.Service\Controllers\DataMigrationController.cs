using FluentBlue.Application.Business.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class DataMigrationController : ControllerBase
    {
        private readonly IDataMigrationService dataMigrationService;
        private readonly IApplicationKeyService applicationKeyService;
        private readonly ILogger<DataMigrationController> logger;

        public DataMigrationController(
            IDataMigrationService dataMigrationService,
            IApplicationKeyService applicationKeyService,
            ILogger<DataMigrationController> logger)
        {
            this.dataMigrationService = dataMigrationService;
            this.applicationKeyService = applicationKeyService;
            this.logger = logger;
        }

        [HttpGet("encryption-status")]
        public async Task<IActionResult> GetEncryptionStatus()
        {
            try
            {
                var migrationRequired = await dataMigrationService.IsEncryptionMigrationRequiredAsync();
                var applicationKey = await applicationKeyService.GetApplicationKeyAsync();
                
                return Ok(new
                {
                    MigrationRequired = migrationRequired,
                    HasApplicationKey = !string.IsNullOrEmpty(applicationKey),
                    Message = migrationRequired 
                        ? "Encryption migration is required" 
                        : "All data is already encrypted"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error checking encryption status");
                return StatusCode(500, new { error = "Error checking encryption status" });
            }
        }

        [HttpPost("generate-application-key")]
        public async Task<IActionResult> GenerateApplicationKey()
        {
            try
            {
                var newKey = await applicationKeyService.GenerateAndStoreApplicationKeyAsync();
                
                return Ok(new
                {
                    Message = "Application encryption key generated successfully",
                    KeyGenerated = !string.IsNullOrEmpty(newKey)
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error generating application key");
                return StatusCode(500, new { error = "Error generating application key" });
            }
        }

        [HttpPost("migrate-to-encryption")]
        public async Task<IActionResult> MigrateToEncryption()
        {
            try
            {
                logger.LogInformation("Starting encryption migration");
                
                await dataMigrationService.MigrateExistingDataToEncryptionAsync();
                
                logger.LogInformation("Encryption migration completed successfully");
                
                return Ok(new
                {
                    Message = "Data migration to encryption completed successfully"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during encryption migration");
                return StatusCode(500, new { error = "Error during encryption migration" });
            }
        }
    }
} 