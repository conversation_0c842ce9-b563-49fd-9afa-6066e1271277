﻿using FluentBlue.Shared.SignalR;
using Microsoft.AspNetCore.SignalR.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Services
{
    public class SignalRNotificationService : ISignalRNotificationService
    {
        private readonly HubConnection connection;
        private string tenantId = string.Empty;
        private string userId = string.Empty;

        public event Action<NotificationData>? OnNotificationReceived;

        public SignalRNotificationService(string hubUrl)
        {
            connection = new HubConnectionBuilder()
                .WithUrl(hubUrl)
                .WithAutomaticReconnect()
                .Build();

            connection.On<string>("FluentBlueNotification", (notificationDataJson) =>
            {
                //Convert json to NotificationData object
                NotificationData? notificationData = System.Text.Json.JsonSerializer.Deserialize<NotificationData>(notificationDataJson);
                if (notificationData != null)
                {
                    OnNotificationReceived?.Invoke(notificationData);
                }
            });

            // Handle reconnection to rejoin the current tenant group
            connection.Closed += async (error) =>
            {
                await Task.Delay(1000);
                await StartAsync(this.tenantId, this.userId);
            };
        }

        public async Task StartAsync(string tenantId, string userId)
        {
            this.tenantId = tenantId;
            this.userId = userId;

            if (connection.State == HubConnectionState.Disconnected)
            {
                await connection.StartAsync();
                await connection.InvokeAsync("JoinGroup", this.tenantId);
                await connection.InvokeAsync("JoinGroup", this.userId);
            }
        }

        public async Task SetTenantIdAsync(string tenantId)
        {
            if (string.IsNullOrWhiteSpace(tenantId))
                throw new ArgumentException("TenantId cannot be empty", nameof(tenantId));

            // Leave the current group if already in one
            if (!string.IsNullOrWhiteSpace(this.tenantId) && connection.State == HubConnectionState.Connected)
            {
                await connection.InvokeAsync("LeaveGroup", this.tenantId);
            }

            // Update tenant and join new group
            this.tenantId = tenantId;
            if (connection.State == HubConnectionState.Connected)
            {
                await connection.InvokeAsync("JoinGroup", this.tenantId);
            }
        }

        public async Task SetUserIdAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("UserId cannot be empty", nameof(userId));

            // Leave the current group if already in one
            if (!string.IsNullOrWhiteSpace(this.userId) && connection.State == HubConnectionState.Connected)
            {
                await connection.InvokeAsync("LeaveGroup", this.userId);
            }

            // Update user and join new group
            this.userId = userId;
            if (connection.State == HubConnectionState.Connected)
            {
                await connection.InvokeAsync("JoinGroup", this.userId);
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (connection != null)
            {
                if (connection.State == HubConnectionState.Connected)
                {
                    if (!string.IsNullOrWhiteSpace(this.tenantId))
                    {
                        await connection.InvokeAsync("LeaveGroup", this.tenantId);
                    }
                    if (!string.IsNullOrWhiteSpace(this.userId))
                    {
                        await connection.InvokeAsync("LeaveGroup", this.userId);
                    }
                    await connection.DisposeAsync();
                }
            }
        }
    }
}
