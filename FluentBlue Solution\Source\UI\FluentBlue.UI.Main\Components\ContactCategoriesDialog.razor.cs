﻿using Blazored.FluentValidation;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ComponentModel;

namespace FluentBlue.UI.Main.Components
{
    public partial class ContactCategoriesDialog
    {
        [Parameter] public List<ContactCategory>? Content { get; set; } = new List<ContactCategory>();
        [CascadingParameter] public FluentDialog CurrentDialog { get; set; } = default!;
        private FluentValidationValidator? fluentValidationValidator = new FluentValidationValidator();
        private UserSetting? userSetting;
        private ContactCategoryValidator eventCategoryValidator = new ContactCategoryValidator();
        private bool isSaving = false;
        private List<EventCategoryColor> predefinedColors = new List<EventCategoryColor>
        {
            new EventCategoryColor { Color = "#cd5c5c", Name = "Indian Red" },
            new EventCategoryColor { Color = "#AC4F5E", Name = "Dark Red" },
            new EventCategoryColor { Color = "#DC626D", Name = "Berry" },
            //new EventCategoryColor { Color = "#E9835E", Name = "DarkOrange" },
            new EventCategoryColor { Color = "#CA8057", Name = "Bronze" },
            new EventCategoryColor { Color = "#FFBA66", Name = "Peach" },
            new EventCategoryColor { Color = "#f0e68c", Name = "Orange" },
            new EventCategoryColor { Color = "#DAC157", Name = "Gold" },
            new EventCategoryColor { Color = "#946B5C", Name = "Brown" },
            new EventCategoryColor { Color = "#9acd32", Name = "Yellow Green" },
            new EventCategoryColor { Color = "#85B44C", Name = "Forest" },
            //new EventCategoryColor { Color = "#5EC75A", Name = "Green" },
            new EventCategoryColor { Color = "#4DA64D", Name = "Dark Green" },
            new EventCategoryColor { Color = "#58D3DB", Name = "Petrol" },
            new EventCategoryColor { Color = "#41A3A3", Name = "Dark Petrol" },
            new EventCategoryColor { Color = "#4682b4", Name = "SteelBlue" },
            //new EventCategoryColor { Color = "#4178A3", Name = "Blue" },
            new EventCategoryColor { Color = "#A79CF1", Name = "Levanda" },
            new EventCategoryColor { Color = "#7E5CA7", Name = "Purple" },
            new EventCategoryColor { Color = "#EF85C8", Name = "Pink" },
            new EventCategoryColor { Color = "#AD4589", Name = "Damask" },
            //new EventCategoryColor { Color = "#AFABAA", Name = "Beige" },
            new EventCategoryColor { Color = "#B3BFC2", Name = "Silver" },
            //new EventCategoryColor { Color = "#888888", Name = "anthracite" },
            new EventCategoryColor { Color = "#808080", Name = "Gray" }
        };

        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (fluentValidationValidator != null)
                {
                    fluentValidationValidator.Validator = eventCategoryValidator;
                }

                await base.OnInitializedAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);

                    var contactCategoriesCacheOptions = new HybridCacheEntryOptions
                    {
                        LocalCacheExpiration = TimeSpan.FromMinutes(60)
                    };

                    Content = await cache.GetOrCreateAsync(
                        Keywords.ContactCategories,
                        async cancel =>
                        {
                            ContactCategoriesWebApiClient contactCategoriesWebApiClient = new ContactCategoriesWebApiClient(httpClient, contactCategoriesWebApiClientLogger);
                            return await contactCategoriesWebApiClient.GetAllContactCategories(AuthenticatedUserData.TenantId);
                        },
                        contactCategoriesCacheOptions);

                    // For each category set timezone and enables NotifyPropertyChanged
                    foreach (var category in Content!)
                    {
                        category.UserTimeZoneId = this.userSetting!.TimeZone;
                        category.NotifyPropertyChangedEnabled = true;
                    }

                    StateHasChanged();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task DeleteBtnOnClick(ContactCategory category)
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();

                if (result.Cancelled == false)
                {
                    if (category.ObjectState == ObjectState.Added)
                    {
                        Content!.Remove(category);
                    }
                    else
                    {
                        ContactCategoriesWebApiClient contactCategoriesWebApiClient = new ContactCategoriesWebApiClient(httpClient, contactCategoriesWebApiClientLogger);
                        await contactCategoriesWebApiClient.DeleteContactCategory(category.ContactCategoryId);
                        Content!.Remove(category);
                    }
                    StateHasChanged();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task AddBtnOnClick()
        {
            try
            {
                var newCategory = new ContactCategory
                {
                    ContactCategoryId = Guid.CreateVersion7(),
                    TenantId = AuthenticatedUserData.TenantId,
                    Name = "",
                    Color = "#000000",
                    SortIndex = Content!.Any() ? Content!.Max(x => x.SortIndex) + 1 : 0,
                    ObjectState = ObjectState.Added,
                    UserTimeZoneId = this.userSetting!.TimeZone
                };

                newCategory.NotifyPropertyChangedEnabled = true;
                Content!.Add(newCategory);
                StateHasChanged();
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SaveBtnOnClick()
        {
            try
            {
                this.isSaving = true;

                if (await ValidateData())
                {
                    ContactCategoriesWebApiClient contactCategoriesWebApiClient = new ContactCategoriesWebApiClient(httpClient, contactCategoriesWebApiClientLogger);

                    // Update sort indexes
                    for (int i = 0; i < Content!.Count; i++)
                    {
                        Content[i].SortIndex = i;
                    }

                    // Save each category
                    foreach (var category in Content!)
                    {
                        if (category.ObjectState != ObjectState.Unchanged)
                        {
                            if (category.ObjectState == ObjectState.Added)
                            {
                                category.DateCreatedUtc = DateTime.UtcNow;
                            }
                            category.DateModifiedUtc = DateTime.UtcNow;

                            await contactCategoriesWebApiClient.CreateOrUpdateContactCategory(category);
                        }
                    }

                    // Clears the ContactCategories from cache
                    await cache.RemoveAsync(Keywords.ContactCategories);

                    // Reads the ContactCategories from the database.
                    Content = await contactCategoriesWebApiClient.GetAllContactCategories(AuthenticatedUserData.TenantId);

                    // Stores the ContactCategories in cache
                    var contactCategoriesCacheOptions = new HybridCacheEntryOptions
                    {
                        LocalCacheExpiration = TimeSpan.FromMinutes(60)
                    };
                    await cache.SetAsync(Keywords.ContactCategories, Content, contactCategoriesCacheOptions);

                    await this.CurrentDialog.CloseAsync(Content);
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
                this.isSaving = false;
            }
        }

        private async Task CancelBtnOnClick()
        {
            try
            {
                await this.CurrentDialog.CancelAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        private async Task<bool> ValidateData()
        {
            try
            {
                bool isValid = true;
                List<string> errorMessages = new List<string>();

                foreach (var category in Content!)
                {
                    var validationResult = await eventCategoryValidator.ValidateAsync(category);
                    if (!validationResult.IsValid)
                    {
                        isValid = false;
                        errorMessages.AddRange(validationResult.Errors.Select(e => e.ErrorMessage));
                    }
                }

                //Checks if the same ContactCategory title exists 2 times.
                if (Content!.GroupBy(x => x.Name).Any(x => x.Count() > 1))
                {
                    isValid = false;
                    errorMessages.Add(Resources.ContactCategoriesDialogResource.NameAlreadyExists);
                }

                if (!isValid)
                {
                    string errorMessage = GlobalResource.CorrectInvalidFields;
                    RenderFragment errorRF = FluentBlue.Shared.Utilities.ValidationErrorsToBulletsConverter.ConvertValidationErrorsToBullets(errorMessage, errorMessages.Distinct().ToArray(), "");

                    await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                    {
                        ShowTitle = false,
                        ShowDismiss = false,
                        DialogType = DialogType.MessageBox,
                        PrimaryAction = UI.Main.GlobalResource.Close,
                        SecondaryAction = "",
                        Modal = true,
                        PreventDismissOnOverlayClick = true
                    });
                }

                return isValid;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async Task HandleReorder(FluentSortableListEventArgs args)
        {
            try
            {
                if (args is null || args.OldIndex == args.NewIndex)
                {
                    return;
                }

                var oldIndex = args.OldIndex;
                var newIndex = args.NewIndex;

                var tempItems = this.Content;
                var itemToMove = this.Content![oldIndex];
                tempItems!.RemoveAt(oldIndex);

                if (newIndex < Content.Count)
                {
                    tempItems.Insert(newIndex, itemToMove);
                }
                else
                {
                    tempItems.Add(itemToMove);
                }

                // Update sort indexes
                for (int i = 0; i < tempItems!.Count; i++)
                {
                    tempItems[i].SortIndex = i;
                }

                this.Content = tempItems.OrderBy(x => x.SortIndex).ToList();

                StateHasChanged();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
    }
}
