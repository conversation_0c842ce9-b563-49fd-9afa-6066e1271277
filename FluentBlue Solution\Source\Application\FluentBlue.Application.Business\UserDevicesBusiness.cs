using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business
{
    public class UserDevicesBusiness : IUserDevicesBusiness
    {
        private readonly FluentBlueDbContext dbContext;
        private ILogger<UserDevicesBusiness> logger;

        public UserDevicesBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, ILogger<UserDevicesBusiness> logger)
        {
            this.dbContext = dbContext;
            this.logger = logger;
        }

        public async Task RegisterDeviceAsync(Guid userId, string deviceToken, string platform)
        {
            //Checks if device for specific device token and user already exists.
            UserDevice? existingDevice = await dbContext.UserDevices.FirstOrDefaultAsync(d => d.UserId == userId && d.DeviceToken == deviceToken);

            if (existingDevice != null)
            {
                existingDevice.DateUpdatedUtc = DateTime.UtcNow;
                existingDevice.ObjectState = ObjectState.Modified;
            }
            else
            {
                var newDevice = new UserDevice
                {
                    UserId = userId,
                    DeviceToken = deviceToken,
                    Platform = platform,
                    DateUpdatedUtc = DateTime.UtcNow,
                    ObjectState = ObjectState.Added
                };
                dbContext.UserDevices.Add(newDevice);
            }

            await dbContext.SaveChangesAsync();
        }
    }
}