﻿using AKSoftware.Blazor.Utilities;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Shared;
using FluentBlue.Shared.Utilities;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Components;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using System.Globalization;


namespace FluentBlue.UI.Main.Layout
{
    public partial class MainLayout
    {
        private FluentGrid? fluentGrid;
        bool userMenuOpened = false;
        bool quickMenuOpened = false;
        bool smNavMenuOpened = false;
        IDialogReference dialog;
        //private bool shouldRender = false;

        private Data.Model.DBOs.Settings.UserSetting? userSetting;
        private Data.Model.DBOs.Tenants.Role? userRole;  //Το Role του User
        private DesignThemeModes currentDesignThemeMode;  //Χρησιμοποιείται από το FluentDesignTheme
        private string currentThemeColorText;  //Η τιμή π.χ. #dd23F0 του currentThemeColor
        private string neutralBaseColor;
        private FluentDesignTheme fluentDesignTheme;
        private static readonly HybridCacheEntryOptions userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
        private static readonly HybridCacheEntryOptions userRoleCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
        private static readonly HybridCacheEntryOptions timeZonesCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };
        private ErrorBoundary? errorBoundary;

        protected override async Task OnInitializedAsync()
        {
            try
            {   
                //Καθαρίζει το cache από τα προηγούμενα δεδομένα
                await cache.RemoveAsync(Keywords.UserSetting);
                await ClearCacheData();

                #region  Reads the UserSettings from cache or database
                UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
                this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                    async cancel =>
                    {
                        UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                        return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                    }, userSettingCacheOptions);
                #endregion

                if (userSetting != null)
                {
                    // Apply theme after the component is rendered to ensure JS interop is ready
                    await SetTheme(ConvertThemeColorModeToThemeMode(this.userSetting!.ThemeColorMode), this.userSetting.ThemeColor);
                }

                //Αν είμαστε σε Web
                if (formFactor.GetDeviceIdiom() == "Web")
                {
                    //Έγιναν σχόλιο οι παρακάτω γραμμές για να δούμε αν κολλάει η σελίδα λόγω αυτού. Ακόμα ίσως να μη δουλεύει σωστά.
                    webUpdateChecker.OnUpdateDetected += OnWebUpdateDetected;
                    await webUpdateChecker.InitializeAsync();
                }

                //Set and handles the SignalR notification service
                signalRNotificationService.OnNotificationReceived += async (notificationData) =>
                {
                    try
                    {
                        //If the notification is not comming from this app.
                        if (notificationData.SenderId != ApplicationInstanceInfo.ApplicationInstanceId.ToString())
                        {
                            if (notificationData.NotificationType == "EventChanged")
                            {
                                MessagingCenter.Send(this, Keywords.ReloadEvents);
                            }
                            else if (notificationData.NotificationType == "EventReminder")
                            {
                                //MessagingCenter.Send(this, "Reminder......");
                                toastService.ShowInfo(notificationData.Title, 6000);
                            }
                        }
                        await InvokeAsync(StateHasChanged);
                    }
                    catch (Exception ex)
                    {
                        InvokeAsync(StateHasChanged);
                        logger.LogError(ex, ex.Message);
                        await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                    }
                };

                MessagingCenter.Subscribe<SettingsDialog, object[]>(this, Keywords.ThemeUpdated, async (sender, value) =>
                {
                    try
                    {
                        DesignThemeModes designThemeMode = Enum.Parse<DesignThemeModes>(value[0].ToString());
                        ThemeColor themeColor = Enum.Parse<ThemeColor>(value[1].ToString());

                        await SetTheme(designThemeMode, themeColor);
                        await InvokeAsync(StateHasChanged);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, ex.Message);
                        await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                    }
                });


#if ANDROID || IOS
                string deviceToken = devicePreferences.Get("DeviceToken", "");
                if(deviceToken!="")
                {
                    //UserDevicesWebApiClient userDevicesWebApiClient = new UserDevicesWebApiClient(httpClient, userDevicesWebApiClientLogger);
                    //await
                    await userDevicesWebApiClient.RegisterDeviceAsync(deviceToken, formFactor.GetPlatform());
                    //logger?.LogInformation("Device token registered successfully from MainActivity.");
                }
#endif
                //this.StateHasChanged();
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                this.errorBoundary?.Recover();

                if (firstRender)
                {
                    //Διαβάζουμε 2η φορά τα UserSettings στη περίπτωση που η OnAfterRenderAsync τρέξει πριν διαβαστουν τα δεδομένα στην OnInitializedAsync.
                    #region  Reads the UserSettings from cache or database
                    UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);
                    #endregion

                    //Αν είμαστε στο Web και υπάρχει ασυμφωνία στο language.
                    if (formFactor.GetDeviceIdiom() == "Web")
                    {
                        //Αν η γλώσσα στα userSetting διαφέρει από τη τρέχουσα.
                        if (this.userSetting!.Language.GetCultureInfo().Name != CultureInfo.CurrentCulture.Name)
                        {
                            //await localStorage.SetItemAsync<string>("TimeZone", userSetting!.TimeZone!);
                            await localStorage.SetItemAsync<string>("Language", this.userSetting!.Language.GetCultureInfo().Name.Contains("el") ? "el" : "en");

                            navManager.NavigateTo("/", forceLoad: true);
                            //Ο παρακάτω κώδικας δεν τρέχει γιατί το web θα κάνει refresh την σελίδα.
                        }
                    }

                    #region  Διαβάζει το Role του User από το cache και αν δεν το βρει από τη βάση δεδομένων
                    //var userRoleCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
                    this.userRole = await cache.GetOrCreateAsync(
                        Keywords.UserRole,
                        async cancel => await usersWebApiClient.GetUserRole(AuthenticatedUserData.UserId),
                        userRoleCacheOptions,
                        new[] { "Deletable" });
                    #endregion

                    #region  Αν είναι η πρώτη φορά που συνδέεται ο User, τότε ορισμένα πεδία του UserSetting δεν έχουν τιμή και πρέπει να οριστούν.
                    if (this.userSetting!.TimeZone == "")
                    {
                        //Αν είναι η ελληνική γλώσσα.
                        if (CultureInfo.CurrentCulture.Name.Contains("el"))
                        {
                            this.userSetting!.TimeFormat = "HH:mm";
                            this.userSetting!.DateFormat = "dd/MM/yyyy";
                        }
                        else  //Αν είναι αγγλική γλώσσα.
                        {
                            this.userSetting!.TimeFormat = "HH:mm";
                            this.userSetting!.DateFormat = "dd/MM/yyyy";
                        }

                        //Pass initial UI settings (language, TimeZone etc) to UserSettings and saves it to database.
                        if (formFactor.GetDeviceIdiom() == "Phone" || formFactor.GetDeviceIdiom() == "Desktop")
                        {
                            //Η μεταβλητή devicePreferences θα είναι null αν δεν είμαστε σε device.
                            this.userSetting!.TimeZone = devicePreferences.Get("TimeZone", "");
                            this.userSetting!.Language = (Language)Enum.Parse<Language>(devicePreferences.Get("Language", ""));
                        }
                        else  //Αν είμαστε στο web.
                        {
                            this.userSetting!.TimeZone = await localStorage.GetItemAsync<string>("TimeZone");
                            this.userSetting!.Language = (Language)Enum.Parse<Language>(await localStorage.GetItemAsync<string>("Language"));
                        }
                        this.userSetting!.ObjectState = ObjectState.Modified;

                        SettingsWebApiClient settingsWebApiClient = new SettingsWebApiClient(httpClient, settingsWebApiClientLogger);
                        await settingsWebApiClient.CreateOrUpdateUserSettings(this.userSetting);
                    }
                    #endregion

                    Thread.CurrentThread.CurrentCulture = this.userSetting!.Language.GetCultureInfo();
                    Thread.CurrentThread.CurrentUICulture = Thread.CurrentThread.CurrentCulture;
                    CultureInfo.DefaultThreadCurrentCulture = Thread.CurrentThread.CurrentCulture;
                    CultureInfo.DefaultThreadCurrentUICulture = Thread.CurrentThread.CurrentCulture;

                    CultureInfo.CurrentCulture.DateTimeFormat.ShortTimePattern = this.userSetting!.TimeFormat;
                    CultureInfo.CurrentCulture.DateTimeFormat.LongTimePattern = this.userSetting!.TimeFormat;
                    CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern = this.userSetting!.DateFormat;
                    CultureInfo.CurrentUICulture.DateTimeFormat.ShortTimePattern = this.userSetting!.TimeFormat;
                    CultureInfo.CurrentUICulture.DateTimeFormat.LongTimePattern = this.userSetting!.TimeFormat;
                    CultureInfo.CurrentUICulture.DateTimeFormat.ShortDatePattern = this.userSetting!.DateFormat;




                    //Τρέχουμε το Task χωρίς await για να μην περιμένει ο κώδικας, όμως το SignalR να ξεκινήσει με μια καθυστέρηση.
                    _ = Task.Run(async () =>
                    {
                        //try
                        //{
                        //Εκτελούμε το SingalR NotificationService 15 sec αργότερα για να έχει προλάβει το WebApi να ξεκινήσει.
                        await Task.Delay(15000);
                        // TEMP DISABLE                        await signalRNotificationService.StartAsync(AuthenticatedUserData.TenantId.ToString(), AuthenticatedUserData.UserId.ToString());
                        //}
                        //catch (Exception ex)
                        //{
                        //    logger.LogError(ex, ex.Message);
                        //    new ErrorNotifier(dialogService).ShowError(ex.Message, "");
                        //}
                    });


                    //Τρέχουμε το Task χωρίς await για να μην περιμένει ο κώδικας.
                    _ = Task.Run(async () =>
                    {
                        //try
                        //{
                        //Καταχωρούμε τα TimeZones στη μνήμη για να φορτώνουν γρήγορα τα Settings.
                        //var timeZonesCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };

                        await cache.SetAsync(Keywords.TimeZones, FluentBlue.Shared.Utilities.TimeZone.AllTimeZones.Select(x => x.ZoneId).ToList(), timeZonesCacheOptions);
                        //}
                        //catch (Exception ex)
                        //{
                        //    logger.LogError(ex, ex.Message);
                        //    new ErrorNotifier(dialogService).ShowError(ex.Message, "");
                        //}
                    });


                    //Αν είμαστε σε device
                    if (formFactor.GetDeviceIdiom() == "Phone" || formFactor.GetDeviceIdiom() == "Desktop")
                    {
                        //Κάνει navigate σε όλες τις σελίδες ώστε να γίνει πραγματικό refresh και να εμφανιστεί η επιλεγμένη γλώσσα.
                        navManager.NavigateTo("/", forceLoad: false);
                        navManager.NavigateTo("/contacts", forceLoad: false);
                        navManager.NavigateTo("/calendar", forceLoad: false);
                        navManager.NavigateTo("/", forceLoad: false);
                    }
                }

            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public async Task Dispose()
        {
            try
            {
                webUpdateChecker.OnUpdateDetected -= OnWebUpdateDetected;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SetTheme(DesignThemeModes designThemeMode, ThemeColor themeColor)
        {
            if (designThemeMode != currentDesignThemeMode || themeColor.GetEnumDescription() != this.currentThemeColorText)
            {
                this.currentThemeColorText = themeColor.GetEnumDescription();
                this.currentDesignThemeMode = designThemeMode;

                if (designThemeMode == DesignThemeModes.Light)
                {
                    if (themeColor == ThemeColor.Default)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-default" });
                    }
                    else if (themeColor == ThemeColor.RedWine)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-redwine" });
                    }
                    else if (themeColor == ThemeColor.Pinewater)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-pinewater" });
                    }
                    else if (themeColor == ThemeColor.Nature)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-nature" });
                    }
                    else if (themeColor == ThemeColor.ElectricSky)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-electricsky" });
                    }
                    else if (themeColor == ThemeColor.DeepViolet)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-deepviolet" });
                    }
                    else if (themeColor == ThemeColor.PurpleRain)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-purplerain" });
                    }
                    else if (themeColor == ThemeColor.GolderSun)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-goldensun" });
                    }
                    else if (themeColor == ThemeColor.Rose)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-rose" });
                    }
                    else if (themeColor == ThemeColor.TropicalSea)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-tropicalsea" });
                    }
                    else if (themeColor == ThemeColor.SteelViolet)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-steelviolet" });
                    }
                    else if (themeColor == ThemeColor.SlateAzure)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-light-slateazure" });
                    }
                }
                else
                {
                    if (themeColor == ThemeColor.Default)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-default" });
                    }
                    else if (themeColor == ThemeColor.RedWine)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-redwine" });
                    }
                    else if (themeColor == ThemeColor.Pinewater)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-pinewater" });
                    }
                    else if (themeColor == ThemeColor.Nature)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-nature" });
                    }
                    else if (themeColor == ThemeColor.ElectricSky)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-electricsky" });
                    }
                    else if (themeColor == ThemeColor.DeepViolet)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-deepviolet" });
                    }
                    else if (themeColor == ThemeColor.PurpleRain)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-purplerain" });
                    }
                    else if (themeColor == ThemeColor.GolderSun)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-goldensun" });
                    }
                    else if (themeColor == ThemeColor.Rose)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-rose" });
                    }
                    else if (themeColor == ThemeColor.TropicalSea)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-tropicalsea" });
                    }
                    else if (themeColor == ThemeColor.SteelViolet)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-steelviolet" });
                    }
                    else if (themeColor == ThemeColor.SlateAzure)
                    {
                        await JS.InvokeAsync<string>("setTheme", new object?[] { "fluent2-dark-slateazure" });
                    }
                }
            }

            //this.StateHasChanged();
        }

        private async Task OnWebUpdateDetected(string newVersion)
        {
            try
            {
                int timeout = 30000;  //Timeout for toast to stay visible.

                ToastParameters<ProgressToastContent> reloadAppParams = new()
                {
                    Id = "ReloadAppToast",
                    Intent = ToastIntent.Upload,
                    Title = Resources.MainlLayoutResource.ReloadAppRequired,
                    Timeout = timeout,
                    Icon = (new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size16.ArrowSyncCircle(), Color.Accent),
                    PrimaryAction = Resources.MainlLayoutResource.ReloadAppNow,
                    OnPrimaryAction = EventCallback.Factory.Create<ToastResult>(this, ReloadAppNowClicked),
                    Content = new ProgressToastContent()
                    {
                        Details = Resources.MainlLayoutResource.ReloadAppMessage
                    },
                };

                toastService.ShowProgressToast(reloadAppParams); //Εμφανίζει το Progress Toast

                await Task.Delay(timeout).ContinueWith(async t => { await this.webUpdateChecker.ForceReloadAsync(); });  //Μετά από 30 δευτερόλεπτα κάνει αυτόματα reload, εκτός αν πατήσει ο χρήστης να γίνει reload.
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ReloadAppNowClicked(ToastResult result)
        {
            try
            {
                await this.webUpdateChecker.ForceReloadAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
              

        private async Task AccountOnClick()
        {
            try
            {
                UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                Data.Model.DBOs.Tenants.User? user = await usersWebApiClient.GetUser(AuthenticatedUserData.UserId);

                if (user != null)
                {
                    string dialogWidth = "900px", dialogHeight = "550px";
                    if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                    {
                        dialogWidth = "100%";
                        dialogHeight = "100%";
                    }

                    UserDialogInput userDialogInput = new UserDialogInput() { User = user, AccountMode = true };

                    //εμφανίζει το dialog
                    DialogParameters<UserDialogInput> parameters = new()
                    {
                        ShowTitle = true,
                        OnDialogResult = dialogService.CreateDialogCallback(this, OnUserDialogResult),
                        Title = UI.Main.Components.Resources.UserDialogResource.Title,
                        PrimaryAction = "",  //GlobalResource.Save,
                        SecondaryAction = "", //=GlobalResource.Cancel,
                        Width = dialogWidth,
                        Height = dialogHeight,
                        TrapFocus = false,
                        Modal = true,
                        PreventScroll = true,
                        PreventDismissOnOverlayClick = true,
                        ShowDismiss = false,
                        Alignment = HorizontalAlignment.Center
                    };
                    dialog = await dialogService.ShowDialogAsync<UI.Main.Components.UserDialog>(userDialogInput, parameters);
                    DialogResult result = await dialog.Result;
                    //return result;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                //return DialogResult.Cancel();
            }
        }

        private async Task OnUserDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false)
                {
                }
                if (dialog != null)
                {
                    await dialog.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task LogoutOnClick()
        {
            try
            {
                await this.loginService.Logout();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SettingsOnClick()
        {
            try
            {
                try
                {
                    UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    UserSetting? userSetting = await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);

                    string dialogWidth = "95%", dialogHeight = "90%";
                    if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm" || ScreenSizeTracker.CurrentBreakpoint == "md")
                    {
                        dialogWidth = "100%";
                        dialogHeight = "100%";
                    }

                    //εμφανίζει το dialog
                    DialogParameters parameters = new()
                    {
                        ShowTitle = false,
                        OnDialogResult = dialogService.CreateDialogCallback(this, OnSettingsDialogResult),
                        Title = GlobalResource.Settings,
                        PrimaryAction = "",  //GlobalResource.Save,
                        SecondaryAction = "", //=GlobalResource.Cancel,
                        Width = dialogWidth,
                        Height = dialogHeight,
                        TrapFocus = false,
                        Modal = true,
                        DialogBodyStyle = "",
                        //PreventScroll = true,
                        PreventDismissOnOverlayClick = true,
                        ShowDismiss = false,
                        Alignment = HorizontalAlignment.Center
                    };
                    dialog = await dialogService.ShowDialogAsync<UI.Main.Components.SettingsDialog>(userSetting!, parameters);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, ex.Message);
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnSettingsDialogResult(DialogResult result)
        {
            try
            {
                await dialog.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.

                #region  Reads the UserSettings from cache or database
                UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
                this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                    async cancel =>
                    {
                        UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                        return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                    }, userSettingCacheOptions);
                #endregion

                await SetTheme(ConvertThemeColorModeToThemeMode(this.userSetting!.ThemeColorMode), this.userSetting.ThemeColor);

                this.StateHasChanged();
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private DesignThemeModes ConvertThemeColorModeToThemeMode(ThemeColorMode colorMode)
        {
            if (colorMode == ThemeColorMode.Light)
            {
                return DesignThemeModes.Light;
            }
            else if (colorMode == ThemeColorMode.Dark)
            {
                return DesignThemeModes.Dark;
            }
            //else if (colorMode == ThemeColorMode.System)
            //{
            //    return DesignThemeModes.System;
            //}
            else
            {
                return DesignThemeModes.System;  //Δεν θα τρέξει ποτέ.
            }
        }

        //protected override bool ShouldRender()
        //{
        //    bool tempShouldRender = this.shouldRender;
        //    this.shouldRender = false;
        //    return tempShouldRender;
        //}

        //protected void StateHasChangedOptimized()
        //{
        //    this.shouldRender = true;
        //    StateHasChanged();
        //}

        private void OnBreakpointChanged(string breakpoint)
        {
            
        }

        private async Task NewEvent()
        {
            try
            {
                DateTime startTime = DateTime.Now.AddHours(1);
                startTime = new DateTime(startTime.Year, startTime.Month, startTime.Day, startTime.Hour, 0, 0);
                DateTime endTime = startTime.AddHours(1);

                Data.Model.DBOs.Calendar.Event eventObj = Data.Model.DBOs.Calendar.Event.CreateEvent(this.userSetting!.TimeZone, startTime, startTime.AddHours(1), AuthenticatedUserData.TenantId, null);  //TODO: Κανονικά εδώ πρέπει να βάζει διάρκεια με βάση τα settings ή το timescale του ημερολογίου.
                //eventObj.UserTimeZoneId = this.userSetting!.TimeZone;

                await this.ShowEvent(eventObj);
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ShowEvent(Data.Model.DBOs.Calendar.Event evnt)
        {
            try
            {
                string dialogWidth = "1000px", dialogHeight = "90%";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm" || ScreenSizeTracker.CurrentBreakpoint == "md")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                //εμφανίζει το dialog
                DialogParameters<FluentBlue.UI.Main.Shared.EventDialogInput> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnEventDialogResult),
                    Title = UI.Main.Pages.Resources.CalendarResource.Event,
                    PrimaryAction = "",  //GlobalResource.Save,
                    SecondaryAction = "", //=GlobalResource.Cancel,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    //PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                EventDialogInput eventDialogInput = new EventDialogInput() { Event = evnt, RecurrentEventHandlingType = WebApi.Shared.Request.RecurrentEventHandlingType.Current };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.EventDialog>(eventDialogInput, parameters);
                DialogResult? result = await dialog.Result;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnEventDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false)
                {
                    MessagingCenter.Send(this, Keywords.ReloadEvents);
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }



        private async Task NewContact()
        {
            try
            {
                Contact contact = Contact.CreateContact(AuthenticatedUserData.TenantId);
                contact.UserTimeZoneId = this.userSetting!.TimeZone;
                await this.ShowContact(contact);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ShowContact(Data.Model.DBOs.Contacts.Contact contact)
        {
            try
            {
                string dialogWidth = "1100px", dialogHeight = "90%";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm" || ScreenSizeTracker.CurrentBreakpoint == "md")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                //εμφανίζει το dialog
                DialogParameters<FluentBlue.UI.Main.Shared.ContactDialogInput> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnContactDialogResult),
                    Title = UI.Main.Components.Resources.ContactDialogResource.Title,
                    PrimaryAction = "",  //GlobalResource.Save,
                    SecondaryAction = "", //=GlobalResource.Cancel,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                ContactDialogInput contactDialogInput = new ContactDialogInput() { Contact = contact, Restricted = false };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.ContactDialog>(contactDialogInput, parameters);
                DialogResult? result = await dialog.Result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnContactDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false)
                {
                    MessagingCenter.Send(this, Keywords.ReloadContacts);
                }
                if (dialog != null)
                {
                    await dialog.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        // Helper method to clear user cache data
        private async Task ClearCacheData()
        {
            // Clear specific entries by key
            //await cache.RemoveAsync(Keywords.UserRole);
            //await cache.RemoveAsync(Keywords.EventStates);
            //await cache.RemoveAsync(Keywords.EventCategories);
            //await cache.RemoveAsync(Keywords.ContactCategories);
            //await cache.RemoveAsync(Keywords.Users);
            //await cache.RemoveAsync(Keywords.LastSelectedCalendarDisplayUsers);
            await cache.RemoveByTagAsync("Deletable");

            // We could also remove by tag if many items share the same tag
            // await cache.RemoveByTagAsync("UserData");
        }
    }
}