﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.UI.Main.Components;
using FluentBlue.WebApi.Shared.Request;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor.Popups;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Shared
{
    public class ErrorNotifier
    {
        private readonly IDialogService dialogService;
        private static readonly SemaphoreSlim _dialogSemaphore = new(1, 1);

        public ErrorNotifier(IDialogService dialogService)
        {
            this.dialogService = dialogService;
        }

        public async Task ShowErrorAsync(string message, string title)
        {
            if (!await _dialogSemaphore.WaitAsync(TimeSpan.Zero))
            {
                return;
            }

            try
            {
                await dialogService.ShowErrorAsync(message, title).ContinueWith((dialog) => { _dialogSemaphore.Release(); });
            }
            finally
            {
                //_dialogSemaphore.Release();
            }
        }

        private void OnDialogResult(DialogResult result)
        {
        }

        public void ShowInfo(string message, string title)
        {
            this.dialogService.ShowInfo(message, title);
        }
    }
}
