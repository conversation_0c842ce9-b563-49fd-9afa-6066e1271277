{"FluentBlueConnectionString": "Server=MAIN\\SQLEXPRESS;Database=FluentBlue;Trusted_Connection=True;TrustServerCertificate=True", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "WebApi": {"BaseUrl": "http://localhost:5122/"}, "Encryption": {"ApplicationKey": "Fu5gf0JiwhqinJeBTl8fJu8rcqhVUR/SHmiNfXGtdNk="}, "AllowedHosts": "*", "jwt": {"key": "aasdfkhascahfcasdklfhacnlsdkjfhalkjhaslfjhasdkfahsldfkjhasdf"}, "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console", "Sentry.Serilog"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "./logs/log-.txt", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.JsonFormatter", "rollingInterval": "Day", "retainedFileCountLimit": "30", "fileSizeLimitBytes": "1000000"}}], "Enrich": ["WithExceptionDetails", "WithMachineName", "WithEnvironmentName"]}}